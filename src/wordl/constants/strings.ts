export const GAME_TITLE = "Adivina la palabra secreta";

export const WIN_MESSAGES = ["¡Buen trabajo!", "Impresionante", "¡Bien hecho!"];
export const GAME_COPIED_MESSAGE = "Juego copiado al portapapeles";
export const NOT_ENOUGH_LETTERS_MESSAGE = "No hay suficientes letras";
export const WORD_NOT_FOUND_MESSAGE = "Palabra no encontrada";
export const CORRECT_WORD_MESSAGE = (solution: string) =>
  `La palabra era ${solution}`;
export const WRONG_SPOT_MESSAGE = (guess: string, position: number) =>
  `Debe usar ${guess} en la posición ${position}`;
export const NOT_CONTAINED_MESSAGE = (letter: string) =>
  `Supongo que debe contener ${letter}`;
export const ENTER_TEXT = "Ingresar";
export const DELETE_TEXT = "Borrar";
export const STATISTICS_TITLE = "Estadísticas";
export const GUESS_DISTRIBUTION_TEXT = "Distribución de conjeturas";
export const SHARE_TEXT = "Compartir";
export const SHARE_FAILURE_TEXT =
  "No se pueden compartir los resultados. Esta función está disponible solo en contextos seguros (HTTPS), en algunos o en todos los navegadores compatibles.";
export const TOTAL_TRIES_TEXT = "Intentos totales";
export const SUCCESS_RATE_TEXT = "Tasa de éxito";
export const CURRENT_STREAK_TEXT = "Racha actual";
export const BEST_STREAK_TEXT = "Mejor racha";
