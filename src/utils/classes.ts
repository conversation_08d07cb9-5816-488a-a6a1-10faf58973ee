import {
  CartType,
  ClassesPricingType,
  ClubType,
  CustomProductType,
  EventOnType,
  EventSchemaType,
  EventsPriceDetailsType,
  TransactionType,
  UserType,
} from "@/api/mongoTypes";
import {
  CLASSES_SORT,
  CLASSES_TYPE,
  CLASSESS_FETCH_DURATION,
  CURRENCY_ENUM,
  DURATION_TYPE,
  EVENT_MODE_TYPE,
  IN_PERSON_TYPE,
  LEVEL_TYPES,
  ONLINE_CLASSES_TYPE,
  PLAN_FOR,
} from "@/constant/Enums";
import { NextRouter } from "next/router";
import {
  getPlansTitle,
  getPricingTitle,
  getSubTypeName,
  getTypeName,
} from "./format";
import {
  EventSchemaWithSingleEvent,
  localPlanType,
  Maybe,
  MaybeCartSchedule,
  MaybeObjectId,
  StudentScheduleInfo,
} from "@/types";
import {
  startOfWeek,
  addDays,
  startOfMonth,
  endOfMonth,
  isSameDay,
  format,
} from "date-fns";
import {
  convertToTime,
  dateAsPerTimeZone,
  formatDate,
  formatTime,
  getDateAsPerUTC,
} from "./dateTime";
import { getProficiencyEn } from "./common";
import { LEARN_COOKIE_NAME } from "@/constant/classes";
import Cookies from "js-cookie";

const getPlanFor = (plan: PLAN_FOR) => {
  if (plan === PLAN_FOR.MYSELF) {
    return "Myself";
  }
  return "Someone";
};

const getLocalBuyEventDetails = () => {
  try {
    const buy = localStorage.getItem("BUY_EVENT");
    if (buy) {
      const parsed = JSON.parse(buy);
      return parsed;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
};
const getLocalBuyClubDetails = () => {
  try {
    const buy = localStorage.getItem("BUY_CLUB");
    if (buy) {
      const parsed = JSON.parse(buy);
      return parsed;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
};

const getLocalBuyClassDetaills = () => {
  try {
    const buy = localStorage.getItem("BUY_CLASS");
    if (buy) {
      const parsed = JSON.parse(buy);
      return parsed;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
};

const getLocalCarts = () => {
  try {
    const carts = localStorage.getItem("CART");
    if (carts) {
      const parsed = JSON.parse(carts);
      return parsed;
    } else {
      return [];
    }
  } catch (error) {
    return [];
  }
};

const handleReturnToCart = (router: NextRouter) => {
  try {
    const wasClassBuying = getLocalBuyClassDetaills();
    if (wasClassBuying?.uniqueId) {
      const type =
        wasClassBuying.type === CLASSES_TYPE.IN_PERSON
          ? "clases-lineares"
          : "online-clases";
      router.push(`/classes/${type}`);
      return;
    }

    const wasBuying = getLocalBuyEventDetails();
    if (wasBuying?._id) {
      router.push(`/classes/experiencias-libres/${wasBuying._id}`);
      return;
    }

    const wasClubBuying = getLocalBuyClubDetails();
    if (wasClubBuying?._id) {
      router.push(`/classes/online-clubs/${wasClubBuying._id}`);
      return;
    }

    const isCartFilled = (() => {
      const list = localStorage.getItem("CART");
      if (list) {
        return true;
      }
      return false;
    })();
    if (isCartFilled) {
      router.push("/cart");
    }
    router.push("/cart");
  } catch (error) {
    console.log("error", error);
  }
};

const needToRedirect = () => {
  const wasClassBuying = getLocalBuyClassDetaills();
  const wasBuying = getLocalBuyEventDetails();
  const wasClubBuying = getLocalBuyClubDetails();
  const localCart = getLocalCarts();

  const open =
    wasClassBuying?.uniqueId ||
    wasBuying?._id ||
    wasClubBuying?._id ||
    localCart?.length > 0;

  return open;
};

const AMOUNT_CONVERTOR = 100;

const getFinalAmount = (price) => {
  return +price / AMOUNT_CONVERTOR;
};

const getProductData = (data: CartType[]) => {
  if (data.length === 0) {
    return [];
  }
  const productList = [];
  data.map((m) => {
    const eventDetails = m.eventId;
    if (
      eventDetails &&
      typeof eventDetails === "object" &&
      "title" in eventDetails &&
      "price" in eventDetails
    ) {
      productList.push({
        name: eventDetails.title,
        price: eventDetails.price,
      });
      return;
    }

    const clubDetails = m.clubId;
    if (
      clubDetails &&
      typeof clubDetails === "object" &&
      "title" in clubDetails
    ) {
      m.memberships.map((j) => {
        productList.push({
          name: clubDetails.title,
          price: +clubDetails.price,
        });
      });
      return;
    }

    const classData = (() => {
      if (
        (m?.classesId && typeof m.classesId !== "object") ||
        !(m?.classesId && "plans" in m.classesId)
      ) {
        return null;
      }
      return m.classesId;
    })();
    const title = getPricingTitle({
      data: classData,
    });
    const classPlans = (() => {
      if (
        (m?.classesId && typeof m.classesId !== "object") ||
        !(m?.classesId && "plans" in m.classesId)
      ) {
        return [];
      }
      return m.classesId.plans;
    })();
    const boughtPlans = m?.plans ?? [];
    const totalPrice = boughtPlans.reduce((total, boughtPlan) => {
      const matchingPlan = classPlans.find(
        (plan) => String(plan.planId) === String(boughtPlan.planId)
      );
      return matchingPlan ? total + matchingPlan.price : total;
    }, 0);
    productList.push({
      name: title,
      price: totalPrice,
    });
    return;
  });
  return productList;
};

const getClubImage = (clubDetails: ClubType) => {
  try {
    if ("images" in clubDetails && clubDetails?.images) {
      return clubDetails.images[0].url;
    }
    return "";
  } catch (error) {
    return "";
  }
};

const getCustomProductInfo = (data: MaybeObjectId<CustomProductType>) => {
  if (data && data._id) {
    return data as CustomProductType;
  }
  return null;
};

const getItemsDetailsFromTransaction = (data: TransactionType) => {
  const items = [];
  try {
    if (!data) {
      return items;
    }
    const eventItems: EventSchemaType[] =
      data?.eventIds?.map((m) => m as EventSchemaType) ?? [];
    if (eventItems.length > 0) {
      eventItems.map((event) => {
        items.push({
          amount: event.price,
          title: event.title,
          type: "Community",
          currency: event.currency,
        });
      });
    }

    if (data.clubsDetails.length > 0) {
      data.clubsDetails.map((clubData) => {
        const memberships = clubData.memberships;
        const clubDetails = clubData.clubInfo as ClubType;
        if (memberships.length > 0) {
          memberships.map((mem) => {
            items.push({
              amount: clubDetails.price,
              title: clubDetails.title,
              type: "Club",
              currency: clubDetails.currency,
            });
          });
        } else {
          items.push({
            amount: clubDetails.price,
            title: clubDetails.title,
            type: "Club",
            currency: clubDetails.currency,
          });
        }
      });
    }

    if (data.classesDetails.length > 0) {
      data.classesDetails.map((classDetails) => {
        const plans = classDetails.plans;
        const classData = classDetails.classInfo as ClassesPricingType;
        if (plans.length > 0) {
          plans.map((plan) => {
            const planInfo = classData.plans.find(
              (f) => f.planId === plan.planId
            );
            const title = getPricingTitle({
              data: classData,
            });
            items.push({
              amount: planInfo.price,
              title,
              type: getTypeName(classData.type),
              currency: CURRENCY_ENUM.USD,
            });
          });
        }
      });
    }
    const customProductInfo = getCustomProductInfo(
      data?.customProductsDetails?.customProductsInfo
    );
    if (customProductInfo) {
      items.push({
        amount: data.customProductsDetails.price,
        title: customProductInfo.title,
        type: "Custom Product",
        currency: data.customProductsDetails.currency,
      });
    }
    return items;
  } catch (error) {
    console.error(
      "Something went wrong in getItemsDetailsFromTransaction due to ",
      error
    );
    return items;
  }
};

const getTypedClubInfo = (data: MaybeCartSchedule) => {
  if (!data) {
    return null;
  }
  if ("clubId" in data) {
    return data.clubId as ClubType;
  }
  if ("clubsDetails" in data && "clubInfo" in data.clubsDetails) {
    return data.clubsDetails.clubInfo as ClubType;
  }
  return null;
};

const getTypedEventOnDetails = (data: MaybeObjectId<EventOnType>) => {
  try {
    if (data && "_id" in data) {
      return data as EventOnType;
    }
    return null;
  } catch (error) {
    return null;
  }
};

const getTypedEventInfo = (data: MaybeCartSchedule) => {
  if ("eventsPriceDetails" in data) {
    const eventsPriceDetails =
      data?.eventsPriceDetails as EventsPriceDetailsType;
    const eventOnDetails = getTypedEventOnDetails(eventsPriceDetails.eventOn);
    return {
      ...data.eventsPriceDetails.eventInfo,
      startDateTime: eventOnDetails?.startDateTime,
      endDateTime: eventOnDetails?.endDateTime,
      timezone: eventOnDetails?.timezone,
    } as EventSchemaType;
  }
  if ("eventIds" in data) {
    return data.eventIds as EventSchemaType;
  }
  if ("eventId" in data) {
    return data.eventId as EventSchemaType;
  }
  if (data && "type" in data && data.type === "event" && "data" in data) {
    const eventInfo = data.data;
    return eventInfo as EventSchemaType;
  }
  return null;
};

const getTypedClassPricingInfo = (data: MaybeCartSchedule) => {
  if ("classesId" in data) {
    return data.classesId as ClassesPricingType;
  }
  if (
    data &&
    "type" in data &&
    "data" in data &&
    data.type === "schedule" &&
    "classInfo" in data.data
  ) {
    const classInfo = data.data.classInfo;
    return classInfo as ClassesPricingType;
  }
  if (data && "classesDetails" in data) {
    const classInfo = data?.classesDetails?.classInfo;
    return classInfo as ClassesPricingType;
  }
  return null;
};

const getTypedScheduledInfo = (data: MaybeCartSchedule) => {
  if (data && "type" in data && "data" in data && data.type === "schedule") {
    const scheduleInfo = data.data as StudentScheduleInfo;
    return scheduleInfo;
  }
  return null;
};

type getClassesImageProps = {
  purchaseDetails: MaybeCartSchedule;
};
const getClassesImage = ({ purchaseDetails }: getClassesImageProps) => {
  try {
    if (!purchaseDetails) {
      return "";
    }
    const clubDetails = getTypedClubInfo(purchaseDetails);
    const eventDetails = getTypedEventInfo(purchaseDetails);
    const classesPricingDetails = getTypedClassPricingInfo(purchaseDetails);

    if (eventDetails && eventDetails?.images?.length > 0) {
      return eventDetails?.images[0].url;
    }

    if (clubDetails && clubDetails?.images?.length > 0) {
      return clubDetails?.images[0].url;
    }

    if (classesPricingDetails.type === CLASSES_TYPE.IN_PERSON) {
      return "/images/classes/classesInPerson.webp";
    }
    if (classesPricingDetails.type === CLASSES_TYPE.ONLINE) {
      return "/images/classes/onlineClub.webp";
    }
    return "";
  } catch (error) {
    return "";
  }
};

type getClassTitleAndSubtitleProps = {
  cartData: MaybeCartSchedule;
};
const getClassTitleAndSubtitle = ({
  cartData,
}: getClassTitleAndSubtitleProps) => {
  try {
    if (cartData) {
      const clubDetails = getTypedClubInfo(cartData);
      const eventDetails = getTypedEventInfo(cartData);
      const classesPricingDetails = getTypedClassPricingInfo(cartData);

      if (eventDetails?._id) {
        return {
          title: eventDetails.title,
          subTitle: "Community Experiences",
          description: eventDetails.description,
        };
      }

      if (clubDetails?._id) {
        return {
          title: clubDetails.title,
          subTitle: "Online Club",
          description: clubDetails.about,
        };
      }

      if (classesPricingDetails?.type) {
        return {
          title:
            classesPricingDetails.type === CLASSES_TYPE.IN_PERSON
              ? "In person classes"
              : "Online Classes",
          subTitle: getSubTypeName(classesPricingDetails.subType),
          description: classesPricingDetails.description,
        };
      }
    }
    return {
      title: "",
      subTitle: "",
      description: "",
    };
  } catch (error) {
    return {
      title: "",
      subTitle: "",
      description: "",
    };
  }
};

type getClassTypeAndSubTypeProps = {
  cartData: MaybeCartSchedule;
};
const getClassTypeAndSubType = ({ cartData }: getClassTypeAndSubTypeProps) => {
  try {
    if (cartData) {
      const clubDetails = getTypedClubInfo(cartData);
      const eventDetails = getTypedEventInfo(cartData);
      const classesPricingDetails = getTypedClassPricingInfo(cartData);

      if (eventDetails?._id) {
        return {
          type: "Community Classes",
          subType: "",
        };
      }

      if (clubDetails?._id) {
        return {
          type: "Online Club",
          subType: "",
        };
      }

      if (classesPricingDetails.type) {
        return {
          type:
            classesPricingDetails.type === CLASSES_TYPE.IN_PERSON
              ? "In person classes"
              : "Online Classes",
          subType: getSubTypeName(classesPricingDetails.subType),
        };
      }
    }
    return {
      type: "",
      subType: "",
    };
  } catch (error) {
    return {
      type: "",
      subType: "",
    };
  }
};

type getCurrentPlanProps = {
  cartData: MaybeCartSchedule;
};
const getCurrentPlan = ({ cartData }: getCurrentPlanProps) => {
  if ("currentPlan" in cartData) {
    return cartData.currentPlan as localPlanType;
  }
  if (
    cartData &&
    "classesDetails" in cartData &&
    "plans" in cartData.classesDetails
  ) {
    return cartData.classesDetails.plans;
  }
  return null;
};

const getTargetDate = (): Date => {
  const currentDate = new Date();
  const dayOfWeek = currentDate.getDay();

  if (dayOfWeek >= 1 && dayOfWeek <= 3) {
    const nextDay = new Date(currentDate);
    nextDay.setDate(currentDate.getDate() + 1);
    return nextDay;
  }

  if (dayOfWeek === 0 || (dayOfWeek >= 4 && dayOfWeek <= 6)) {
    const monday = new Date(currentDate);
    const daysToMonday = dayOfWeek === 0 ? 1 : 8 - dayOfWeek;
    monday.setDate(currentDate.getDate() + daysToMonday);
    return monday;
  }
  return new Date(currentDate);
};

function getTomorrow(): Date {
  const currentDate = new Date();
  const tomorrow = new Date(currentDate);
  tomorrow.setDate(currentDate.getDate() + 1);
  return tomorrow;
}

type getStartAndEndDateOfClassesProps = {
  cartData: MaybeCartSchedule;
  isDashboard?: boolean;
  active?: CLASSESS_FETCH_DURATION;
};
const getStartAndEndDateOfClasses = ({
  cartData,
  isDashboard = false,
  active,
}: getStartAndEndDateOfClassesProps) => {
  const defaultValues = {
    startDate: null,
    endDate: null,
    timezone: null,
  };
  try {
    if (cartData) {
      const clubDetails = getTypedClubInfo(cartData);
      const eventDetails = getTypedEventInfo(cartData);
      const classesDetails = getTypedClassPricingInfo(cartData);
      const currentPlan = getCurrentPlan({ cartData });

      if (
        cartData &&
        "customProductsDetails" in cartData &&
        "customProductsInfo" in cartData.customProductsDetails
      ) {
        const customProductInfo = getCustomProductInfo(
          cartData?.customProductsDetails?.customProductsInfo
        );
        if (customProductInfo) {
          return {
            startDate: customProductInfo.startDate,
            endDate: customProductInfo.expirationDate,
            timezone: customProductInfo.timezone,
          };
        }
      }

      if (classesDetails) {
        if (isDashboard) {
          if (active === CLASSESS_FETCH_DURATION.CURRENT) {
            return {
              startDate: new Date(),
              endDate: "",
              timezone: null,
            };
          }
          if (active === CLASSESS_FETCH_DURATION.UPCOMING) {
            return {
              startDate: getTargetDate(),
              endDate: "",
              timezone: null,
            };
          }
        }
      }

      if (currentPlan && currentPlan.isDateEnabled) {
        defaultValues.startDate = currentPlan.startDate;
        defaultValues.endDate = currentPlan.endDate;
        return defaultValues;
      }

      if (eventDetails && eventDetails._id) {
        if (isDashboard) {
          if (active === CLASSESS_FETCH_DURATION.CURRENT) {
            return {
              startDate: new Date(),
              endDate: "",
              timezone: null,
            };
          }
          if (active === CLASSESS_FETCH_DURATION.UPCOMING) {
            return {
              startDate: getTomorrow(),
              endDate: "",
              timezone: null,
            };
          }
        }
        defaultValues.startDate = eventDetails.startDateTime;
        defaultValues.endDate = eventDetails.endDateTime;
        defaultValues.timezone = eventDetails.timezone;
        return defaultValues;
      }
      if (clubDetails && clubDetails._id) {
        return defaultValues;
      }
    }
    return defaultValues;
  } catch (error) {
    return defaultValues;
  }
};

type getClassTimingsProps = {
  data: MaybeCartSchedule;
};
const getClassTimings = ({ data }: getClassTimingsProps) => {
  try {
    if (!data) {
      return "";
    }
    const clubDetails = getTypedClubInfo(data);
    const eventDetails = getTypedEventInfo(data);
    const classesDetails = getTypedClassPricingInfo(data);
    const scheduledInfo = getTypedScheduledInfo(data);

    console.log("eventDetails", eventDetails);

    if (
      classesDetails &&
      classesDetails.type === CLASSES_TYPE.IN_PERSON &&
      classesDetails.subType === IN_PERSON_TYPE.GROUP
    ) {
      return "10:00 am - 1:00 pm";
    }

    if (scheduledInfo?._id) {
      if (
        scheduledInfo.type === CLASSES_TYPE.IN_PERSON &&
        scheduledInfo.subType === IN_PERSON_TYPE.GROUP
      ) {
        return "10:00 am - 1:00 pm";
      }
    }
    if (eventDetails?._id) {
      const fStartTime = convertToTime(eventDetails.startTime);
      const fEndTime = convertToTime(eventDetails.endTime);

      const startTime = formatTime({
        date: fStartTime,
        timezone: eventDetails.timezone,
      });
      const endTime = formatTime({
        date: fEndTime,
        timezone: eventDetails.timezone,
      });

      return `${startTime} - ${endTime}`;
    }
    return "";
  } catch (error) {
    return "";
  }
};

type getCreatorDetailsProps = {
  cartData: MaybeCartSchedule;
};
const getCreatorDetails = ({ cartData }: getCreatorDetailsProps) => {
  try {
    const clubDetails = getTypedClubInfo(cartData);

    if (clubDetails?._id && clubDetails?.teachers?.length > 0) {
      return clubDetails?.teachers as UserType[];
    }
    return [];
  } catch (error) {
    return [];
  }
};

type getTeachersProps = {
  data: MaybeCartSchedule;
};
const getTeachers = ({ data }: getTeachersProps) => {
  try {
    const scheduledInfo = getTypedScheduledInfo(data);
    if (scheduledInfo) {
      const studentLevel = scheduledInfo.student.level;
      const teacher = scheduledInfo.teachers.find(
        (f) => f.level === studentLevel
      );
      const teachers = scheduledInfo.teacherDetails.filter(
        (f) => f._id === teacher.teacherId
      );
      return teachers;
    }
    console.log("scheduledInfo", scheduledInfo);
    console.log("data", data);
    return [];
  } catch (error) {
    return [];
  }
};

type getClassesLevelProps = {
  cartData: MaybeCartSchedule;
};
const getClassesLevel = ({ cartData }: getClassesLevelProps) => {
  try {
    if (!cartData) {
      return "";
    }

    const clubDetails = getTypedClubInfo(cartData);
    const eventDetails = getTypedEventInfo(cartData);
    const classesDetails = getTypedClassPricingInfo(cartData);
    const scheduledInfo = getTypedScheduledInfo(cartData);

    if (scheduledInfo?._id) {
      const level = scheduledInfo?.student?.level;
      if (level) {
        return level;
      }
    }
  } catch (error) {
    return "";
  }
};

type getProficiencyLevelProps = {
  data: MaybeCartSchedule;
};
const getProficiencyLevel = ({ data }: getProficiencyLevelProps) => {
  try {
    if (!data) {
      return "";
    }
    const clubDetails = getTypedClubInfo(data);
    const eventDetails = getTypedEventInfo(data);

    let level = null;
    if (clubDetails && clubDetails._id && clubDetails.proficiencyLevel) {
      level = clubDetails.proficiencyLevel;
    }
    if (eventDetails && eventDetails._id && eventDetails.proficiencyLevel) {
      level = eventDetails.proficiencyLevel;
    }

    const inEnglish = getProficiencyEn({
      data: level,
    });

    if (inEnglish) {
      return inEnglish;
    }
    return "";
  } catch (error) {
    return "";
  }
};

type getClassesLocationProps = {
  cartData: MaybeCartSchedule;
};
const getClassesLocation = ({ cartData }: getClassesLocationProps) => {
  try {
    const eventDetails = getTypedEventInfo(cartData);
    const scheduledInfo = getTypedScheduledInfo(cartData);

    if (eventDetails && eventDetails._id) {
      if (eventDetails.mode === EVENT_MODE_TYPE.ONLINE) {
        return "Online";
      }
      return eventDetails.location;
    }
    if (scheduledInfo) {
      const studentLevel = scheduledInfo.student.level;
      const teacherDetail = scheduledInfo.teachers.find(
        (f) => f.level === studentLevel
      );
      if (teacherDetail.location) {
        return teacherDetail.location;
      }
    }

    return "";
  } catch (error) {
    return "";
  }
};

type classAndEventModeAndLocationProps = {
  data: MaybeCartSchedule;
};
const classAndEventModeAndLocation = ({
  data,
}: classAndEventModeAndLocationProps) => {
  const defaultValues = {
    isOnline: false,
    location: "",
  };
  try {
    const location = getClassesLocation({ cartData: data });
    defaultValues.location = location;
    const eventDetails = getTypedEventInfo(data);
    const scheduledInfo = getTypedScheduledInfo(data);

    if (eventDetails?._id) {
      defaultValues.isOnline = eventDetails.mode === EVENT_MODE_TYPE.ONLINE;
      return defaultValues;
    }

    if (scheduledInfo._id) {
      defaultValues.isOnline = scheduledInfo.type === CLASSES_TYPE.ONLINE;
      return defaultValues;
    }

    return defaultValues;
  } catch (error) {
    return defaultValues;
  }
};

type getAccessRestrictedInfoProps = {
  data: MaybeCartSchedule;
};
const getAccessRestrictedInfo = ({ data }: getAccessRestrictedInfoProps) => {
  const defaultValues = {
    title: "",
    description: "",
    button: "",
  };

  try {
    if (!data) {
      return defaultValues;
    }

    // we need to know if its class/event or club
    const clubDetails = getTypedClubInfo(data);
    const eventDetails = getTypedEventInfo(data);
    const classesDetails = getTypedClassPricingInfo(data);

    if (clubDetails?._id) {
      defaultValues.title = "Ready to join the club?";
      defaultValues.description = "Purchase a plan to jump in and learn!";
      defaultValues.button = "Explore Plans";
      return defaultValues;
    }
    if (eventDetails?._id) {
      defaultValues.title = "Ready to join the event?";
      defaultValues.description =
        "Grab a plan now to attend this exciting event.";
      defaultValues.button = "Explore Plans";
      return defaultValues;
    }
    if (classesDetails?.type) {
      defaultValues.title = "Ready to join the class?";
      defaultValues.description = "Purchase a plan to jump in and learn!";
      defaultValues.button = "Explore Plans";
      return defaultValues;
    }
  } catch (error) {
    return defaultValues;
  }
};

type getStatusBasedOnSelectedDateProps = {
  selectedDate: Date;
};
const getStatusBasedOnSelectedDate = ({
  selectedDate,
}: getStatusBasedOnSelectedDateProps) => {
  const currentDate = new Date();
  const formattedSelectedDate = new Date(selectedDate);

  const isSelectedToday =
    formatDate({ date: currentDate }) ===
    formatDate({ date: formattedSelectedDate });

  if (selectedDate) {
    if (isSelectedToday) {
      return CLASSESS_FETCH_DURATION.CURRENT;
    }
    if (currentDate < formattedSelectedDate) {
      return CLASSESS_FETCH_DURATION.UPCOMING;
    }
    if (currentDate >= formattedSelectedDate) {
      return CLASSESS_FETCH_DURATION.PAST;
    }
  }
};

const isInPersonGroupClassLive = (currentDate: Date) => {
  const hours = currentDate.getHours();
  const isIt = hours >= 10 && hours < 13;
  console.log({
    isIt,
    hours,
    currentDate,
    start: 10,
    end: 13,
  });
  return isIt;
};

type getcardStatusProps = {
  data: MaybeCartSchedule;
  selectedDate: Maybe<Date>;
  cardDate: Maybe<Date>;
  active: CLASSESS_FETCH_DURATION;
};
const getClassStatus = ({
  data,
  selectedDate,
  active,
  cardDate,
}: getcardStatusProps) => {
  try {
    const clubDetails = getTypedClubInfo(data);
    const eventDetails = getTypedEventInfo(data);
    const classesDetails = getTypedClassPricingInfo(data);
    const scheduledInfo = getTypedScheduledInfo(data);
    const todaysDate = new Date();

    if (cardDate) {
      const formattedCardDate = new Date(cardDate);
      const isSame = isSameDay(todaysDate, formattedCardDate);

      if (!isSame) {
        if (formattedCardDate > todaysDate) {
          return CLASSESS_FETCH_DURATION.UPCOMING;
        }
        if (formattedCardDate < todaysDate) {
          return CLASSESS_FETCH_DURATION.PAST;
        }
      } else {
        if (scheduledInfo) {
          const isLive = isInPersonGroupClassLive(formattedCardDate);
          return isLive
            ? CLASSESS_FETCH_DURATION.CURRENT
            : CLASSESS_FETCH_DURATION.PAST;
        } else {
          return CLASSESS_FETCH_DURATION.CURRENT;
        }
      }
    }

    const formattedSelectedDate = (() => {
      if (!selectedDate) {
        return null;
      }
      return new Date(selectedDate);
    })();

    const logicalDate = formattedSelectedDate ?? todaysDate;

    if (formattedSelectedDate) {
      const isSame = isSameDay(todaysDate, formattedSelectedDate);

      if (!isSame) {
        if (formattedSelectedDate > todaysDate) {
          return CLASSESS_FETCH_DURATION.UPCOMING;
        }
        if (formattedSelectedDate < todaysDate) {
          return CLASSESS_FETCH_DURATION.PAST;
        }
      }
    }

    if (+active === CLASSESS_FETCH_DURATION.ALL) {
      if (clubDetails?.createdAt) {
        return "";
      }

      if (eventDetails?._id) {
        const startDate = new Date(eventDetails.startDateTime);
        const endDate = new Date(eventDetails.endDateTime);

        if (logicalDate < startDate) {
          return CLASSESS_FETCH_DURATION.UPCOMING;
        }
        if (logicalDate > endDate) {
          return CLASSESS_FETCH_DURATION.PAST;
        }
        return CLASSESS_FETCH_DURATION.CURRENT;
      }

      if (scheduledInfo?._id) {
        const startDate = new Date(scheduledInfo.startDate);
        const endDate = new Date(scheduledInfo.endDate);
        if (logicalDate < startDate) {
          return CLASSESS_FETCH_DURATION.UPCOMING;
        }
        if (logicalDate > endDate) {
          return CLASSESS_FETCH_DURATION.PAST;
        }
        return CLASSESS_FETCH_DURATION.CURRENT;
      }
      return "";
    } else {
      return active;
    }
  } catch (error) {
    return "";
  }
};

type getRedirectUrlProps = {
  cartData: MaybeCartSchedule;
};
const getRedirectUrl = ({ cartData }: getRedirectUrlProps) => {
  const defaultUrl = "/classes";
  try {
    if (!cartData) {
      return defaultUrl;
    }
    const clubDetails = getTypedClubInfo(cartData);
    const eventDetails = getTypedEventInfo(cartData);
    const classesPricingDetails = getTypedClassPricingInfo(cartData);

    if (clubDetails && clubDetails?._id) {
      return `/classes/online-clubs/${clubDetails._id}`;
    }
    if (eventDetails && eventDetails?._id) {
      return `/classes/experiencias-libres/${eventDetails._id}`;
    }
    if (classesPricingDetails?.type) {
      const type =
        classesPricingDetails.type === CLASSES_TYPE.IN_PERSON
          ? "clases-lineares"
          : "online-clases";
      return `/classes/${type}`;
    }
    return defaultUrl;
  } catch (error) {
    return defaultUrl;
  }
};

const getClassModalEventInfo = ({
  startDate,
  endDate,
  isEvent = false,
}: {
  startDate: Date;
  endDate: Date;
  isEvent: boolean;
}) => {
  const startDateTime = new Date(startDate);
  const endDateTime = new Date(endDate);
  const currentDate = new Date();
  const currentHour = currentDate.getHours();

  if (currentDate < startDateTime) {
    return {
      buttonText: "Attend now",
      show: true,
      status: "upcoming",
    };
  }

  if (currentDate > endDateTime) {
    return {
      buttonText: "Watch Recording",
      show: true,
      status: "ended",
    };
  }

  if (isEvent) {
    if (currentDate >= startDateTime && currentDate <= endDateTime) {
      return {
        buttonText: "Attend now",
        show: true,
        status: "live",
      };
    }
  } else {
    if (
      currentDate >= startDateTime &&
      currentDate <= endDateTime &&
      currentHour >= 10 &&
      currentHour < 13
    ) {
      return {
        buttonText: "Attend now",
        show: true,
        status: "live",
      };
    }
  }

  return {
    buttonText: "Watch Recording",
    show: true,
    status: "ended",
  };
};

type getLinkInfoProps = {
  data: MaybeCartSchedule;
};
const getLinkInfo = ({ data }: getLinkInfoProps) => {
  const defaultvalues = {
    show: false,
    buttonText: "",
    status: "",
    helperText: "",
    url: "",
  };
  try {
    if (!data) {
      return defaultvalues;
    }
    const clubDetails = getTypedClubInfo(data);
    const eventDetails = getTypedEventInfo(data);
    const classesDetails = getTypedClassPricingInfo(data);
    const scheduledInfo = getTypedScheduledInfo(data);

    if (eventDetails && eventDetails?._id) {
      if (eventDetails.mode === EVENT_MODE_TYPE.OFFLINE) {
        return {
          show: false,
          buttonText: "",
          status: "",
          helperText: "",
          url: "",
        };
      }
      const { buttonText, show, status } = getClassModalEventInfo({
        endDate: new Date(eventDetails.endDateTime),
        startDate: new Date(eventDetails.startDateTime),
        isEvent: true,
      });
      return {
        show,
        buttonText,
        url: eventDetails.url,
        status: status,
        helperText:
          status === "upcoming"
            ? "This event hasn’t started yet."
            : status === "ended"
            ? "This event has already ended."
            : "",
      };
    }

    if (clubDetails && clubDetails?._id) {
      return defaultvalues;
    }
    if (classesDetails?.type) {
      if (classesDetails.type === CLASSES_TYPE.IN_PERSON) {
        return defaultvalues;
      }
    }

    if (scheduledInfo?._id) {
      const { buttonText, show, status } = getClassModalEventInfo({
        endDate: new Date(scheduledInfo.endDate),
        startDate: new Date(scheduledInfo.startDate),
        isEvent: false,
      });
      return {
        show: status === "upcoming" ? false : show,
        // show,
        buttonText: buttonText,
        status: status,
        url: "",
        helperText:
          status === "upcoming"
            ? "This class hasn’t started yet."
            : status === "ended"
            ? "This class has already ended."
            : "",
      };
    }
    return defaultvalues;
  } catch (error) {
    return defaultvalues;
  }
};

type getClassessFetchTabsProps = {
  needPast: boolean;
  needCurrent: boolean;
  needUpcoming: boolean;
  needAll: boolean;
};
const getClassessFetchTabs = ({
  needPast,
  needCurrent,
  needUpcoming,
  needAll,
}: getClassessFetchTabsProps) => {
  const list = [];
  if (needCurrent) {
    list.push({
      id: CLASSESS_FETCH_DURATION.CURRENT,
      name: "dash.today",
    });
  }
  if (needUpcoming) {
    list.push({
      id: CLASSESS_FETCH_DURATION.UPCOMING,
      name: "dash.upcoming",
    });
  }
  if (needAll) {
    list.push({
      id: CLASSESS_FETCH_DURATION.ALL,
      name: "dash.all",
    });
  }
  if (needPast) {
    list.push({
      id: CLASSESS_FETCH_DURATION.PAST,
      name: "dash.past",
    });
  }
  return list;
};

type getClassessFiltersProps = {
  needInPerson: boolean;
  needOnline: boolean;
  needClub: boolean;
  needCommunityExperience: boolean;
};
const getClassessFilters = ({
  needInPerson = false,
  needOnline = false,
  needClub = false,
  needCommunityExperience = false,
}: getClassessFiltersProps) => {
  const list = [];

  if (needInPerson) {
    list.push({
      id: 1,
      value: CLASSES_SORT.IN_PERSON_CLASS,
      name: "In-Person Classes",
    });
  }

  if (needOnline) {
    list.push({
      id: 2,
      value: CLASSES_SORT.ONLINE_CLASS,
      name: "Online Classes",
    });
  }
  if (needClub) {
    list.push({ id: 3, value: CLASSES_SORT.ONLINE_CLUB, name: "Online Clubs" });
  }
  if (needCommunityExperience) {
    list.push({
      id: 4,
      value: CLASSES_SORT.COMMUNITY,
      name: "Community Experiences",
    });
  }

  return list;
};

const getLevelColor = (level) => {
  if (level === LEVEL_TYPES.ADVANCED) {
    return "#FF8C61";
  }
  if (level === LEVEL_TYPES.INTERMEDIATE) {
    return "#FED07C";
  }
  return "#B1F3B1";
};

const getLevelName = (level) => {
  if (level === LEVEL_TYPES.ADVANCED) {
    return "Advanced";
  }
  if (level === LEVEL_TYPES.INTERMEDIATE) {
    return "Intermediate";
  }
  return "Beginner";
};

const getMondayAndThursday = (inputDate) => {
  const monday = startOfWeek(inputDate, { weekStartsOn: 1 });
  const thursday = addDays(monday, 3);
  return { monday, thursday };
};

const getFirstAndLastDateOfMonth = (monthNumber) => {
  const sanitizedMonth = +monthNumber;
  const year = new Date().getFullYear();
  const date = new Date(year, sanitizedMonth - 1, 1);

  const firstDate = startOfMonth(date);
  const lastDate = endOfMonth(date);
  return {
    firstDate: getDateAsPerUTC(firstDate),
    lastDate: getDateAsPerUTC(lastDate),
  };
};

const getStudentWeekName = ({ data, selectedMonday, selectedThursday }) => {
  const currentPlan = data.currentPlan;
  const classInfo = data?.classInfo;
  const classInfoPlans = classInfo.plans ?? [];

  const boughtPlan = classInfoPlans.find(
    (f) => f.planId === currentPlan.planId
  );

  if (!currentPlan || !classInfo) {
    return {
      isWeek4: false,
      formattedText: "",
      weekNumber: 0,
    };
  }

  const text = getPlansTitle({
    duration: boughtPlan.duration,
    subType: classInfo.subType,
    type: classInfo.type,
  });
  const formattedText = (() => {
    const replacedText = text.replace(":", "");
    return `${replacedText} Plan`;
  })();

  if (boughtPlan.duration === 1) {
    return {
      isWeek4: false,
      formattedText,
      weekNumber: 0,
    };
  }

  const boughtPlanStartDate = new Date(currentPlan.startDate);
  const boughtPlanEndDate = new Date(currentPlan.endDate);

  const monday = new Date(selectedMonday);
  const thursday = new Date(selectedThursday);

  function getWeekNumber(planStartDate, selectedMonday) {
    const startDate = new Date(planStartDate);
    startDate.setHours(0, 0, 0, 0);

    const selectedDate = new Date(selectedMonday);
    selectedDate.setHours(0, 0, 0, 0);

    const timeDiff = selectedDate.getTime() - startDate.getTime();
    const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const weekNumber = Math.floor(daysDiff / 7) + 1;
    return Math.max(1, Math.min(4, weekNumber));
  }

  const weekNumber = getWeekNumber(boughtPlanStartDate, monday);

  return {
    isWeek4: true,
    formattedText,
    weekNumber,
  };
};

type getDiscountedPriceProps = {
  price: number;
  discount: number;
};
const getDiscountedPrice = ({ price, discount }: getDiscountedPriceProps) => {
  return (price * (1 - discount / 100)).toFixed(2);
};

type GetOriginalPriceProps = {
  discountedPrice: number;
  discount: number;
};

const getOriginalPrice = ({
  discountedPrice,
  discount,
}: GetOriginalPriceProps) => {
  return discountedPrice / (1 - discount / 100);
};

type getPriceSymbolProps = {
  currency: CURRENCY_ENUM;
};
const getPriceSymbol = ({ currency }: getPriceSymbolProps) => {
  if (currency === CURRENCY_ENUM.MXN) {
    return "MX$";
  }
  return "$";
};

type isEveryCurrencySameProps = {
  currencies: CURRENCY_ENUM[];
};
const checkIfEveryCurrencySame = ({ currencies }: isEveryCurrencySameProps) => {
  if (currencies.length === 1) {
    return true;
  }
  return currencies.every((currency) => currency === currencies[0]);
};

const getEventOnDetails = (eventsOn: EventOnType) => {
  const startDate = (() => {
    const dateAsPerEventTimezone = getDateAsPerUTC(
      dateAsPerTimeZone({
        dateToChange: eventsOn.startDateTime,
        timeZone: String(eventsOn.timezone),
      })
    );
    const dateAsPerUserTZ = getDateAsPerUTC(
      dateAsPerTimeZone({
        dateToChange: dateAsPerEventTimezone,
      })
    );
    return dateAsPerUserTZ;
  })();

  const endDate = (() => {
    const dateAsPerEventTimezone = getDateAsPerUTC(
      dateAsPerTimeZone({
        dateToChange: eventsOn.endDateTime,
        timeZone: String(eventsOn.timezone),
      })
    );
    const dateAsPerUserTZ = getDateAsPerUTC(
      dateAsPerTimeZone({
        dateToChange: dateAsPerEventTimezone,
      })
    );
    return dateAsPerUserTZ;
  })();

  const sartTime = (() => {
    const date = new Date(eventsOn.startDateTime);
    const timeWithAmPm = format(date, "hh:mm a");
    return timeWithAmPm;
  })();

  const endTime = (() => {
    const date = new Date(eventsOn.endDateTime);
    const timeWithAmPm = format(date, "hh:mm a");
    return timeWithAmPm;
  })();

  return {
    startDate,
    endDate,
    sartTime,
    endTime,
    isStartEndDateSame: isSameDay(startDate, endDate),
  };
};

const getRemainingSeats = (
  data: EventSchemaWithSingleEvent,
  eventsOn: EventOnType
) => {
  const maxAllowedSeat = data.maxNumberOfRegistrations ?? 0;
  const boughtCount = (() => {
    if (
      data &&
      "boughtCount" in data &&
      data?.boughtCount &&
      eventsOn._id in data?.boughtCount
    ) {
      return data.boughtCount[eventsOn._id];
    }
    return 0;
  })();
  const final = maxAllowedSeat - boughtCount;
  if (final >= 0) {
    return final;
  }
  return 0;
};

const getTargetLanguageFromCookieOrUser = ({ dbUser = null }) => {
  const lang = Cookies.get(LEARN_COOKIE_NAME);
  const isLoggedIn = !!dbUser;
  const nameInEnglish = dbUser?.languageOfInterest?.nameInEnglish;
  if (isLoggedIn && nameInEnglish) {
    return nameInEnglish;
  }
  return lang;
};

const getPricingTypeId = (type: string) => {
  if (type === "In Person") {
    return "ipc.in-person";
  }
  if (type === "Online") {
    return "ipc.online";
  }
};

const getSubTypeId = (type: string) => {
  if (type === "Group") {
    return "ipc.group";
  }
  if (type === "Private") {
    return "ipc.private";
  }
  if (type === "Long Session") {
    return "ipc.long-session";
  }
  if (type === "Quick Session") {
    return "ipc.quick-session";
  }
  if (type === "Regular Session") {
    return "ipc.regular-session";
  }
};

type getPricingCardTitleIdProps = {
  title: string;
  type: CLASSES_TYPE;
  durationType: DURATION_TYPE;
  subType: IN_PERSON_TYPE | ONLINE_CLASSES_TYPE;
};
const getPricingCardTitleId = ({
  title,
  type,
  durationType,
  subType,
}: getPricingCardTitleIdProps) => {
  if (
    title === "1 Week Plan" &&
    type === CLASSES_TYPE.IN_PERSON &&
    subType === IN_PERSON_TYPE.GROUP &&
    durationType === DURATION_TYPE.WEEK
  ) {
    return "pc.1-week-plan";
  }
  if (
    title === "4 Week Plan" &&
    type === CLASSES_TYPE.IN_PERSON &&
    subType === IN_PERSON_TYPE.GROUP &&
    durationType === DURATION_TYPE.WEEK
  ) {
    return "pc.4-week-plan";
  }
  if (
    title === "Single Hour Session" &&
    type === CLASSES_TYPE.IN_PERSON &&
    subType === IN_PERSON_TYPE.PRIVATE &&
    durationType === DURATION_TYPE.HOUR
  ) {
    return "pc.single-hour-session";
  }
  if (
    title === "10 - Hour Bundle" &&
    type === CLASSES_TYPE.IN_PERSON &&
    subType === IN_PERSON_TYPE.PRIVATE &&
    durationType === DURATION_TYPE.HOUR
  ) {
    return "pc.10-hour-bundle";
  }
  if (
    title === "20 - Hour Bundle" &&
    type === CLASSES_TYPE.IN_PERSON &&
    subType === IN_PERSON_TYPE.PRIVATE &&
    durationType === DURATION_TYPE.HOUR
  ) {
    return "pc.20-hour-bundle";
  }
  if (
    title === "30 Mins Class" &&
    type === CLASSES_TYPE.ONLINE &&
    subType === ONLINE_CLASSES_TYPE.QUICK_SESSIONS &&
    durationType === DURATION_TYPE.THIRTY_MINUTES
  ) {
    return "pc.30-mins-class";
  }
  if (
    title === "10 Sessions" &&
    type === CLASSES_TYPE.ONLINE &&
    subType === ONLINE_CLASSES_TYPE.QUICK_SESSIONS &&
    durationType === DURATION_TYPE.THIRTY_MINUTES
  ) {
    return "pc.10-sessions";
  }
  if (
    title === "1 hour pack" &&
    type === CLASSES_TYPE.ONLINE &&
    subType === ONLINE_CLASSES_TYPE.REGULAR_SESSIONS &&
    durationType === DURATION_TYPE.HOUR
  ) {
    return "pc.1-hour-pack";
  }
  if (
    title === "10 hour pack" &&
    type === CLASSES_TYPE.ONLINE &&
    subType === ONLINE_CLASSES_TYPE.REGULAR_SESSIONS &&
    durationType === DURATION_TYPE.HOUR
  ) {
    return "pc.10-hour-pack";
  }
  if (
    title === "20 hours pack" &&
    type === CLASSES_TYPE.ONLINE &&
    subType === ONLINE_CLASSES_TYPE.REGULAR_SESSIONS &&
    durationType === DURATION_TYPE.HOUR
  ) {
    return "pc.20-hour-pack";
  }
  if (
    title === "90 minutes session" &&
    type === CLASSES_TYPE.ONLINE &&
    subType === ONLINE_CLASSES_TYPE.LONG_SESSIONS &&
    durationType === DURATION_TYPE.NINETY_MINUTES
  ) {
    return "pc.90-mins-session";
  }
  if (
    title === "10 sessions" &&
    type === CLASSES_TYPE.ONLINE &&
    subType === ONLINE_CLASSES_TYPE.LONG_SESSIONS &&
    durationType === DURATION_TYPE.NINETY_MINUTES
  ) {
    return "pc.10-sessions-90-mins";
  }
  if (
    title === "20 sessions" &&
    type === CLASSES_TYPE.ONLINE &&
    subType === ONLINE_CLASSES_TYPE.LONG_SESSIONS &&
    durationType === DURATION_TYPE.NINETY_MINUTES
  ) {
    return "pc.20-sessions-90-mins";
  }
};

const getSubtitleId = (text: string) => {
  if (text === "30 Minutes Each") {
    return "pc.30-mis-each";
  }
  if (text === "90 minutes each") {
    return "pc.90-mins-each";
  }
};

const getDurationId = (text: string) => {
  if (text === "hour") {
    return "pc.hour";
  }
  if (text === "Week") {
    return "pc.week";
  }
  if (text === "Minutes") {
    return "pc.minutes";
  }
  if (text === "30 mins") {
    return "pc.30-mins";
  }
  if (text === "90 mins") {
    return "pc.90-mins";
  }
};

export {
  getSubtitleId,
  getDurationId,
  getPricingCardTitleId,
  getPricingTypeId,
  getSubTypeId,
  getRemainingSeats,
  getEventOnDetails,
  getPriceSymbol,
  getStudentWeekName,
  getLevelName,
  getPlanFor,
  getLocalBuyEventDetails,
  getLocalBuyClassDetaills,
  getLocalCarts,
  handleReturnToCart,
  needToRedirect,
  AMOUNT_CONVERTOR,
  getFinalAmount,
  getProductData,
  getClubImage,
  getLocalBuyClubDetails,
  getItemsDetailsFromTransaction,
  getClassesImage,
  getClassTitleAndSubtitle,
  getClassTypeAndSubType,
  getStartAndEndDateOfClasses,
  getCreatorDetails,
  getClassesLevel,
  getClassesLocation,
  getClassessFetchTabs,
  getLevelColor,
  getRedirectUrl,
  getClassessFilters,
  getFirstAndLastDateOfMonth,
  getClassStatus,
  getClassTimings,
  getAccessRestrictedInfo,
  getStatusBasedOnSelectedDate,
  getMondayAndThursday,
  isInPersonGroupClassLive,
  getLinkInfo,
  getTypedClubInfo,
  getTypedEventInfo,
  getTypedClassPricingInfo,
  getTypedScheduledInfo,
  getTeachers,
  getProficiencyLevel,
  classAndEventModeAndLocation,
  getDiscountedPrice,
  getOriginalPrice,
  checkIfEveryCurrencySame,
  getCustomProductInfo,
  getTargetLanguageFromCookieOrUser,
};
