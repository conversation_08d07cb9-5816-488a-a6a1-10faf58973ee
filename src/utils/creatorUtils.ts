import { v4 as uuidv4 } from "uuid";
import axios from "axios";
import { uploadFileOnAws } from "./uploadFileOnAws";
import { ROLE_TYPES } from "@/constant/Enums";

type creatorUserValidatorProps = {
  firstName: string;
  lastName: string;
  email?: string;
  avatarFile: File;
};
const creatorUserValidator = ({
  firstName,
  lastName,
  email,
  avatarFile,
}: creatorUserValidatorProps) => {
  if (!firstName) {
    return "Please enter firstName";
  }
  if (!lastName) {
    return "Please enter lastName";
  }
  // if (!email) {
  //   return "Please enter email";
  // }
  // if (!email) {
  //   return "Please enter email";
  // }
  if (!avatarFile) {
    return "Please select profile image";
  }
  // if (!validator.isEmail(email)) {
  //   return "Please enter accurate email";
  // }
};

const uploadCreatorImageTos3 = async (
  id: string,
  environment: string,
  avatarFile: File
) => {
  const avatarFileName = avatarFile?.name;
  const data = await uploadFileOnAws({
    file: avatarFile,
    key: `${environment}/user/${id}/profile_${avatarFileName}`,
  });
  return data;
};

type createCreatorServiceProps = {
  environment: string;
  avatarFile: File;
  firstName: string;
  lastName: string;
  email: string;

  onImageUploadFail: () => void;
  onCreateCreatorFail: () => void;
  onCreateCreatorSuccess: ({ data }) => void;
};
const createCreatorService = async ({
  environment,
  avatarFile,
  firstName,
  lastName,
  email,

  onImageUploadFail = () => {},
  onCreateCreatorFail = () => {},
  onCreateCreatorSuccess = ({ data: any }) => {},
}: createCreatorServiceProps) => {
  try {
    const profileImageId = uuidv4();
    const uploadedImg = await uploadCreatorImageTos3(
      profileImageId,
      environment,
      avatarFile
    );
    if (!uploadedImg.isError && uploadedImg.data.Key) {
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/user/create`,
        {
          firstName,
          lastName,
          email,
          role: ROLE_TYPES.CREATOR,
          profileImageId,
          profileImageKey: uploadedImg.data.Key,
        }
      );
      if (data.success && data?.data?._id) {
        onCreateCreatorSuccess({
          data: {
            uploadedImage: uploadedImg,
            data: data.data,
          },
        });
      } else {
        onCreateCreatorFail();
      }
    } else {
      onImageUploadFail();
    }
  } catch (error) {
    onCreateCreatorFail();
  }
};

export { creatorUserValidator, uploadCreatorImageTos3, createCreatorService };
