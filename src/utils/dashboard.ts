import { VideoProgressSchemaType } from "@/api/mongoTypes";
import { PROGRESS_STATUS } from "@/constant/Enums";

const getEmptyClassesTitleAndDesc = ({ type }) => {
  if (type === 1) {
    return {
      title: "dash.no-past-class/event",
      description: "dash.no-past-class/event-desc",
    };
  }
  if (type === 2) {
    return {
      title: "dash.no-ongoing-class/event",
      description: "dash.no-ongoing-class/event-desc",
    };
  }
  return {
    title: "dash.no-upcoming-class/event",
    description: "dash.no-upcoming-class/event-desc",
  };
};

const getEmptySuggestionsTitleAndDesc = ({ type }) => {
  if (type === 1) {
    return {
      title: "dash.no-past-suggestion",
      description: "dash.no-past-suggestion-desc",
    };
  }
  if (type === 2) {
    return {
      title: "dash.nothing-suggested",
      description: "dash.nothing-suggested-desc",
    };
  }
  return {
    title: "dash.no-upcoming-suggestion",
    description: "dash.no-upcoming-suggestion-desc",
  };
};

const getEmptyPurchaseTitleAndDes = () => {
  return {
    title: "dash.no-purchases",
    description: "dash.browse-classes",
  };
};

const getVideoState = (duration: number) => {
  if (duration === 0) {
    return PROGRESS_STATUS.NOT_STARTED;
  }
  if (duration < 100) {
    return PROGRESS_STATUS.IN_PROGRESS;
  }
  return PROGRESS_STATUS.COMPLETE;
};

const getVideoProgress = (progress: VideoProgressSchemaType) => {
  if (!progress) {
    return 0;
  }
  return progress.progress;
};

export {
  getEmptyClassesTitleAndDesc,
  getEmptySuggestionsTitleAndDesc,
  getEmptyPurchaseTitleAndDes,
  getVideoState,
  getVideoProgress,
};
