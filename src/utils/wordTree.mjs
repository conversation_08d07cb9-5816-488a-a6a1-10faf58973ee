//Groups
// 1: parent root
// 2: verbs
// 3: nouns
// 4: adjectives
import { writeFile } from "fs/promises"; // Import the 'writeFile' function from the 'fs/promises' module
import { poner } from "../../public/data/roots/poner.mjs";

const root = poner;

function flattenNodes(node, group) {
  const nodes = [{ id: node.id, group }];

  if (node.nodes) {
    let nestedGroup = group + 1;
    for (const key in node.nodes) {
      node.nodes[key].forEach((nestedNode) => {
        nodes.push(...flattenNodes(nestedNode, nestedGroup));
      });

      nestedGroup++;
    }
  }

  return nodes;
}

function createLinks(node, sourceId) {
  const links = [];

  if (node.nodes) {
    for (const key in node.nodes) {
      node.nodes[key].forEach((nestedNode) => {
        links.push({
          source: nestedNode.id,
          target: sourceId,
          value: 1,
        });

        links.push(...createLinks(nestedNode, nestedNode.id));
      });
    }
  }

  return links;
}

(async () => {
  const nodes = flattenNodes(root, 1);
  const links = createLinks(root, root.id);
  const jsonOutput = JSON.stringify({ nodes, links }, null, 2);

  // Appending the additional data to the JSON object
  const additionalData = {
    nodeStyles: [
      {
        group: 1,
        color: "#ff0000",
        textHeight: 6,
        fontFace: "Arial",
      },
      {
        group: 2,
        color: "#00FF00",
        textHeight: 6,
        fontFace: "Courier New",
      },
      {
        group: 3,
        color: "#0000FF",
        textHeight: 6,
        fontFace: "Times New Roman",
      },
      {
        group: 4,
        color: "#FFFF00",
        textHeight: 6,
        fontFace: "Verdana",
      },
      {
        group: 5,
        color: "#00FFFF",
        textHeight: 6,
        fontFace: "Georgia",
      },
      {
        group: 6,
        color: "#FF00FF",
        textHeight: 14,
        fontFace: "Comic Sans MS",
      },
      {
        group: 7,
        color: "#C0C0C0",
        textHeight: 6,
        fontFace: "Impact",
      },
    ],
    linkColor: "white",
    linkOpacity: 0.8,
    linkWidth: 0.2,
  };

  // Merging the original and additional data
  const finalJsonOutput = JSON.parse(jsonOutput);
  Object.assign(finalJsonOutput, additionalData);

  const parentDir =
    "/Users/<USER>/Desktop/coding/patito/patito-feo-app/public/data/roots";
  const fileName = `${parentDir}/${root.id}.json`;

  try {
    await writeFile(fileName, JSON.stringify(finalJsonOutput, null, 2));
    console.log(`JSON file '${fileName}' has been successfully created.`);
  } catch (err) {
    console.error("Error writing the JSON file:", err);
  }
})();
