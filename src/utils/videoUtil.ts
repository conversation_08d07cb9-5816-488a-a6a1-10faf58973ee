import { environment } from "@/api/aws";
import { ASSETS_STATUS, ASSETS_TYPES } from "@/constant/Enums";
import { MaybeFileOrUrl, ValueOf } from "@/types";
import axios from "axios";
import { uploadFileOnAws } from "./uploadFileOnAws";

const getCategoryNamesBasedOnId = (categories, selectedCategories) => {
  let data = [];
  categories.map((m) => {
    if (selectedCategories.includes(m._id)) {
      data.push(m.name);
    }
  });
  return data;
};

type validateUploadVideoFieldsProps = {
  title: string;
  description: string;
  language: string;
  languageProficiency: string;
  selectedCollection: string;
  selectedCreator: string;
  selectedCategories: string[];
  selectedThumbnail: File | string;
  selectedEnglishSubtitle: File | string;
  selectedSpanishSubtitle: File | string;

  ignoreCollection?: boolean;
};

const validateUploadVideoFields = ({
  title,
  description,
  language,
  languageProficiency,
  selectedCreator,
  selectedCategories,
  selectedThumbnail,
  selectedCollection,
  ignoreCollection = false,
  selectedEnglishSubtitle,
  selectedSpanishSubtitle,
}: validateUploadVideoFieldsProps) => {
  if (!title) {
    return "Please enter title";
  }
  if (!description) {
    return "Please enter description";
  }
  if (!language) {
    return "Please select language";
  }
  if (!languageProficiency) {
    return "Please select languageProficiency";
  }
  if (!selectedCollection && !ignoreCollection) {
    return "Please select Collection";
  }
  if (!selectedThumbnail) {
    return "Please select thumbnail";
  }
  // if (!selectedEnglishSubtitle) {
  //   return "Please select English subtitle";
  // }
  // if (!selectedSpanishSubtitle) {
  //   return "Please select Spanish subtitle";
  // }
  if (!selectedCreator) {
    return "Please select Creator";
  }
  if (selectedCategories.length === 0) {
    return "Please select categories";
  }
};

const isFile = (file: MaybeFileOrUrl) => {
  if (typeof file === "string" || !file.name) {
    return false;
  }
  return true;
};

type handleCreateVideoProps = {
  isUpdate?: boolean;
  id?: string;
  title: string;
  description: string;
  language: string;
  proficiencyLevel: string;
  categoriesId: string[];
  categoriesName: string[];
  userId: string;
  level: string;
  duration: number;
  collectionId: string;
  file: MaybeFileOrUrl;
  isFeatured: Boolean;
  selectedThumbnail: MaybeFileOrUrl;
  selectedEnglishSubtitle: MaybeFileOrUrl;
  selectedSpanishSubtitle: MaybeFileOrUrl;
  onVideoCrateSuccess: () => void;
  onVideoUploadSuccess: ({ data }) => void;
  onVideoCreateError: () => void;
};
const handleCreateVideo = async ({
  isUpdate = false,
  id = null,
  title,
  description,
  language,
  proficiencyLevel,
  categoriesId,
  categoriesName,
  userId,
  duration,
  collectionId,
  file,
  selectedThumbnail,
  isFeatured,
  level,
  selectedEnglishSubtitle,
  selectedSpanishSubtitle,

  onVideoCrateSuccess = () => {},
  onVideoUploadSuccess = ({ data }) => {},
  onVideoCreateError = () => {},
}: handleCreateVideoProps) => {
  try {
    const payload = {
      title,
      description,
      language,
      proficiencyLevel,
      categoriesId,
      categoriesName,
      userId,
      duration,
      collectionId,
      isFeatured,
      level,
    };

    let data = null;

    if (isUpdate) {
      const url = `${process.env.NEXT_PUBLIC_BASE_URL}api/video/update`;
      const { data: respData } = await axios.put(url, {
        ...payload,
        id,
      });
      data = respData;
      console.log("after updating data", respData);
    } else {
      const url = `${process.env.NEXT_PUBLIC_BASE_URL}api/video/create`;
      const { data: respData } = await axios.post(url, payload);
      data = respData;
      console.log("after creation data", respData);
    }

    if (data.id && data._id) {
      const videoId = data.id;
      onVideoCrateSuccess();

      const mediaJobId = await createMediaJob({
        error: "",
        id: videoId,
        status: ASSETS_STATUS.STARTED,
        type: ASSETS_TYPES.VIDEO,
      });

      // construct keys only if files are new (File object)
      const videoKey =
        file && typeof file !== "string"
          ? `${environment}/${videoId}/${file.name}`
          : data.videoKey;

      const thumbnailKey =
        selectedThumbnail && typeof selectedThumbnail !== "string"
          ? `${environment}/${videoId}/thumbnail_${selectedThumbnail.name}`
          : data.thumbnailKey;

      const subtitleEnglishKey =
        selectedEnglishSubtitle && typeof selectedEnglishSubtitle !== "string"
          ? `${environment}/${videoId}/subtitle_${selectedEnglishSubtitle.name}`
          : data.subtitleEnglishKey;

      const subtitleSpanishKey =
        selectedSpanishSubtitle && typeof selectedSpanishSubtitle !== "string"
          ? `${environment}/${videoId}/subtitle_${selectedSpanishSubtitle.name}`
          : data.subtitleSpanishKey;

      // upload main video if new file
      let uploadVideoData: any = { isError: false, data: { Key: videoKey } };
      if (file && typeof file !== "string") {
        uploadVideoData = await uploadFileOnAws({ file, key: videoKey });
      }

      if (!uploadVideoData.isError && uploadVideoData?.data?.Key) {
        await updateMediaJob({
          error: "",
          id: mediaJobId,
          status: ASSETS_STATUS.READY,
        });

        // upload thumbnail if it's a file
        const { isError: thumbnailError } =
          selectedThumbnail && typeof selectedThumbnail !== "string"
            ? await handleUploadFileAndStatus({
                Key: thumbnailKey,
                file: selectedThumbnail,
                videoId,
                type: ASSETS_TYPES.THUMBNAIL,
              })
            : { isError: false };

        // upload English subtitle if it's a file
        const { isError: en_subtitleError } =
          selectedEnglishSubtitle && typeof selectedEnglishSubtitle !== "string"
            ? await handleUploadFileAndStatus({
                Key: subtitleEnglishKey,
                file: selectedEnglishSubtitle,
                videoId,
                type: ASSETS_TYPES.CC,
              })
            : { isError: false };

        // upload Spanish subtitle if it's a file
        const { isError: es_subtitleError } =
          selectedSpanishSubtitle && typeof selectedSpanishSubtitle !== "string"
            ? await handleUploadFileAndStatus({
                Key: subtitleSpanishKey,
                file: selectedSpanishSubtitle,
                videoId,
                type: ASSETS_TYPES.CC,
              })
            : { isError: false };

        const uploadedBothThumbAndSubtitle =
          !en_subtitleError && !es_subtitleError && !thumbnailError;

        const deletedIds = isUpdate
          ? {
              deletedFileId: isFile(file) ? data.videoKey : null,
              deletedThumnailId: isFile(selectedThumbnail)
                ? data.thumbnailKey
                : null,
              deletedEsSubtitle: isFile(selectedSpanishSubtitle)
                ? data.es_subtitleKey
                : null,
              deletedEnSubtitle: isFile(selectedEnglishSubtitle)
                ? data.en_subtitleKey
                : null,
            }
          : null;

        const { data: updatedVideo } = await axios.put(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/video/update`,
          {
            id: data._id,
            thumbnailKey,
            subtitleEnglishKey,
            subtitleSpanishKey,
            videoKey,
            status: uploadedBothThumbAndSubtitle ? "ready" : "none",
            deletedIds,
          },
          {
            params: { getPopulated: true },
          }
        );

        onVideoUploadSuccess({ data: updatedVideo });
      } else {
        updateMediaJob({
          error: uploadVideoData.message,
          id: mediaJobId,
          status: ASSETS_STATUS.FAILED,
        });
        onVideoCreateError();
      }
    } else {
      onVideoCreateError();
    }
  } catch (error) {
    console.error("Something went wrong in handleCreateVideo due to ", error);
    onVideoCreateError();
  }
};

const handleUploadFileAndStatus = async ({ Key, file, videoId, type }) => {
  try {
    const mediaJobId = await createMediaJob({
      error: "",
      id: videoId,
      status: ASSETS_STATUS.STARTED,
      type,
    });

    const upload = await uploadFileOnAws({
      file,
      key: Key,
    });

    await updateMediaJob({
      error: upload.isError ? upload.message : "",
      id: mediaJobId,
      status: upload.isError ? ASSETS_STATUS.FAILED : ASSETS_STATUS.READY,
    });
    return {
      isError: upload.isError,
    };
  } catch (error) {
    const errorMessage = `Something went wrong in handleUploadFileAndStatus due to ${JSON.stringify(
      error
    )}`;
    console.error(errorMessage);
    return {
      isError: true,
    };
  }
};

type createMediaJobProps = {
  id: String;
  type: ValueOf<typeof ASSETS_TYPES>;
  status: ValueOf<typeof ASSETS_STATUS>;
  error: String;
};
const createMediaJob = async ({
  id,
  type,
  status,
  error,
}: createMediaJobProps) => {
  try {
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/media-job/create`,
      {
        status,
        errorMsg: error,
        videoId: id,
        assetType: type,
      }
    );
    return data?.data?._id;
  } catch (error) {
    console.error(
      `Something went wrong in handleCreateMediaJob due to `,
      error
    );
  }
};

type updateMediaJobProps = {
  id: String;
  status: ValueOf<typeof ASSETS_STATUS>;
  error: String;
};
const updateMediaJob = async ({ id, status, error }: updateMediaJobProps) => {
  try {
    const { data } = await axios.put(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/media-job/update`,
      {
        id,
        status,
        errorMsg: error,
      }
    );
    return data?._id;
  } catch (error) {
    console.error(
      `Something went wrong in handleCreateMediaJob due to `,
      error
    );
  }
};

export {
  validateUploadVideoFields,
  getCategoryNamesBasedOnId,
  handleCreateVideo,
};
