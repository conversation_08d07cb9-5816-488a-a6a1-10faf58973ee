import axios from 'axios';

/**
 * Creates a new contact in Brevo API
 * 
 * @param email - Email address of the contact
 * @param firstName - First name of the contact
 * @param lastName - Last name of the contact
 * 
 * @returns Promise with the API response
 */
export async function createContact(
  email: string,
  firstName: string,
  lastName?: string,
  additionalAttr?: Record<string, string>,
): Promise<any> {
  try {
    const url = 'https://api.brevo.com/v3/contacts';
    const headers = {
      'accept': 'application/json',
      'content-type': 'application/json',
      'api-key': process.env.BREVO_API_KEY
    };

    // Brevo only take note of uppercase attributes, so we enforce that even if caller forgets!
    const attr = Object.keys(additionalAttr).reduce((prev, key) => {
      prev[key.toUpperCase()] = additionalAttr[key];
      return prev;
    }, {});

    // Request body
    const data = {
      email,
      attributes: {
        FIRSTNAME: firstName,
        LASTNAME: lastName,
        ...attr,
      },
      updateEnabled: false,
    };

    if (process.env.NEXT_PUBLIC_ENVIRONMENT == 'production') data['listIds'] = [4];

    const response = await axios.post(url, data, { headers });
    console.log('Contact created successfully:', response.data);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Error creating contact in Brevo:', error.response?.data || error.message);
    } else {
      console.error('Unexpected error creating contact in Brevo:', error);
    }
    
    console.error(`Tried creating Brevo contact but encountered an UNKNOWN error we should have thrown!`);
  }
}