import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import dayjs from "dayjs";
import { dateAsPerTimeZone } from "./dateTime";
import { ClassesTypesPills } from "@/constant/classes";

const getConsistentData = (data) => {
  return data.map((info) => {
    const isEvent = info.type === "event";
    const timeZone = isEvent ? info?.data?.timezone : null;

    let start = dayjs(
      dateAsPerTimeZone({
        dateToChange: !isEvent
          ? info?.data?.startDate
          : info?.data?.startDateTime,
        timeZone,
      })
    );
    const end = dayjs(
      dateAsPerTimeZone({
        dateToChange: !isEvent ? info?.data?.endDate : info?.data?.endDateTime,
        timeZone,
      })
    );

    if (info.type === "event") {
      return {
        ...info,
        data: {
          ...info.data,
          startDate: start,
          endDate: end,
        },
      };
    }
    return {
      ...info,
      data: {
        ...info.data,
        startDate: start,
        endDate: end,
      },
    };
  });
};
type groupDataByDateProps = {
  data: any[];
  active: CLASSESS_FETCH_DURATION;
  isAscending: boolean;
  baseDate?: Date | string;
  from?: Date | string;
  to?: Date | string;
};
const groupDataByDate = ({
  data,
  active,
  isAscending = true,
  baseDate,
  from,
  to,
}: groupDataByDateProps) => {
  const eventDates: Record<string, any[]> = {};
  const dataArray = getConsistentData(data);

  const today = dayjs(baseDate || new Date()).startOf("day");
  const fromDate = from ? dayjs(from).startOf("day") : null;
  const toDate = to ? dayjs(to).startOf("day") : null;

  dataArray?.forEach((info) => {
    const details = info.data;

    let start = dayjs(
      dateAsPerTimeZone({
        dateToChange: details.startDate,
        timeZone: details?.timezone ?? null,
      })
    );
    const end = dayjs(
      dateAsPerTimeZone({
        dateToChange: details.endDate,
        timeZone: details?.timezone ?? null,
      })
    );

    // Skip events that end before fromDate or start after toDate
    // if (fromDate && end.isBefore(fromDate)) return;
    // if (toDate && start.isAfter(toDate)) return;

    while (start.isBefore(end) || start.isSame(end, "day")) {
      const dateStr = start.format("YYYY-MM-DD");
      const compareDate = start.startOf("day");

      if (fromDate && compareDate.isBefore(fromDate)) {
        start = start.add(1, "day");
        continue;
      }
      if (toDate && compareDate.isAfter(toDate)) {
        start = start.add(1, "day");
        continue;
      }

      const IS_ALL = active === CLASSESS_FETCH_DURATION.ALL;
      const IS_CURRENT =
        active === CLASSESS_FETCH_DURATION.CURRENT && compareDate.isSame(today);
      const IS_PAST =
        active === CLASSESS_FETCH_DURATION.PAST && compareDate.isBefore(today);
      const IS_UPCOMING =
        active === CLASSESS_FETCH_DURATION.UPCOMING &&
        compareDate.isAfter(today);

      const shouldInclude = IS_ALL || IS_PAST || IS_UPCOMING || IS_CURRENT;

      if (!shouldInclude) {
        start = start.add(1, "day");
        continue;
      }

      if (info.type === "event") {
        if (!eventDates[dateStr]) eventDates[dateStr] = [];
        eventDates[dateStr].push(info);
      } else {
        if ([1, 2, 3, 4].includes(start.day())) {
          if (!eventDates[dateStr]) eventDates[dateStr] = [];
          eventDates[dateStr].push(info);
        }
      }

      start = start.add(1, "day");
    }
  });

  const groupedData = Object.entries(eventDates)
    .map(([date, entries]) => ({
      date,
      data: entries,
    }))
    .sort((a, b) =>
      isAscending
        ? new Date(a.date).getTime() - new Date(b.date).getTime()
        : new Date(b.date).getTime() - new Date(a.date).getTime()
    );

  const getTimeInMinutes = (item) => {
    if (item.type === "event" && item.data && item.data.startDateTime) {
      const timezoneDate = dateAsPerTimeZone({
        dateToChange: item.data.startDateTime,
        timeZone: item.data?.timezone ?? null,
      });
      const totalHours = timezoneDate.getHours() * 60;
      const totalMinutes = timezoneDate.getMinutes();
      const totalDuration = totalHours + totalMinutes;
      return totalDuration;
    } else {
      return 10 * 60;
    }
  };

  const sortedData = groupedData.map((group) => {
    const sorted = {
      ...group,
      data: group.data.sort((a, b) => {
        const timeA = getTimeInMinutes(a);
        const timeB = getTimeInMinutes(b);
        console.log({
          timeA,
          timeB,
        });
        const result = timeA - timeB;
        return result;
      }),
    };
    return sorted;
  });

  return sortedData;
};

const getEventDates = (data) => {
  const eventDates = {};

  if (data) {
    data?.forEach((event) => {
      let start = dayjs(
        dateAsPerTimeZone({
          dateToChange: event.startDate,
          timeZone: event?.timezone ?? null,
        })
      );
      const end = dayjs(
        dateAsPerTimeZone({
          dateToChange: event.endDate,
          timeZone: event?.timezone ?? null,
        })
      );

      while (start.isBefore(end) || start.isSame(end, "day")) {
        const dateStr = start.format("YYYY-MM-DD");
        // console.log(`Date: ${dateStr}, Day: ${start.day()}`); // Debug: Log date and day
        if (event.type === "event") {
          if (!eventDates[dateStr]) {
            eventDates[dateStr] = [];
          }
          const pill = ClassesTypesPills.find((p) => p.value === event.type);
          if (pill) {
            eventDates[dateStr].push(pill.color);
          }
        } else {
          if (
            start.day() === 1 ||
            start.day() === 2 ||
            start.day() === 3 ||
            start.day() === 4
          ) {
            if (!eventDates[dateStr]) {
              eventDates[dateStr] = [];
            }
            const pill = ClassesTypesPills.find((p) => p.value === "IN_PERSON");
            if (pill) {
              eventDates[dateStr].push(pill.color);
            }
          } else {
            console.log(`Excluded: ${dateStr}, Day: ${start.day()}`);
          }
        }
        start = start.add(1, "day");
      }
    });
  }
  return eventDates;
};

export { groupDataByDate, getEventDates };
