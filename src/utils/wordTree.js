const root = {
  id: "poner",
  nodes: {
    verbs: [
      {
        id: "imponer",
        nodes: {
          nouns: [{ id: "imposición" }],
          adjectives: [{ id: "imponente" }],
        },
      },
    ],
    nouns: [{ id: "posición" }, { id: "ponería" }],
  },
};

function flattenNodes(node, group) {
  const nodes = [{ id: node.id, group }];

  if (node.nodes) {
    for (const key in node.nodes) {
      node.nodes[key].forEach((nestedNode) => {
        let nestedGroup;

        switch (key) {
          case "verbs":
            nestedGroup = group + 1;
            break;
          case "nouns":
            nestedGroup = group + 2;
            break;
          case "adjectives":
            nestedGroup = group + 3;
            break;
          default:
            nestedGroup = group + 1;
            break;
        }

        nodes.push(...flattenNodes(nestedNode, nestedGroup));
      });
    }
  }

  return nodes;
}

function createLinks(node, sourceId) {
  const links = [];

  if (node.nodes) {
    for (const key in node.nodes) {
      node.nodes[key].forEach((nestedNode) => {
        links.push({
          source: nestedNode.id,
          target: sourceId,
          value: 1,
        });

        links.push(...createLinks(nestedNode, nestedNode.id));
      });
    }
  }

  return links;
}

const nodes = flattenNodes(root, 1);
const links = createLinks(root, root.id);
const jsonOutput = JSON.stringify({ nodes, links }, null, 2);
