import { UserType, VideosCollectionType, VideoType } from "@/api/mongoTypes";
import { ProgressType } from "@/types";
import { getProficiencyEn } from "./common";

export const getVideoDetails = (data: ProgressType) => {
  if (!data) {
    return null;
  }

  if (
    "videoId" in data &&
    data.videoId &&
    typeof data.videoId === "object" &&
    "_id" in data.videoId
  ) {
    return data.videoId as VideoType;
  }
  return null;
};

export const getCollectionDetails = (data: ProgressType) => {
  if (!data) {
    return null;
  }
  if (
    "collectionId" in data &&
    data.collectionId &&
    typeof data.collectionId === "object" &&
    "_id" in data.collectionId
  ) {
    return data.collectionId as VideosCollectionType;
  }
  return null;
};

export const getLearningName = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails) {
    return vidDetails.title;
  }
  if (collectionDetails) {
    return collectionDetails.name;
  }
  return null;
};
export const getLearningDetailPageUrl = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails) {
    return `/videos/${vidDetails._id}`;
  }
  if (collectionDetails) {
    return `/videos/collection/${collectionDetails._id}`;
  }
  return null;
};

export const getLearningId = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails) {
    return vidDetails._id;
  }
  if (collectionDetails) {
    return collectionDetails._id;
  }
  return null;
};

export const getLearningDescription = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails) {
    return vidDetails.description;
  }
  if (collectionDetails) {
    return collectionDetails.desc;
  }
  return null;
};

export const getLearningCategories = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails) {
    return vidDetails.categories;
  }
  if (collectionDetails) {
    return collectionDetails.categories;
  }
  return null;
};

const getPopulatedUserDetails = (data: any) => {
  if (data && "_id" in data) {
    return data as UserType;
  }
  return null;
};

export const getLearningCreator = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails?.creator) {
    return getPopulatedUserDetails(vidDetails.creator);
  }
  if (collectionDetails) {
    return null;
  }
  return null;
};

export const getLearningProgress = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails) {
    return data.progress;
  }
  if (collectionDetails) {
    return data.progress;
  }

  return 0;
};

export const getLearningProficiencyLevel = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  console.log({
    collectionDetails,
    vidDetails,
  });

  if (vidDetails?.proficiencyLevel) {
    return getProficiencyEn({
      data: vidDetails.proficiencyLevel,
      isEn: true,
    });
  }
  if (collectionDetails?.proficiencyLevel) {
    return getProficiencyEn({
      data: collectionDetails.proficiencyLevel,
      isEn: true,
    });
  }
  return null;
};

export const getLearningImage = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails?.thumbnailUrl) {
    return vidDetails.thumbnailUrl;
  }
  if (collectionDetails?.coverImageUrl) {
    return collectionDetails.coverImageUrl;
  }
  return null;
};

export const getIsLiked = (data: ProgressType) => {
  const vidDetails = getVideoDetails(data);
  const collectionDetails = getCollectionDetails(data);

  if (vidDetails?.isLiked) {
    return vidDetails.isLiked;
  }
  if (collectionDetails?.isLiked) {
    return collectionDetails.isLiked;
  }
  return false;
};
