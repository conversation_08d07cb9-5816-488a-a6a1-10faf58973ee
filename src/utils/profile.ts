export const getGoalId = (name: string) => {
  if (name === "Academic") {
    return "kyc.academic";
  }
  if (name === "Professional") {
    return "kyc.professional";
  }
  if (name === "Family") {
    return "kyc.family";
  }
  if (name === "Traveling/Vacation") {
    return "kyc.travel";
  }
  if (name === "Personal Growth") {
    return "kyc.personal-growth";
  }
  if (name === "Social/Community") {
    return "kyc.social-community";
  }
  return "";
};

export const getProficiencyId = (name: string) => {
  if (name === "From scratch/zero") {
    return "kyc.from-scratch";
  }
  if (name === "Basic knowledge") {
    return "kyc.basic-knowledge";
  }
  if (name === "Everyday survival") {
    return "kyc.everyday-survival";
  }
  if (name === "Independent speaker") {
    return "kyc.independent-speaker";
  }
  if (name === "Conversational") {
    return "kyc.conversational";
  }
  if (name === "Fluent") {
    return "kyc.fluent";
  }
  if (name === "Proficient") {
    return "kyc.proficient";
  }
  return "";
};

export const getInterestsId = (name: string) => {
  if (name === "Cooking & Food Experiences") {
    return "kyc.cooking";
  }
  if (name === "Mexican Festivals & Traditions") {
    return "kyc.mexican-traditions";
  }
  if (name === "Music & Dance") {
    return "kyc.music-dance";
  }
  if (name === "Travel & Day Trips") {
    return "kyc.travel-excursions";
  }
  if (name === "History & Storytelling") {
    return "kyc.history-storytelling";
  }
  if (name === "Everyday Conversations") {
    return "kyc.everyday-conversations";
  }
  if (name === "Shopping & Markets") {
    return "kyc.shopping-markets";
  }
  if (name === "Sports & Activities") {
    return "kyc.sports-activities";
  }
  if (name === "Work & Professional Growth") {
    return "kyc.work-growth";
  }
  if (name === "Friendship & Networking") {
    return "kyc.friendship-networking";
  }
  if (
    name === "Art, Literature & Architecture" ||
    name === "Art, Literature and Architecture"
  ) {
    return "kyc.art-literature";
  }
  return "";
};
