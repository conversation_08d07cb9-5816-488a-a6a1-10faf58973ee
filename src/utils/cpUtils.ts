import dayjs from "dayjs";
import { CURRENCY_ENUM } from "@/constant/Enums";
import axiosInstance from "./interceptor";

type validateCpProps = {
  title: string;
  description: string;
  price: number;
  currency: CURRENCY_ENUM;
  targetLanguage: string;
  startDate: dayjs.Dayjs;
  internalNotes: string;
};
const validateCp = ({
  title,
  description,
  price,
  currency,
  targetLanguage,
  startDate,
  internalNotes,
}: validateCpProps) => {
  if (!title) {
    return "Please enter an event title";
  }
  if (!description || description.length < 5) {
    return "Description is too small";
  }
  if (!price) {
    return "Price cannot be empty";
  }
  if (!currency) {
    return "Please select a currency";
  }
  if (!targetLanguage) {
    return "Please select target language";
  }
  if (!startDate) {
    return "Please select start date";
  }
  return "";
};

const getCustomProductDetails = async (id: string) => {
  try {
    const resp = await axiosInstance.get(`custom-product/get/${id}`);
    return resp;
  } catch (error) {
    console.error("Something went wrong in getCustomProductDetails", error);
    return null;
  }
};

export { validateCp, getCustomProductDetails };
