import { Box, Card, Chip, Divider, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import CustomButton from "../CustomButton";
import PricingType from "../classes/PricingType";
import { ClassesPricingType } from "@/api/mongoTypes";
import {
  getDuration,
  getPlansTitle,
  getPricingTitle,
  getSubTypeName,
  getTranslatedPlanTitle,
  getTypeName,
} from "@/utils/format";
import {
  CLASSES_TYPE,
  DURATION_TYPE,
  IN_PERSON_TYPE,
  ONLINE_CLASSES_TYPE,
} from "@/constant/Enums";
import { ValueOf } from "@/types";
import {
  getDurationId,
  getLocalBuyClassDetaills,
  getPricingCardTitleId,
  getPricingTypeId,
  getSubtitleId,
  getSubTypeId,
} from "@/utils/classes";
import CheckoutModal from "../CheckoutModal/CheckoutClass/CheckoutModal";
import PricingCardTopInfo from "./PricingCardTopInfo";
import useTranslate from "@/hooks/useTranslate";

type PricingCardProps = React.FC<{
  data: ClassesPricingType;
  isLoggedIn: boolean;
  redirectToSignup: () => void;
}>;

const PricingCard: PricingCardProps = ({
  data,
  isLoggedIn,
  redirectToSignup,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { translate } = useTranslate();

  const title = getPricingTitle({
    data: data,
    needSubtitle: false,
  });

  useEffect(() => {
    if (data) {
      const wasClassBuying = getLocalBuyClassDetaills();
      if (wasClassBuying?.uniqueId) {
        const idSame = wasClassBuying?.uniqueId === data.uniqueId;
        if (idSame) {
          setIsModalOpen(true);
        }
      }
    }
  }, [data]);

  const singlePrice = (() => {
    // if (option?.singlePrice) {
    //   return option?.singlePrice;
    // }
    if (data && data.plans.length > 0 && data.plans[0].price) {
      return data.plans[0].price / data.plans[0].duration;
    }
    return null;
  })();

  return (
    <>
      <Card
        sx={{
          width: {
            xs: "100%",
            sm: 350,
          },
          height: 550,
          p: 4,
          boxShadow: "1.73px 1.73px 6.06px 3.46px rgba(204, 204, 204, 0.54)",
          borderRadius: 2,
        }}
      >
        {(data.isLimitedDeal || data.isPopular) && (
          <PricingCardTopInfo
            isLimited={data.isLimitedDeal}
            isPopular={data.isPopular}
          />
        )}
        <Typography textAlign="start" mt={5} mb={0} fontSize={26}>
          {translate(
            getPricingCardTitleId({
              title,
              type: data.type,
              subType: data.subType,
              durationType: data.durationType,
            })
          )}
        </Typography>
        <Typography
          color="#6D6D6D"
          textAlign="start"
          sx={{ fontSize: 16, fontWeight: "500" }}
        >
          {data?.subtitle ? (
            <>({translate(getSubtitleId(data.subtitle))})</>
          ) : (
            <>&nbsp;</>
          )}
        </Typography>
        <PricingInfo
          discount={data.plans[0].discount}
          durationType={data.durationType}
          price={data.plans[0].price}
          singlePrice={singlePrice}
        />
        <Box display="flex" flexDirection="row" gap={5} mt={4}>
          <PricingType
            text={translate(getPricingTypeId(getTypeName(data.type)))}
          />
          <PricingType
            text={translate(getSubTypeId(getSubTypeName(data.subType)))}
          />
        </Box>

        <CustomButton
          text={translate("common.add-to-cart")}
          colortype="secondary"
          onClick={() => {
            // if (isLoggedIn) {
            setIsModalOpen(true);
            // } else {
            //   redirectToSignup();
            // }
          }}
          sx={{
            width: "100%",
            mt: 6,
          }}
        />
        {/* <CustomButton
          text="Buy Now"
          sx={{
            width: "100%",
            mt: 4,
          }}
          onClick={() => {
            if (isLoggedIn) {
              setIsModalOpen(true);
            } else {
              redirectToSignup();
            }
          }}
        /> */}

        <Rating data={data} />

        <Divider />

        <Box mt={5}>
          <Typography
            textAlign="start"
            mb={5}
            sx={{ fontWeight: "600", fontSize: 14 }}
          >
            {translate(
              data.plans.length === 1 ? "ipc.plandetails" : "ipc.plan-cust"
            )}
          </Typography>

          {data.plans.map((m) => (
            <WeekPrice
              key={m.planId}
              discount={m.discount}
              duration={m.duration}
              subType={data.subType}
              type={data.type}
              price={m.price}
            />
          ))}
        </Box>
      </Card>
      {isModalOpen && (
        <CheckoutModal
          pricingData={data}
          open={isModalOpen}
          isLoggedIn={isLoggedIn}
          setOpen={setIsModalOpen}
          redirectToSignup={redirectToSignup}
        />
      )}
    </>
  );
};

export default PricingCard;

type WeekPriceProps = React.FC<{
  price: number;
  discount: number;
  duration: number;
  type: ValueOf<typeof CLASSES_TYPE>;
  subType: ValueOf<typeof IN_PERSON_TYPE> | ValueOf<typeof ONLINE_CLASSES_TYPE>;
}>;
const WeekPrice: WeekPriceProps = ({
  price,
  discount,
  duration,
  subType,
  type,
}) => {
  const { translate } = useTranslate();
  const text = getPlansTitle({
    duration,
    subType,
    type,
  });

  const translatedText = getTranslatedPlanTitle(text, translate);

  return (
    <Box display="flex" flexDirection="row" alignItems="center" mb={3}>
      <Box mr={2}>
        <Typography sx={{ color: "#6F6F6F", fontSize: 12 }}>
          {translatedText} ${price.toFixed(2)}
        </Typography>
      </Box>
      {discount > 1 && (
        <SaveTag text={`${translate("common.save")} ${discount}%`} isSmall />
      )}
    </Box>
  );
};

const Rating = ({ data }) => {
  const { translate } = useTranslate();

  const studentEnrolledText = (() => {
    const students = data?.noOfStudentsEnrolled;
    if (students > 0) {
      const translatedText = translate("clubSubs.student-enrolled");
      if (students > 1) {
        return translatedText;
      }
      return translatedText
        .replace("students", "student")
        .replace("estudiantes", "estudiante");
    }
    return null;
  })();

  const rating = data?.ratings?.value ?? 0;

  return (
    <Box mt={5} mb={5}>
      {rating > 0 && (
        <Box display="flex" flexDirection="row" alignItems="center">
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            sx={{
              background: "#14AE5C",
              width: "max-content",
              borderRadius: 1,
              padding: "4px 8px",
              marginRight: 1,
            }}
          >
            <Typography
              sx={{
                fontSize: 10,
                color: "#fff",
                marginRight: 1,
              }}
            >
              4
            </Typography>
            <svg
              width="11"
              height="10"
              viewBox="0 0 11 10"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2.57824 9.82689L3.35324 6.50627L0.776367 4.27377L4.17074 3.98002L5.50012 0.848145L6.82949 3.97939L10.2232 4.27314L7.64637 6.50564L8.42199 9.82627L5.50012 8.06377L2.57824 9.82689Z"
                fill="white"
              />
            </svg>
          </Box>

          <Typography
            sx={{
              fontSize: 11,
            }}
          >
            ({rating} {translate("ipc.ratings")})
          </Typography>
        </Box>
      )}
      {studentEnrolledText && (
        <Typography textAlign="start" sx={{ fontSize: 12, color: "#818181" }}>
          {data?.noOfStudentsEnrolled} {studentEnrolledText}
        </Typography>
      )}
    </Box>
  );
};

type PricingInfoProps = React.FC<{
  price: number;
  durationType: ValueOf<typeof DURATION_TYPE>;
  discount: number;
  singlePrice: number;
}>;
const PricingInfo: PricingInfoProps = ({
  discount,
  durationType,
  price,
  singlePrice,
}) => {
  const durationText = getDuration(durationType);
  const { translate } = useTranslate();
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
      }}
      mt={6}
    >
      <Box display="flex" flexDirection="row" gap={2} alignItems="center">
        <Typography sx={{ fontSize: 30, fontWeight: 800, m: 0 }}>
          ${price.toFixed(2)}
        </Typography>
        {discount > 0 && (
          <SaveTag text={`${translate("common.save")} ${discount}%`} />
        )}
      </Box>
      <Typography
        sx={{
          color: "#64748B",
          textAlign: "start",
          fontSize: 14,
          fontWeight: 500,
        }}
      >
        ${singlePrice.toFixed(2)} / {translate(getDurationId(durationText))}
      </Typography>
    </Box>
  );
};

const SaveTag = ({ isSmall = false, text }) => {
  return (
    <Box
      sx={{
        background: "#F16425",
        color: "#fff",
        padding: isSmall ? "1px 4px" : "6px 12px",
        width: "max-content",
        borderRadius: 1,
      }}
    >
      <Typography
        sx={{
          fontSize: isSmall ? 10 : 12,
        }}
      >
        {text}
      </Typography>
    </Box>
  );
};
