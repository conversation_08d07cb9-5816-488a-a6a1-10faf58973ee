import useTranslate from "@/hooks/useTranslate";
import { Box } from "@mui/material";
import React from "react";

const PopularTag = () => {
  const { translate } = useTranslate();
  return (
    <Box
      sx={{
        background: "rgba(238, 159, 99, 1)",
        color: "#fff",
        fontSize: 12,
        borderRadius: 20,
        padding: "2px 10px",
        height: "fit-content",
      }}
    >
      {translate("ipc.popular")}
    </Box>
  );
};

export default PopularTag;
