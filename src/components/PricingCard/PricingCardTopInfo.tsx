import { Box, Typography } from "@mui/material";
import React from "react";
import LimitedDealTag from "./LimitedDealTag";
import PopularTag from "./PopularTag";

type PricingCardTopInfoProps = React.FC<{
  isLimited: Boolean;
  isPopular: Boolean;
}>;
const PricingCardTopInfo: PricingCardTopInfoProps = ({
  isLimited,
  isPopular,
}) => {
  return (
    <Box display="flex" flexDirection="row" justifyContent="space-between">
      {isLimited && <LimitedDealTag />}
      {isPopular && <PopularTag />}
    </Box>
  );
};

export default PricingCardTopInfo;
