import React, { useState } from "react";
import Autocomplete from "@mui/material/Autocomplete";
import TextField from "@mui/material/TextField";

const SearchBar = ({ events, handleSearch, helperText }) => {
  const [inputValue, setInputValue] = useState(""); // Added state for the input value

  return (
    <Autocomplete
      id="search-box"
      freeSolo
      options={events && events.map((option) => option.title)}
      value={inputValue} // Added value prop
      onInputChange={(event, newInputValue) => {
        setInputValue(newInputValue);
        handleSearch(event, newInputValue); // Updated the onInputChange to set the state and call handleSearch
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={helperText}
          variant="outlined"
          sx={{ width: "100%", marginBottom: { xs: "1rem", sm: "2rem" } }}
        />
      )}
    />
  );
};

export default SearchBar;
