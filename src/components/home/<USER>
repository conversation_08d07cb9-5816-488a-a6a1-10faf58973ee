import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Image from "next/image";
import { useSpring, animated, useInView } from "react-spring";

export default function ActivityCard({
  title,
  date,
  description,
  image,
  index,
}) {
  const [ref, springs] = useInView(
    () => ({
      from: { opacity: 0, transform: "translateY(50px)" },
      to: { opacity: 1, transform: "translateY(0px)" },
      config: { tension: 80, friction: 20 },
      once: true,
    }),
    { rootMargin: "-10% 0px" }
  );
  return (
    <Grid
      item
      xs={12}
      sm={6}
      sx={{
        padding: "10px",
      }}
    >
      <animated.div style={springs} ref={ref}>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            padding: "20px",
            outline: "2px solid",
            borderRadius: "8px",
            height: "100%",
          }}
        >
          <Box
            sx={{
              width: { xs: "100%", md: "180px" },
              position: "relative",
              height: { xs: "200px", md: "180px" },
              flexShrink: 0,
            }}
          >
            <Image
              fill
              src={image}
              alt="Card image"
              style={{
                objectFit: "cover",
                objectPosition: "center",
                borderRadius: "5px",
              }}
            />
          </Box>
          <Box
            sx={{
              paddingTop: { xs: "20px", md: "0px" },
              paddingLeft: { xs: "0px", md: "20px" },
              flexGrow: 1,
            }}
          >
            <Typography
              variant="h3"
              className="pb-2"
              sx={{
                fontWeight: "500",
                fontSize: "26px",
              }}
            >
              {title}
            </Typography>
            <Typography
              variant="body1"
              className="pb-2"
              sx={{
                fontWeight: "400",
                fontSize: { xs: "18px", sm: "20px" },
              }}
            >
              {date}
            </Typography>
            <Typography
              variant="body1"
              className="pb-2"
              sx={{
                fontWeight: "500",
                fontSize: { xs: "16px", sm: "16px" },
              }}
            >
              {description}
            </Typography>
          </Box>
        </Box>
      </animated.div>
    </Grid>
  );
}
