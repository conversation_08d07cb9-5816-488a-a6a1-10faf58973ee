import { antonioFont, barlowFont } from "@/constant/font";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";

export default function Banner() {
  return (
    <Box
      sx={{
        height: "100vh",
        maxHeight: "100vh",
        borderBottom: "1px solid",
        borderColor: "rgba(0, 0, 0, 0.5)",
        position: "relative",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundImage: "url('/assets/banner.webp')",
        backgroundPosition: "top",
        backgroundSize: "cover",
      }}
    >
      <div
        style={{
          position: "absolute",
          bottom: "20%",
          left: "10%",
          display: "flex",
          flexDirection: "column",
          gap: { md: "3rem", lg: "2rem" },
        }}
      >
        <Typography
          component="span"
          sx={{
            width: { xs: "50vw", md: "426px" },
            fontFamily: antonioFont,
            fontWeight: 400,
            flexShrink: 0,
            fontSize: { xs: "3.5rem", sm: "4.8rem", md: "5.9rem" },
            textAlign: "left",
            userSelect: "none",
            color: "white",
            lineHeight: { xs: "1.2", md: "162.28px" },
            textTransform: "uppercase",
            textShadow: "4px 4px 4px rgba(0, 0, 0, 0.3)",
          }}
        >
          We Are
        </Typography>
        <Typography
          component="span"
          sx={{
            width: { xs: "50vw", md: "426px" },
            fontFamily: antonioFont,
            fontWeight: 400,
            flexShrink: 0,
            fontSize: { xs: "3.5rem", sm: "4.8rem", md: "5.9rem" },
            textAlign: "left",
            userSelect: "none",
            color: "white",
            lineHeight: { xs: "1.2", md: "162.28px" },
            textTransform: "uppercase",
            textShadow: "4px 4px 4px rgba(0, 0, 0, 0.3)",
          }}
        >
          more
        </Typography>
        <Typography
          component="span"
          sx={{
            fontFamily: barlowFont,
            fontWeight: 700,
            flexShrink: 0,
            fontSize: { xs: "2.5rem", sm: "2.8rem", md: "5rem" },
            textAlign: "left",
            userSelect: "none",
            color: "white",
            textShadow: "4px 4px 4px rgba(0, 0, 0, 0.3)",
          }}
        >
          than a school
        </Typography>
      </div>
    </Box>
  );
}
