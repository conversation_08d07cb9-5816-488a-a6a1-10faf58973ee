import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import ActivityCard from "./ActivityCard";
import img from "../../../public/images/home/<USER>";
import { Container, But<PERSON> } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";
import useTranslate from "@/hooks/useTranslate";

const SeeMoreURL = "classes/experiencias-libres";

const getEarliestDate = (numDays) => {
  const today = new Date();
  const futureDate = new Date();
  futureDate.setDate(today.getDate() + numDays);

  return futureDate.toLocaleString("en-US", {
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
};

const activities = [
  {
    image: "/images/home/<USER>",
    title: "home-chocolate",
    numDays: 3,
    description: "home-chocolate-desc",
  },
  {
    image: "/images/home/<USER>",
    title: "home-movie-night",
    numDays: 5,
    description: "home-movie-night-desc",
  },
  {
    image: "/images/home/<USER>",
    title: "home-mezcal",
    numDays: 6,
    description: "home-mezcal-desc",
  },
  {
    image: "/images/home/<USER>",
    title: "home-trivia",
    numDays: 7,
    description: "home-trivia-desc",
  },
];

export default function ActivitiesSection() {
  const { translate } = useTranslate();
  const router = useRouter();

  const handleClick = () => {
    router.push(SeeMoreURL);
  };
  return (
    <Box
      sx={{
        padding: {
          xs: "60px 15px 30px 15px",
          sm: "60px 20px 60px 20px",
          md: "60px 30px 60px 30px",
          lg: "40px 25px 40px 25px",
        },
      }}
    >
      <Container>
        <Grid
          container
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {/* title/banner section */}
          <Grid
            className=" md:flex md:flex-col md:justify-start"
            item
            xs={12}
            md={4}
            sx={{
              flexDirection: "column",
              paddingBottom: { xs: "40px", md: "0px" },
              marginLeft: { xs: "0px", md: "10px" },
            }}
          >
            <Grid>
              <Typography
                variant="h3"
                sx={{
                  fontSize: "48px",
                  fontWeight: "500",
                  marginBottom: "30px",
                }}
              >
                {translate("home-activites")}
              </Typography>
            </Grid>
            <Grid>
              <Typography
                variant="h3"
                sx={{
                  fontSize: { xs: "24px", lg: "24px" },
                  fontWeight: "500",
                  marginBottom: "30px",
                  paddingRight: "0px",
                }}
              >
                {translate("home-activites-desc")}
              </Typography>
              <Button onClick={handleClick}>
                {translate("common.view-more")}
              </Button>
            </Grid>
          </Grid>

          <Grid
            item
            xs={12}
            md={7}
            sx={{
              aspectRatio: "2 / 1",
              position: "relative",
              marginRight: { xs: "0px", md: "10px" },
              height: { xs: "200px", sm: "400px", md: "350px" }, // Fixed height for the banner image
            }}
          >
            <Image
              src={img}
              blurDataURL={img}
              alt="Image of upcoming activities"
              fill
              style={{
                borderRadius: "8px",
                objectFit: "cover",
                objectPosition: "center bottom",
              }}
            />
          </Grid>

          {/* events container */}
          <Grid container spacing={2} sx={{ paddingTop: "30px" }}>
            {activities.map((m) => (
              <ActivityCard
                key={m.title}
                image={m.image}
                title={translate(m.title)}
                date={getEarliestDate(m.numDays)}
                description={translate(m.description)}
                index={activities.indexOf(m)}
              />
            ))}
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}
