import React, { useState } from "react";
import Container from "@mui/material/Container";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import PartnerCircles from "../PartnerCircles";

const images = [
  "/testLogo1.png",
  "/testLogo2.png",
  "/testLogo3.png",
  "/testLogo4.png",
  "/testLogo5.png",
];

export default function PartnerSection() {
  const [isHovered, setIsHovered] = useState(false);
  const [hoverStyle, setHoverStyle] = useState({});

  const handleMouseEnter = () => {
    setIsHovered(true);
    setHoverStyle({ boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.5)" });
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setHoverStyle({});
  };

  return (
    <Container
      sx={{
        borderBottom: "1px solid",
        borderColor: "rgba(0, 0, 0, 0.5)",
        padding: "40px 0 60px 0",
        minWidth: "100%",
        margin: "0",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          width: "100%",
          height: "100%",
          margin: { xs: "0 10px 40px 10px", sm: "15px 10px 50px 10px" },
        }}
      >
        <Typography
          component="span"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          sx={{
            fontWeight: 700,
            fontSize: { xs: "2.2rem", sm: "2.5rem", md: "2.7rem" },
            textAlign: "center",
            userSelect: "none",
          }}
        >
          {isHovered
            ? "Nuestras Empresas Colaboradoras"
            : "Our Partner Businesses"}
        </Typography>
      </Box>
      <div className="circlesContainer">
        <PartnerCircles images={images} originDirection={"right"} />
        <PartnerCircles images={images} originDirection={"left"} />
      </div>
    </Container>
  );
}
