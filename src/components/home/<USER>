import React, { useState, useEffect } from "react";
import { animated } from "react-spring";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { checkIfUserHasEverSignedIn } from "../../utils/clerkUtils";
import { Container } from "@mui/material";
import Link from "next/link";
import { useUserContext } from "@/contexts/UserContext";
import { barlowFont } from "@/constant/font";
import useTranslate from "@/hooks/useTranslate";

const wordOptions = [
  "Travelers",
  "Viajeros",
  "Explorers",
  "Nomads",
  "Nómadas",
  "Discovery",
  "Experiencia",
  "Students",
  "Estudiantes",
  "Connection",
  "Conexión",
  "Opportunity",
  "Oportunidad",
];

const signupBtnTxt = { eng: "Sign Up", esp: "Inscribirse" };
const waitlistBtnTxt = { eng: "Join Waitlist", esp: "Join Waitlist" };

const getRandomItem = (arr) => {
  const randomIndex = Math.floor(Math.random() * arr.length);
  return arr[randomIndex];
};

export default function HomeHeroSection({ goToSignup, user }) {
  const { user: isUserExists } = useUserContext();
  const { translate } = useTranslate();
  const [curWord, setCurWord] = useState("Travelers");
  const [isHovered, setIsHovered] = useState(false);
  const [hoverStyle, setHoverStyle] = useState({});
  const [userHasSignedIn, setUserHasSignedIn] = useState(false);
  const [letterIndex, setLetterIndex] = useState(0);
  const waitlistActive = false; // TODO: remove this once waitlist has ended

  const changeCurWords = () => {
    const newWord = getRandomItem(wordOptions);
    setCurWord(newWord);
    setLetterIndex(0);
  };

  useEffect(() => {
    let interval;

    if (letterIndex < curWord.length) {
      interval = setInterval(() => {
        setLetterIndex((prev) => prev + 1);
      }, 100); // Adjust the speed of the letter reveal here
    } else {
      // Wait 2 seconds before changing the word
      const timeout = setTimeout(() => {
        changeCurWords();
      }, 2000);

      return () => clearTimeout(timeout);
    }

    return () => clearInterval(interval);
  }, [curWord, letterIndex]);

  useEffect(() => {
    if (checkIfUserHasEverSignedIn()) {
      setUserHasSignedIn(true);
    }
  }, []);

  return (
    <Box
      sx={{
        padding: {
          xs: "30px 20px",
          sm: "45px 35px 60px",
          md: "65px 50px 100px",
        },
      }}
    >
      <Container
        sx={{
          maxWidth: { lg: "675px" },
        }}
      >
        <Box
          display="flex"
          justifyContent="center"
          flexWrap="wrap"
          sx={{ marginBottom: "25px", minHeight: { xs: "180px", sm: "100px" } }}
        >
          <Box
            sx={{
              width: { xs: "100%", sm: "auto" },
              display: "flex",
              justifyContent: "center",
            }}
          >
            <animated.span>
              <Typography
                component="span"
                sx={{
                  fontFamily: barlowFont,
                  fontWeight: 500,
                  flexShrink: 0,
                  fontSize: { xs: "57px", sm: "60px" },
                  textAlign: "center",
                  userSelect: "none",
                  color: "black",
                }}
              >
                {isHovered ? "Español Para" : "Spanish For"}
              </Typography>
            </animated.span>
          </Box>
          <Box>
            <animated.span>
              <Typography
                component="span"
                color="#F4B357"
                sx={{
                  fontFamily: barlowFont,
                  fontWeight: 500,
                  fontSize: { xs: "57px", sm: "60px" },
                  textAlign: "center",
                  marginLeft: "10px",
                  userSelect: "none",
                  textShadow: "2px 2px 2px rgba(0, 0, 0, 0.1)",
                }}
              >
                {curWord.substring(0, letterIndex)}
              </Typography>
            </animated.span>
          </Box>
        </Box>
        <Typography
          variant="body1"
          sx={{
            fontSize: { xs: "1rem", sm: "1.4rem", md: "20px" },
            fontWeight: "500",
            textAlign: "center",
            userSelect: "none",
          }}
        >
          {translate("home.mission-details")}
        </Typography>

        {!isUserExists && (
          <>
            {waitlistActive ? (
              <Box sx={{ textAlign: "center", margin: "50px 20px 0px" }}>
                <Link href="/waitlist" passHref>
                  <Button
                    component="a"
                    sx={{
                      fontWeight: 600,
                      fontSize: "1.2rem",
                      color: "white",
                      width: "160px",
                      borderRadius: "10px",
                      backgroundColor: "#F3B358",
                      border: "none",
                      textDecoration: "none",
                      "&:hover": {
                        backgroundColor: "#02AC9F",
                      },
                    }}
                  >
                    {isHovered ? waitlistBtnTxt.esp : waitlistBtnTxt.eng}
                  </Button>
                </Link>
              </Box>
            ) : (
              !userHasSignedIn && (
                <Box sx={{ textAlign: "center", margin: "50px 20px 0px" }}>
                  <Button
                    onClick={goToSignup}
                    sx={{
                      fontWeight: 600,
                      fontSize: "1.2rem",
                      color: "white",
                      width: "160px",
                      borderRadius: "10px",
                      backgroundColor: "#F3B358",
                      border: "none",
                      "&:hover": {
                        backgroundColor: "#02AC9F",
                      },
                    }}
                  >
                    {translate("common.signup")}
                  </Button>
                </Box>
              )
            )}
          </>
        )}
      </Container>
    </Box>
  );
}
