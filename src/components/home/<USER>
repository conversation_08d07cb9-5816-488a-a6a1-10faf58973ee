import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { Modal, Box, Typography } from "@mui/material";
import CustomButton from "../CustomButton";
import Image from "next/image";
import { useUserContext } from "@/contexts/UserContext";
import { LEARNING_LANGUAGE } from "@/constant/Enums";
import { LEARN_COOKIE_NAME } from "@/constant/classes";
import useTranslate from "@/hooks/useTranslate";
import { getSiteLanguageBasedOnLearningLanguage } from "@/utils/common";
import { useSnackbar } from "@/hooks/useSnackbar";

export default function InitialIdentificationModal() {
  const { user, isLoaded } = useUserContext();
  const { setPreferredLanguage } = useTranslate();
  const { showSnackbar } = useSnackbar();

  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (!user && isLoaded) {
      const lang = Cookies.get(LEARN_COOKIE_NAME);
      if (lang) {
        setPreferredLanguage(getSiteLanguageBasedOnLearningLanguage(lang));
      } else {
        setOpen(true);
      }
    }
  }, [user, isLoaded]);

  const handleSelect = (lang: string) => {
    Cookies.set(LEARN_COOKIE_NAME, lang, { expires: 365 });
    setPreferredLanguage(getSiteLanguageBasedOnLearningLanguage(lang));
    setOpen(false);
    const isAuthPages =
      window.location.pathname.includes("/sign-in") ||
      window.location.pathname.includes("/sign-up");
    if (isAuthPages) {
      window.location.reload();
      showSnackbar(`Applying your language preference.`, {
        type: "info",
      });
    }
  };

  return (
    <Modal open={open}>
      <Box
        sx={{
          bgcolor: "white",
          p: 4,
          mx: "auto",
          mt: "20%",
          maxWidth: 400,
          borderRadius: 2,
          boxShadow: 4,
          textAlign: "center",
        }}
      >
        <Typography fontSize="1.2rem" fontWeight={700} mb={2}>
          Select your preference
        </Typography>
        <Typography fontSize="0.9rem" color="text.secondary" sx={{ mb: 3 }}>
          Choose the language you’d like to learn so we can personalize your
          experience and show the most relevant content.
        </Typography>
        <Image height={150} width={150} alt="patito" src="/patitoB.png" />
        <Box display="flex" flexDirection="column" width="100%" mt={2}>
          <CustomButton
            text="I want to learn Spanish"
            onClick={() => handleSelect(LEARNING_LANGUAGE.SPANISH)}
            sx={{
              width: "100%",
            }}
          />
          <CustomButton
            colortype="secondary"
            text="Quiero aprender ingles"
            onClick={() => handleSelect(LEARNING_LANGUAGE.ENGLISH)}
            sx={{
              width: "100%",
              mt: 2,
            }}
          />
        </Box>
      </Box>
    </Modal>
  );
}
