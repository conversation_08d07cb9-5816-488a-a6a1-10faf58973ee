import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import ResponsiveCard from "@/components/Card";
import { Container } from "@mui/material";
import { barlowFont } from "@/constant/font";
import useTranslate from "@/hooks/useTranslate";

const white = "#FFFFFF";

const responsiveCards = [
  {
    defaultLanguage: "eng",
    color: white,
    titleColor: white,
    title: "home.practicle-skills",
    text: "home.practicle-skills-desc",
    objectPosition: "center",
    image: "/images/home/<USER>",
    index: 2,
  },
  {
    defaultLanguage: "eng",
    color: white,
    titleColor: white,
    text: "home.passion-desc",
    title: "home.passion",
    objectPosition: "bottom",
    image: "/images/home/<USER>",
    index: 1,
  },
  {
    defaultLanguage: "eng",
    color: white,
    titleColor: white,
    text: "home.guided-immersion-desc",
    title: "home.guided-immersion",
    objectPosition: "center",
    image: "/images/home/<USER>",
    index: 0,
  },
];

export default function CardsContainerSection() {
  const { translate } = useTranslate();
  return (
    <Box
      sx={{
        width: "100%",
        borderColor: "rgba(0, 0, 0, 0.5)",
        backgroundColor: "#F2F2F2",
        padding: {
          xs: "40px 0px 40px 0px",
          sm: "40px 0px 65px 0px",
          md: "40px 0px 80px 0px",
        },
      }}
    >
      <Container>
        <Box
          flexWrap="wrap"
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: "100%",
            height: "100%",
            margin: { xs: "0 0px 40px 0px", sm: "15px 10px 50px 10px" },
          }}
        >
          <Typography
            component="span"
            sx={{
              fontFamily: barlowFont,
              fontWeight: 500,
              fontSize: "48px",
              textAlign: "center",
              // marginLeft: { sm: "20px", md: "24px" },
              flexShrink: 0,
              textShadow: "2px 2px 2px rgba(0, 0, 0, 0.1)",
            }}
          >
            ¿Why Patito Feo?
          </Typography>
        </Box>
        <Grid
          container
          spacing={5}
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "stretch",
          }}
        >
          {responsiveCards.map((m, i) => (
            <ResponsiveCard
              key={i}
              color={m.color}
              titleColor={m.titleColor}
              objectPosition={m.objectPosition}
              image={m.image}
              index={m.index}
              text={translate(m.text)}
              title={translate(m.title)}
            />
          ))}
        </Grid>
      </Container>
    </Box>
  );
}
