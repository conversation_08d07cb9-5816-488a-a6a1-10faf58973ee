import useTranslate from "@/hooks/useTranslate";
import { Box, Card, SxProps, Theme, Typography } from "@mui/material";
import React from "react";

type PricingTabProps = React.FC<{
  tabs: {
    id: number;
    name: string;
  }[];
  active: number;
  onClick: (data: any) => void;
  tabStyle?: SxProps<Theme>;
  contStyle?: SxProps<Theme>;
}>;

const activeStyle: SxProps<Theme> = {
  background: "#14A79C",
  boxShadow: "0px 8px 10px 0px rgba(0, 0, 0, 0.05)",
  color: "#fff",
};

const inactiveStyle: SxProps<Theme> = {
  background: "#E1FEFC",
  boxShadow: "none",
};

const PricingTab: PricingTabProps = ({
  tabs,
  active,
  onClick,
  tabStyle = {},
  contStyle = {},
}) => {
  const { translate } = useTranslate();
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        background: "#E1FEFC",
        width: "100%",
        padding: 2,
        borderRadius: 10,
        gap: 2,
        ...contStyle,
      }}
    >
      {tabs.map((m, i) => (
        <Card
          sx={{
            ...(m.id === active ? activeStyle : inactiveStyle),
            height: 50,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            width: "50%",
            borderRadius: 10,
            cursor: "pointer",
            ...tabStyle,
          }}
          key={i}
          onClick={() => {
            onClick(m);
            // setActive(m);
          }}
        >
          <Typography
            sx={{
              color: m.id === active ? "#fff" : "#000",
              margin: 0,
              padding: 0,
            }}
          >
            {translate(m.name)}
          </Typography>
        </Card>
      ))}
    </Box>
  );
};

export default PricingTab;
