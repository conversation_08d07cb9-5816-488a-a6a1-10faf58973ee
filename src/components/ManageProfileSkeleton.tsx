import React from 'react';
import {
  Box,
  Container,
  Typography,
  Skeleton,
  Paper,
  styled,
} from '@mui/material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(10),
  marginTop: theme.spacing(10),
  borderRadius: '20px',
  boxShadow: 'none',
  border: '1px solid #D3D3D3',
}));

const ProfileImageSkeleton = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(8),
  marginBottom: theme.spacing(8),
}));

const GoalCardSkeleton = styled(Box)(({ theme }) => ({
  width: 120,
  height: 120,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  border: '1px solid #E0E0E0',
  borderRadius: '12px',
}));

const InterestChipSkeleton = styled(Box)(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  padding: '8px 16px',
  borderRadius: '100px',
  border: '1px solid #E0E0E0',
  backgroundColor: '#fff',
}));

const CategoryButtonSkeleton = styled(Box)(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  gap: '8px',
  padding: '8px 24px',
  borderRadius: '100px',
  minWidth: '140px',
}));

const DropAreaSkeleton = styled(Box)({
  flex: 1,
  marginLeft: '32px',
});

const CategoryRowSkeleton = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  marginBottom: '12px',
  paddingBottom: '12px',
  borderBottom: '1px solid #EAEAEA',
  '&:last-child': {
    marginBottom: 0,
    paddingBottom: '12px',
  },
});

function InformationEditGroupSkeleton({
  index,
  title,
}: {
  index: string | number;
  title: string;
}) {
  return (
    <Box sx={{ mb: 9 }}>
      <Box sx={{ display: `flex`, alignItems: `center`, gap: 4, mb: 7 }}>
        <Skeleton
          variant='circular'
          width={28}
          height={28}
          sx={{ border: '1px solid #000' }}
        />
        <Typography variant='h6' sx={{ fontWeight: 700 }}>
          {title}
        </Typography>
      </Box>
      <Skeleton
        variant='rectangular'
        height={56}
        sx={{ borderRadius: '4px' }}
      />
    </Box>
  );
}

const ManageProfileSkeleton = () => {
  return (
    <Container maxWidth='md'>
      <Typography
        variant='h4'
        align='center'
        gutterBottom
        sx={{ marginTop: '30px', fontWeight: 500 }}
      >
        Profile
      </Typography>

      <StyledPaper>
        {/* Profile Image Section */}
        <ProfileImageSkeleton>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Skeleton variant='text' width={120} height={24} />
            <Skeleton variant='circular' width={160} height={160} />
          </Box>
          <Box sx={{ display: 'flex', gap: 2, mt: 6 }}>
            <Skeleton variant='rectangular' width={120} height={40} />
            <Skeleton variant='rectangular' width={120} height={40} />
          </Box>
        </ProfileImageSkeleton>

        <StyledPaper>
          {/* Name Section */}
          <InformationEditGroupSkeleton index='1' title='Name' />

          {/* Location Section */}
          <InformationEditGroupSkeleton index='2' title='Location' />

          {/* Language Section */}
          <InformationEditGroupSkeleton
            index='3'
            title='What Language Do You Want To Study?'
          />

          {/* Proficiency Section */}
          <InformationEditGroupSkeleton index='4' title='Proficiency Level' />

          {/* Goals Section */}
          <Box sx={{ mb: 9 }}>
            <Box sx={{ display: `flex`, alignItems: `center`, gap: 4, mb: 7 }}>
              <Skeleton
                variant='circular'
                width={28}
                height={28}
                sx={{ border: '1px solid #000' }}
              />
              <Typography variant='h6' sx={{ fontWeight: 700 }}>
                Goals
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 2 }}>
              {[1, 2, 3, 4, 5].map((index) => (
                <GoalCardSkeleton key={index}>
                  <Skeleton variant='text' width={40} height={40} />
                  <Skeleton variant='text' width={80} height={20} />
                </GoalCardSkeleton>
              ))}
            </Box>
          </Box>

          {/* Interests Section */}
          <Box sx={{ mb: 9 }}>
            <Box sx={{ display: `flex`, alignItems: `center`, gap: 4, mb: 7 }}>
              <Skeleton
                variant='circular'
                width={28}
                height={28}
                sx={{ border: '1px solid #000' }}
              />
              <Typography variant='h6' sx={{ fontWeight: 700 }}>
                Interests
              </Typography>
            </Box>

            {/* Available Interests */}
            <Box sx={{ display: 'flex', gap: 2, mb: 4 }}>
              {[1, 2, 3, 4, 5].map((index) => (
                <InterestChipSkeleton key={index}>
                  <Skeleton variant='circular' width={24} height={24} />
                  <Skeleton variant='text' width={80} height={20} />
                </InterestChipSkeleton>
              ))}
            </Box>

            {/* Love it Category */}
            <CategoryRowSkeleton>
              <CategoryButtonSkeleton>
                <Skeleton variant='text' width={60} height={20} />
              </CategoryButtonSkeleton>
              <DropAreaSkeleton>
                <Skeleton variant='text' width={200} height={20} />
              </DropAreaSkeleton>
            </CategoryRowSkeleton>

            {/* Neutral Category */}
            <CategoryRowSkeleton>
              <CategoryButtonSkeleton>
                <Skeleton variant='text' width={60} height={20} />
              </CategoryButtonSkeleton>
              <DropAreaSkeleton>
                <Skeleton variant='text' width={200} height={20} />
              </DropAreaSkeleton>
            </CategoryRowSkeleton>

            {/* Hate it Category */}
            <CategoryRowSkeleton>
              <CategoryButtonSkeleton>
                <Skeleton variant='text' width={60} height={20} />
              </CategoryButtonSkeleton>
              <DropAreaSkeleton>
                <Skeleton variant='text' width={200} height={20} />
              </DropAreaSkeleton>
            </CategoryRowSkeleton>
          </Box>

          {/* Contact Info Section */}
          <InformationEditGroupSkeleton index='7' title='Contact Info' />

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Skeleton variant='rectangular' width={120} height={40} />
            <Skeleton variant='rectangular' width={120} height={40} />
          </Box>
        </StyledPaper>
      </StyledPaper>
    </Container>
  );
};

export default ManageProfileSkeleton;
