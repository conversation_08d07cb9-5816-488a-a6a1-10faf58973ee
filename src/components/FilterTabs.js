import React from "react";
import PropTypes from "prop-types";
// import { makeStyles } from '@mui/material/styles';
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Typography from "@mui/material/Typography";
import SearchIcon from "@mui/icons-material/Search";
import Box from "@mui/material/Box";

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`scrollable-auto-tabpanel-${index}`}
      aria-labelledby={`scrollable-auto-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box p={3}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
};

function a11yProps(index) {
  return {
    id: `scrollable-auto-tab-${index}`,
    "aria-controls": `scrollable-auto-tabpanel-${index}`,
  };
}

export default function ScrollableTabsButtonAuto({
  selectFilterType,
  handleShowSearch,
  isAuth,
}) {
  const [value, setValue] = React.useState("All");

  const handleChange = (event, newValue) => {
    setValue(newValue);
    selectFilterType(newValue);
  };

  return (
    <>
      <Tabs
        className=""
        value={value}
        onChange={handleChange}
        indicatorColor="primary"
        textColor="primary"
        variant="scrollable"
        scrollButtons="auto"
        aria-label="scrollable auto tabs example"
      >
        <Tab
          label="Suggested"
          disabled={!isAuth}
          value="Suggested"
          {...a11yProps(0)}
        />
        <Tab label="All" value="All" {...a11yProps(1)} />
        <Tab
          label="Favorites"
          disabled={!isAuth}
          value="Favorites"
          {...a11yProps(2)}
        />
        <div className="" style={{ display: "flex", alignItems: "center" }}>
          <SearchIcon className="" onClick={handleShowSearch} />
        </div>
      </Tabs>
    </>
  );
}
