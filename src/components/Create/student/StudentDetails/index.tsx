import { Box } from "@mui/material";
import React from "react";
import DocumentSigned from "./DocumentSigned";
import StudentExtPage from "./StudentExtPage";
import StudentClasses from "./StudentClasses";
import { ClassesIcon, CommunityIcon } from "./Icons";
import StudentTopCards from "./StudentTopCards";
import { UserType } from "@/api/mongoTypes";

type AllStudentDetailsProps = React.FC<{ studentInfo: UserType }>;
const AllStudentDetails: AllStudentDetailsProps = ({ studentInfo }) => {
  return (
    <Box width="100%">
      <StudentTopCards studentInfo={studentInfo} />

      <Box
        width="100%"
        display="flex"
        mt={6}
        mb={5}
        flexDirection="row"
        flexWrap="wrap"
      >
        <Box
          sx={{
            pr: {
              xs: 0,
              md: 2,
            },
            width: {
              xs: "100%",
              md: "50%",
            },
          }}
        >
          <StudentClasses
            isEvent={false}
            userId={studentInfo?._id}
            icon={<ClassesIcon size={16} />}
            headerText="Classes"
          />
        </Box>
        <Box
          sx={{
            pl: {
              xs: 0,
              md: 2,
            },
            width: {
              xs: "100%",
              md: "50%",
            },
          }}
        >
          <StudentClasses
            isEvent
            userId={studentInfo?._id}
            icon={<CommunityIcon size={18} />}
            headerText="Community Experience"
          />
        </Box>
      </Box>

      {studentInfo?.notionUrl && (
        <Box width="100%" display="flex" flexDirection="row" flexWrap="wrap">
          {/* <DocumentSigned /> */}
          <StudentExtPage notionUrl={studentInfo.notionUrl} />
        </Box>
      )}
    </Box>
  );
};

export default AllStudentDetails;
