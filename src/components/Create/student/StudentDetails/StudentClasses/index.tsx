/* eslint-disable react/no-unescaped-entities */
import React, { useEffect, useMemo, useState } from "react";
import { startOfMonth, endOfMonth } from "date-fns";
import Tabs from "./Tabs";
import { Box, CircularProgress, Typography } from "@mui/material";
import CalenderSelect from "./CalenderSelect";
import Filter from "./Filter";
import StudentClassCard from "./StudentClassCard";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import Loading from "./Loading";
import InfiniteScroll from "react-infinite-scroll-component";
import { CLASSES_SORT, CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import axios from "axios";
import { CartType } from "@/api/mongoTypes";
import ScheduleCard from "@/components/Profile/schedule/ScheduleCard";
import {
  PreviousFilterTypes,
  StudentFetchClassesType,
  UpcomingFilterTypes,
} from "@/constant/student";
import { groupDataByDate } from "@/utils/listLogic";

const LIMIT = 10;

function getMonthDateRange(year, month) {
  const date = new Date(year, month);
  const firstDate = startOfMonth(date);
  const lastDate = endOfMonth(date);
  return {
    firstDate,
    lastDate,
  };
}

const getIsUpComing = ({ currentYear, selectedDuration, currentMonth }) => {
  if (currentYear === selectedDuration.year) {
    if (selectedDuration.month > currentMonth) {
      return true;
    }
    return false;
  }
  return null;
};

const getIsPast = ({ currentYear, selectedDuration, currentMonth }) => {
  if (selectedDuration.year < currentYear) {
    return true;
  }
  if (currentYear === selectedDuration.year) {
    if (selectedDuration.month < currentMonth) {
      return true;
    }
    return false;
  }
  return false;
};

type StudentClassesProps = React.FC<{
  headerText: string;
  userId: string;
  icon: React.ReactNode;
  isEvent: boolean;
}>;
const StudentClasses: StudentClassesProps = ({
  headerText,
  icon,
  isEvent = false,
  userId,
}) => {
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [data, setData] = useState<CartType[] | []>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [active, setActive] = useState<CLASSESS_FETCH_DURATION>(
    CLASSESS_FETCH_DURATION.UPCOMING
  );
  const [selectedDuration, setSelectedDuration] = useState({
    month: currentMonth,
    year: currentYear,
  });
  const [hasMore, setHasMore] = useState(false);

  const fetchData = async ({ skip, userId, selectedDuration, active }) => {
    const handleError = () => {
      setIsLoading(false);
      showSnackbar(
        `Failed to fetch the ${isEvent ? "Community Experiences" : "Classess"}`,
        { type: "error" }
      );
    };
    try {
      if (skip === 0) {
        setIsLoading(true);
      }
      const { firstDate, lastDate } = getMonthDateRange(
        selectedDuration.year,
        selectedDuration.month
      );
      const currentDate = new Date();
      const params = {
        limit: LIMIT,
        skip,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        currentDate,
        isMyClassesOnly: true,
        timeFilter: active,
        filters: isEvent
          ? CLASSES_SORT.COMMUNITY
          : CLASSES_SORT.IN_PERSON_CLASS,
        from: firstDate,
        to: lastDate,
      };
      const { data: respData } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/schedule/my-schedules`,
        {
          params,
          headers: {
            userid: userId,
          },
        }
      );
      if (respData.success) {
        const groupedData = groupDataByDate({
          data: respData?.data,
          active,
          isAscending: true,
          baseDate: new Date(),
          from: firstDate,
          to: lastDate,
        }) as any[];
        if (skip === 0) {
          setData(groupedData);
        } else {
          setData((prev) => [...prev, ...groupedData]);
        }
        setSkip(+skip + LIMIT);
        setHasMore(+respData.data.length >= LIMIT);
      } else {
        handleError();
      }
      setIsLoading(false);
    } catch (error) {
      console.error(error);
      handleError();
    }
  };

  useEffect(() => {
    if (userId) {
      fetchData({
        skip: 0,
        userId,
        selectedDuration,
        active,
      });
    }
  }, [userId, selectedDuration, active]);

  const isUpcoming = useMemo(() => {
    return getIsUpComing({
      currentMonth,
      currentYear,
      selectedDuration,
    });
  }, [currentMonth, currentYear, selectedDuration]);

  const isPast = useMemo(() => {
    return getIsPast({
      currentMonth,
      currentYear,
      selectedDuration,
    });
  }, [currentMonth, currentYear, selectedDuration]);

  return (
    <Box
      sx={{
        borderRadius: 2,
        border: "1px solid #EDEDED",
        p: 3,
        width: "100%",
        display: "flex",
        flexDirection: "column",
        gap: 3,
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="start"
        gap={1}
        sx={{
          borderBottom: "1px solid #F5F5F5",
          pb: 2,
        }}
      >
        {icon}
        <Typography fontWeight={700} fontSize={14}>
          {headerText}
        </Typography>
      </Box>

      <Box width="100%" aria-disabled={isLoading}>
        <CalenderSelect
          selectedMonth={selectedDuration}
          setSelectedMonth={setSelectedDuration}
          sx={{ my: 2 }}
        />
        <Tabs
          isEvent={isEvent}
          active={active}
          isUpcoming={isUpcoming}
          isPast={isPast}
          setActive={setActive}
        />
        {/* <Filter
          selectedFilter={selectedFilter}
          setSelectedFilter={setSelectedFilter}
        /> */}
      </Box>
      <Box sx={{ height: 400, overflowY: "scroll" }}>
        {isLoading ? (
          <Loading />
        ) : (
          <>
            <InfiniteScroll
              dataLength={data.length}
              next={() =>
                fetchData({
                  skip,
                  userId,
                  selectedDuration,
                  active,
                })
              }
              hasMore={hasMore}
              loader={
                <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
                  <CircularProgress size={24} />
                </Box>
              }
              endMessage={
                <Box
                  sx={{ textAlign: "center", p: 2, color: "text.secondary" }}
                >
                  <Typography fontSize={12}>You've seen all</Typography>
                </Box>
              }
              scrollableTarget="scrollableDiv"
            >
              {/* {data.map((m) => (
                <>
                  {m.data.map((j) => (
                    <ScheduleCard
                      info={j}
                      key={j.id}
                      isSmaller
                      cardDate={m.date}
                      active={CLASSESS_FETCH_DURATION.ALL}
                    />
                  ))}
                </>
              ))} */}
              {data.map((m) => (
                <>
                  {m.data.map((j) => (
                    <StudentClassCard
                      key={j.id}
                      data={j}
                      cardDate={m.date}
                      isEvent={isEvent}
                      active={CLASSESS_FETCH_DURATION.ALL}
                    />
                  ))}
                </>
              ))}
              {/* {data.map((m) => (
              <ScheduleCard
                info={m}
                key={m.id}
                isSmaller
                active={CLASSESS_FETCH_DURATION.ALL}
              />
            ))} */}
            </InfiniteScroll>
          </>
        )}
      </Box>
    </Box>
  );
};

export default StudentClasses;
