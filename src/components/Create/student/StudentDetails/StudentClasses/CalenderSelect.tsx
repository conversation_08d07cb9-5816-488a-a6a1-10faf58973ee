import PrevNextIcon from "@/components/Profile/dashboard/CalenderClass/PrevNext";
import { Box, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";

// Array of month names for display
const monthNames = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const CalenderSelect = ({ selectedMonth, setSelectedMonth, sx = {} }) => {
  const currentDate = new Date();
  const [monthYear, setMonthYear] = useState({
    month: selectedMonth?.month ?? currentDate.getMonth(),
    year: selectedMonth?.year ?? currentDate.getFullYear(),
  });

  // Update monthYear display when selectedMonth changes
  useEffect(() => {
    if (selectedMonth) {
      setMonthYear({
        month: selectedMonth.month,
        year: selectedMonth.year,
      });
    }
  }, [selectedMonth]);

  const handlePreviousMonth = () => {
    setSelectedMonth((prev) => {
      const newMonth = prev.month === 0 ? 11 : prev.month - 1;
      const newYear = prev.month === 0 ? prev.year - 1 : prev.year;
      return { month: newMonth, year: newYear };
    });
  };

  const handleNextMonth = () => {
    setSelectedMonth((prev) => {
      const newMonth = prev.month === 11 ? 0 : prev.month + 1;
      const newYear = prev.month === 11 ? prev.year + 1 : prev.year;
      return { month: newMonth, year: newYear };
    });
  };

  const displayMonthYear = `${monthNames[monthYear.month]} ${monthYear.year}`;

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        ...sx,
      }}
    >
      <PrevNextIcon isPrev onClick={handlePreviousMonth} />
      <Typography
        fontSize={16}
        fontWeight={600}
        variant="h6"
        color="rgba(51, 51, 51, 1)"
      >
        {displayMonthYear}
      </Typography>
      <PrevNextIcon isPrev={false} onClick={handleNextMonth} />
    </Box>
  );
};

export default CalenderSelect;
