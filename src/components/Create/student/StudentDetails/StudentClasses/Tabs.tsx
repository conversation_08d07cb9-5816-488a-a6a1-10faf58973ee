import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import { StudentFetchClassesType } from "@/constant/student";
import { Box, Typography } from "@mui/material";
import React, { useEffect } from "react";

type TabsProps = React.FC<{
  active: CLASSESS_FETCH_DURATION;
  isEvent: boolean;
  isPast: boolean;
  isUpcoming: boolean;
  setActive: React.Dispatch<React.SetStateAction<CLASSESS_FETCH_DURATION>>;
}>;
const Tabs: TabsProps = ({
  active,
  setActive,
  isEvent,
  isUpcoming,
  isPast,
}) => {
  let tabs = [
    {
      id: CLASSESS_FETCH_DURATION.UPCOMING,
      name: isEvent ? "Upcoming Events" : "Upcoming Classes",
    },
    {
      id: CLASSESS_FETCH_DURATION.PAST,
      name: isEvent ? "Past Events" : "Past Classes",
    },
  ];

  if (isUpcoming) {
    tabs = tabs.filter((f) => f.id === CLASSESS_FETCH_DURATION.UPCOMING);
  }
  if (isPast) {
    tabs = tabs.filter((f) => f.id === CLASSESS_FETCH_DURATION.PAST);
  }

  useEffect(() => {
    if (isUpcoming) {
      setActive(CLASSESS_FETCH_DURATION.UPCOMING);
    }
    if (isPast) {
      setActive(CLASSESS_FETCH_DURATION.PAST);
    }
  }, [isUpcoming, isPast]);

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      my={2}
      sx={{
        border: "1px solid #14A79C",
        borderRadius: 1,
      }}
    >
      {tabs.map((m) => {
        const isActive = m.id === active;
        return (
          <Box
            onClick={() => {
              setActive(m.id);
            }}
            key={m.id}
            sx={{
              borderRadius: 1,
              color: isActive ? "#fff" : "#000000",
              backgroundColor: isActive ? "#14A79C" : "#fff",
              width: tabs.length !== 1 ? "50%" : "100%",
              padding: 2,
              cursor: "pointer",
            }}
          >
            <Typography fontSize="0.85rem" textAlign="center">
              {m.name}
            </Typography>
          </Box>
        );
      })}
    </Box>
  );
};

export default Tabs;
