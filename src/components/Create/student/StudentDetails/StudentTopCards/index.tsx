import { Box } from "@mui/material";
import React, { useMemo } from "react";
import Info from "./Info";
import StudentLearningLanguage from "./StudentLearningLanguage";
// import Goals from "./Goals";
import AdditionalLanguages from "./AdditionalLanguages";
import Interests from "@/components/Profile/dashboard/UserDetails/Interests";
import Goals from "@/components/Profile/dashboard/UserDetails/Goals";
import { UserType } from "@/api/mongoTypes";

type StudentTopCardsProps = React.FC<{ studentInfo: UserType }>;
const StudentTopCards: StudentTopCardsProps = ({ studentInfo }) => {
  const goals = useMemo(() => {
    if (studentInfo) {
      return studentInfo.goals;
    }
    return [];
  }, [studentInfo]);

  const interests = useMemo(() => {
    if (studentInfo) {
      return studentInfo.interest;
    }
    return [];
  }, [studentInfo]);

  return (
    <Box width="100%" display="flex" flexDirection="column">
      <Box
        width="100%"
        display="flex"
        flexDirection="row"
        flexWrap="wrap"
        mb={4}
      >
        <Box
          sx={{
            width: {
              xs: "100%",
              md: "50%",
            },
            pr: {
              xs: 0,
              md: 2,
            },
            rowGap: 5,
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Info studentInfo={studentInfo} />
          <StudentLearningLanguage studentInfo={studentInfo} />
        </Box>
        <Box
          sx={{
            width: {
              xs: "100%",
              md: "50%",
            },
            rowGap: 5,
            display: "flex",
            flexDirection: "column",
            pl: {
              xs: 0,
              md: 2,
            },
            mt: {
              xs: 4,
              md: 0,
            },
          }}
        >
          <Goals
            sx={{
              p: 2,
              border: "1px solid #E6E6E6",
              borderRadius: 2,
            }}
            goals={goals}
          />
          <Interests
            selectedInterests={interests}
            sx={{
              p: 2,
              border: "1px solid #E6E6E6",
              borderRadius: 2,
            }}
          />
        </Box>
      </Box>

      <AdditionalLanguages studentInfo={studentInfo} />
    </Box>
  );
};

export default StudentTopCards;
