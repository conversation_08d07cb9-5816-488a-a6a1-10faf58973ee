/* eslint-disable react/no-unescaped-entities */
import React from "react";
import InfoCard from "../InfoCard";
import { StudentHatIcon } from "../Icons";
import { Box } from "@mui/material";
import NameValue from "./NameValue";
import { UserType } from "@/api/mongoTypes";
import {
  getUserStudyingLanguage,
  getUserStudyingLanguageProficiency,
} from "@/utils/format";

type StudentLearningLanguageProps = React.FC<{ studentInfo: UserType }>;
const StudentLearningLanguage: StudentLearningLanguageProps = ({
  studentInfo,
}) => {
  return (
    <InfoCard
      icon={<StudentHatIcon />}
      headerText="Student’s Learning Language"
    >
      <Box width="100%" display="flex" flexDirection="column">
        <NameValue
          widthLeft={40}
          name="Language"
          value={getUserStudyingLanguage(studentInfo)}
        />
        <NameValue
          widthLeft={40}
          name="Student-stated level"
          value={getUserStudyingLanguageProficiency(studentInfo)}
        />
        <NameValue
          widthLeft={40}
          name="Current proficiency level"
          value={studentInfo.level}
        />
      </Box>
    </InfoCard>
  );
};

export default StudentLearningLanguage;
