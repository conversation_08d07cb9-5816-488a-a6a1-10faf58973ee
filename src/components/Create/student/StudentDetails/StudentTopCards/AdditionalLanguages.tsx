import React, { useMemo } from "react";
import InfoCard from "../InfoCard";
import { Box, Typography } from "@mui/material";
import { LnaguageIcon } from "../Icons";
import NameValue from "./NameValue";
import { UserType } from "@/api/mongoTypes";
import { getProficiencyList, getUserStudyingLanguage } from "@/utils/format";

type AdditionalLanguagesProps = React.FC<{ studentInfo: UserType }>;
const AdditionalLanguages: AdditionalLanguagesProps = ({ studentInfo }) => {
  const list = useMemo(() => {
    const languages = studentInfo.languages;
    return getProficiencyList(languages as any);
  }, [studentInfo]);

  return (
    <InfoCard icon={<LnaguageIcon />} headerText="Additional Languages">
      <Box width="100%" display="flex" flexDirection="column">
        {list?.length > 0 ? (
          <>
            {list.map((m, i) => (
              <NameValue
                key={i}
                widthLeft={30}
                name={m.proficiency}
                value={m.languages}
              />
            ))}
          </>
        ) : (
          <Typography fontSize={14} color="rgba(109, 109, 109, 1)">
            Not selected
          </Typography>
        )}
      </Box>
    </InfoCard>
  );
};

export default AdditionalLanguages;
