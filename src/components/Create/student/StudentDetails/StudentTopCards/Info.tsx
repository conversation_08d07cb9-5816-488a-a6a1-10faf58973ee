import React from "react";
import InfoCard from "../InfoCard";
import { InfoIcon } from "../Icons";
import { Box } from "@mui/material";
import NameValue from "./NameValue";
import { UserType } from "@/api/mongoTypes";
import { getUserCountry, getUserFullName } from "@/utils/format";

type InfoProps = React.FC<{ studentInfo: UserType }>;
const Info: InfoProps = ({ studentInfo }) => {
  return (
    <InfoCard icon={<InfoIcon />} headerText="Info">
      <Box width="100%" display="flex" flexDirection="column">
        <NameValue
          widthLeft={30}
          name="Name"
          value={getUserFullName(studentInfo)}
        />
        <NameValue
          widthLeft={30}
          name="Country"
          value={getUserCountry(studentInfo)}
        />
        <NameValue widthLeft={30} name="Email id" value={studentInfo.email} />
      </Box>
    </InfoCard>
  );
};

export default Info;
