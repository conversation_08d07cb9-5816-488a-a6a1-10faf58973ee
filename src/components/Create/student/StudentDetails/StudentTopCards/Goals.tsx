import React from "react";
import InfoCard from "../InfoCard";
import { GoalIcon } from "../Icons";
import { Box, Typography } from "@mui/material";

const data = [
  {
    id: 1,
    name: "Academic ",
  },
  {
    id: 2,
    name: "Family ",
  },
  {
    id: 3,
    name: "Vacation ",
  },
  {
    id: 4,
    name: "professional  ",
  },
  {
    id: 5,
    name: "<PERSON><PERSON><PERSON> ",
  },
  {
    id: 6,
    name: "Cook<PERSON> ",
  },
];

const Goals = () => {
  return (
    <InfoCard icon={<GoalIcon />} headerText="Goals">
      <Box display="flex" flexDirection="row" alignItems="center" gap={2} p={3}>
        {data.map((m) => (
          <Typography
            key={m.id}
            fontSize={12}
            sx={{
              color: "#fff",
              padding: "2px 6px",
              borderRadius: 3,
              background: "#14A79C",
            }}
          >
            {m.name}
          </Typography>
        ))}
      </Box>
    </InfoCard>
  );
};

export default Goals;
