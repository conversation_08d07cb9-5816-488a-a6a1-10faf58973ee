import React from "react";
import InfoCard from "./InfoCard";
import { Box, SxProps, Theme, Typography } from "@mui/material";
import Link from "next/link";

type DocumentSignedProps = React.FC<{
}>

const DocumentSigned: DocumentSignedProps = () => {
  const href = ""
  return (
    <Box sx={{
      pr: {
        xs: 0,
        md: 2
      },
      width: {
        xs: "100%",
        md: "50%"
      },
    }}>
      <InfoCard icon={<DocumentIcon size={18} />} headerText="Documents Signed">
        <Link href={href}>
          <Box display="flex" alignItems="center" flexDirection="row" mt={2} gap={2}>
            <DocumentIcon color="#6D6D6D" />
            <Typography color="#6D6D6D" fontSize={14}>Document1</Typography>
          </Box>
        </Link>
      </InfoCard>
    </Box>
  );
}

const DocumentIcon = ({ size = 16, color = "black" }) => {
  return (
    <svg width={size} height={size} viewBox="0 0 23 24" fill="none">
      <path d="M15.0938 12.625C15.0938 12.4344 15.018 12.2516 14.8832 12.1168C14.7484 11.982 14.5656 11.9063 14.375 11.9063H8.625C8.43438 11.9063 8.25156 11.982 8.11677 12.1168C7.98198 12.2516 7.90625 12.4344 7.90625 12.625C7.90625 12.8156 7.98198 12.9985 8.11677 13.1332C8.25156 13.268 8.43438 13.3438 8.625 13.3438H14.375C14.5656 13.3438 14.7484 13.268 14.8832 13.1332C15.018 12.9985 15.0938 12.8156 15.0938 12.625ZM15.0938 16.4583C15.0938 16.2677 15.018 16.0849 14.8832 15.9501C14.7484 15.8153 14.5656 15.7396 14.375 15.7396H8.625C8.43438 15.7396 8.25156 15.8153 8.11677 15.9501C7.98198 16.0849 7.90625 16.2677 7.90625 16.4583C7.90625 16.649 7.98198 16.8318 8.11677 16.9666C8.25156 17.1014 8.43438 17.1771 8.625 17.1771H14.375C14.5656 17.1771 14.7484 17.1014 14.8832 16.9666C15.018 16.8318 15.0938 16.649 15.0938 16.4583Z" fill={color} />
      <path fill-rule="evenodd" clip-rule="evenodd" d="M6.70841 2.32294C6.00946 2.32294 5.33913 2.6006 4.84489 3.09483C4.35066 3.58907 4.073 4.2594 4.073 4.95835V18.375C4.073 19.074 4.35066 19.7443 4.84489 20.2385C5.33913 20.7328 6.00946 21.0104 6.70841 21.0104H16.2917C16.9907 21.0104 17.661 20.7328 18.1553 20.2385C18.6495 19.7443 18.9272 19.074 18.9272 18.375V7.80269C18.9272 7.43756 18.8083 7.08298 18.5879 6.79165L15.7148 2.98898C15.5585 2.78209 15.3563 2.61427 15.1242 2.4987C14.8921 2.38314 14.6363 2.32297 14.377 2.32294H6.70841ZM5.5105 4.95835C5.5105 4.2971 6.04716 3.76044 6.70841 3.76044H13.6563V7.97423C13.6563 8.37098 13.9783 8.69298 14.3751 8.69298H17.4897V18.375C17.4897 19.0363 16.953 19.5729 16.2917 19.5729H6.70841C6.04716 19.5729 5.5105 19.0363 5.5105 18.375V4.95835Z" fill={color} />
    </svg>
  )
}

export default DocumentSigned