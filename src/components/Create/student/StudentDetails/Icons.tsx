import React from "react";

export const CommunityIcon = ({ size = 16 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 25 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.32955 14.8332V13.7152C7.32955 12.2325 7.87429 10.8106 8.84394 9.76225C9.81358 8.71387 11.1287 8.1249 12.5 8.1249M12.5 8.1249C13.8713 8.1249 15.1864 8.71387 16.1561 9.76225C17.1257 10.8106 17.6705 12.2325 17.6705 13.7152V14.8332M12.5 8.1249C13.3228 8.1249 14.1118 7.77151 14.6936 7.14249C15.2754 6.51346 15.6023 5.66031 15.6023 4.77073C15.6023 3.88115 15.2754 3.02801 14.6936 2.39898C14.1118 1.76995 13.3228 1.41656 12.5 1.41656C11.6772 1.41656 10.8882 1.76995 10.3064 2.39898C9.72457 3.02801 9.39773 3.88115 9.39773 4.77073C9.39773 5.66031 9.72457 6.51346 10.3064 7.14249C10.8882 7.77151 11.6772 8.1249 12.5 8.1249ZM1.125 14.8332V13.7152C1.125 12.8256 1.45185 11.9724 2.03363 11.3434C2.61542 10.7144 3.4045 10.361 4.22727 10.361M4.22727 10.361C4.77579 10.361 5.30184 10.1254 5.6897 9.70607C6.07756 9.28671 6.29545 8.71795 6.29545 8.1249C6.29545 7.53184 6.07756 6.96308 5.6897 6.54373C5.30184 6.12438 4.77579 5.88879 4.22727 5.88879C3.67876 5.88879 3.15271 6.12438 2.76485 6.54373C2.37699 6.96308 2.15909 7.53184 2.15909 8.1249C2.15909 8.71795 2.37699 9.28671 2.76485 9.70607C3.15271 10.1254 3.67876 10.361 4.22727 10.361ZM23.875 14.8332V13.7152C23.875 12.8256 23.5482 11.9724 22.9664 11.3434C22.3846 10.7144 21.5955 10.361 20.7727 10.361M20.7727 10.361C21.3212 10.361 21.8473 10.1254 22.2352 9.70607C22.623 9.28671 22.8409 8.71795 22.8409 8.1249C22.8409 7.53184 22.623 6.96308 22.2352 6.54373C21.8473 6.12438 21.3212 5.88879 20.7727 5.88879C20.2242 5.88879 19.6982 6.12438 19.3103 6.54373C18.9224 6.96308 18.7045 7.53184 18.7045 8.1249C18.7045 8.71795 18.9224 9.28671 19.3103 9.70607C19.6982 10.1254 20.2242 10.361 20.7727 10.361Z"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const ClassesIcon = ({
  size = 16,
  strokeWidth = 1.5,
  color = "#000",
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 15 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.5 1H11.4167C12.7526 1 13.4198 1 13.8349 1.41508C14.25 1.83017 14.25 2.49742 14.25 3.83333V8.08333C14.25 9.41925 14.25 10.0865 13.8349 10.5016C13.4198 10.9167 12.7526 10.9167 11.4167 10.9167H6.45833M7.16667 4.1875H11.4167M1.5 11.625V8.79167C1.5 8.12371 1.5 7.79008 1.70754 7.58254C1.91508 7.375 2.24871 7.375 2.91667 7.375H4.33333M1.5 11.625H4.33333M1.5 11.625V15.1667M4.33333 7.375V11.625M4.33333 7.375H8.58333M4.33333 11.625V15.1667"
        stroke={color}
        strokeWidth={strokeWidth}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M4.33333 4.18748C4.33333 4.5632 4.18408 4.92354 3.9184 5.18921C3.65272 5.45489 3.29239 5.60415 2.91667 5.60415C2.54094 5.60415 2.18061 5.45489 1.91493 5.18921C1.64926 4.92354 1.5 4.5632 1.5 4.18748C1.5 3.81176 1.64926 3.45142 1.91493 3.18574C2.18061 2.92007 2.54094 2.77081 2.91667 2.77081C3.29239 2.77081 3.65272 2.92007 3.9184 3.18574C4.18408 3.45142 4.33333 3.81176 4.33333 4.18748Z"
        stroke={color}
        strokeWidth={strokeWidth}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const InfoIcon = ({ size = 18 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3 7C3 6.20435 3.31607 5.44129 3.87868 4.87868C4.44129 4.31607 5.20435 4 6 4H18C18.7956 4 19.5587 4.31607 20.1213 4.87868C20.6839 5.44129 21 6.20435 21 7V17C21 17.7956 20.6839 18.5587 20.1213 19.1213C19.5587 19.6839 18.7956 20 18 20H6C5.20435 20 4.44129 19.6839 3.87868 19.1213C3.31607 18.5587 3 17.7956 3 17V7Z"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15 8H17M15 12H17M7 16H17M7 10C7 10.5304 7.21071 11.0391 7.58579 11.4142C7.96086 11.7893 8.46957 12 9 12C9.53043 12 10.0391 11.7893 10.4142 11.4142C10.7893 11.0391 11 10.5304 11 10C11 9.46957 10.7893 8.96086 10.4142 8.58579C10.0391 8.21071 9.53043 8 9 8C8.46957 8 7.96086 8.21071 7.58579 8.58579C7.21071 8.96086 7 9.46957 7 10Z"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const StudentHatIcon = ({ size = 18 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 23 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.9166 6.41671V12.6667M21.3124 7.37713C21.4989 7.29486 21.6571 7.1597 21.7676 6.98838C21.878 6.81707 21.9357 6.61712 21.9336 6.41331C21.9315 6.20951 21.8696 6.0108 21.7557 5.8418C21.6418 5.6728 21.4808 5.54094 21.2926 5.46255L12.3645 1.39588C12.0931 1.27208 11.7982 1.20801 11.4999 1.20801C11.2016 1.20801 10.9067 1.27208 10.6353 1.39588L1.70825 5.45838C1.5228 5.5396 1.36503 5.6731 1.25425 5.84256C1.14347 6.01202 1.08447 6.21009 1.08447 6.41255C1.08447 6.615 1.14347 6.81307 1.25425 6.98253C1.36503 7.15199 1.5228 7.28549 1.70825 7.36671L10.6353 11.4375C10.9067 11.5613 11.2016 11.6254 11.4999 11.6254C11.7982 11.6254 12.0931 11.5613 12.3645 11.4375L21.3124 7.37713Z"
        stroke="black"
        stroke-width="1.83333"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5.25 9.021V12.6668C5.25 13.4956 5.90848 14.2905 7.08058 14.8765C8.25269 15.4626 9.8424 15.7918 11.5 15.7918C13.1576 15.7918 14.7473 15.4626 15.9194 14.8765C17.0915 14.2905 17.75 13.4956 17.75 12.6668V9.021"
        stroke="black"
        stroke-width="1.83333"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const StudyingLanguageIcon = ({ size = 18 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 5.875C11.6875 3.1611 14.2841 1.77536 19.9375 1.75C20.0279 1.74966 20.1174 1.76721 20.201 1.80164C20.2846 1.83607 20.3605 1.8867 20.4244 1.95061C20.4883 2.01452 20.5389 2.09044 20.5734 2.17401C20.6078 2.25757 20.6253 2.34713 20.625 2.4375V14.8125C20.625 14.9948 20.5526 15.1697 20.4236 15.2986C20.2947 15.4276 20.1198 15.5 19.9375 15.5C14.4375 15.5 12.3127 16.609 11 18.25M11 5.875C10.3125 3.1611 7.7159 1.77536 2.0625 1.75C1.97213 1.74966 1.88257 1.76721 1.79901 1.80164C1.71544 1.83607 1.63952 1.8867 1.57561 1.95061C1.5117 2.01452 1.46107 2.09044 1.42664 2.17401C1.39221 2.25757 1.37466 2.34713 1.375 2.4375V14.7296C1.375 15.1541 1.63797 15.5 2.0625 15.5C7.5625 15.5 9.69504 16.6172 11 18.25M11 5.875V18.25"
        stroke="black"
        stroke-width="1.7"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const LnaguageIcon = ({ size = 18 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.0625 4.8125H14.4375M8.25 2.75V4.8125M11.6875 19.25L15.8125 9.625L19.9375 19.25M12.9551 16.5H18.6699M12.0871 4.8125C12.0871 4.8125 11.043 8.85156 8.55078 11.9023C6.05859 14.9531 3.4375 16.5 3.4375 16.5"
        stroke="black"
        stroke-width="1.7"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M11 14.4375C11 14.4375 9.49609 13.2773 7.90625 11.2148C6.31641 9.15234 5.5 7.5625 5.5 7.5625"
        stroke="black"
        stroke-width="1.7"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const GoalIcon = ({ size = 18 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.6601 10.6599C10.3034 11.0148 10.102 11.4967 10.1001 11.9999C10.1001 12.5038 10.3003 12.9871 10.6566 13.3434C11.0129 13.6997 11.4962 13.8999 12.0001 13.8999C12.5033 13.8981 12.9852 13.6967 13.3401 13.3399"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.0001 6.30005C10.8727 6.30005 9.77066 6.63435 8.8333 7.26067C7.89594 7.887 7.16536 8.77721 6.73394 9.81875C6.30252 10.8603 6.18964 12.0064 6.40958 13.1121C6.62951 14.2178 7.17238 15.2334 7.96954 16.0306C8.7667 16.8277 9.78235 17.3706 10.888 17.5905C11.9937 17.8105 13.1398 17.6976 14.1813 17.2662C15.2229 16.8347 16.1131 16.1042 16.7394 15.1668C17.3658 14.2294 17.7001 13.1274 17.7001 12"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12 2.5C10.1211 2.5 8.28435 3.05717 6.72209 4.10104C5.15982 5.14491 3.94218 6.62861 3.22315 8.36451C2.50412 10.1004 2.31598 12.0105 2.68254 13.8534C3.0491 15.6962 3.95389 17.3889 5.28249 18.7175C6.61109 20.0461 8.30383 20.9509 10.1466 21.3175C11.9895 21.684 13.8996 21.4959 15.6355 20.7769C17.3714 20.0578 18.8551 18.8402 19.899 17.2779C20.9428 15.7157 21.5 13.8789 21.5 12M15.525 8.476L12.95 11.05"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M20.94 5.84409L17.7 6.3001L18.156 3.06009C18.1593 3.02246 18.1514 2.98469 18.1331 2.95159C18.1149 2.9185 18.0872 2.89159 18.0536 2.87431C18.02 2.85703 17.982 2.85016 17.9445 2.85457C17.907 2.85899 17.8716 2.87449 17.843 2.89909L15.695 5.03609C15.4743 5.25832 15.3121 5.53162 15.2226 5.8317C15.1331 6.13178 15.1191 6.44933 15.182 6.7561L15.524 8.47609L17.244 8.8171C17.5507 8.87995 17.8683 8.866 18.1684 8.7765C18.4684 8.687 18.7418 8.52472 18.964 8.30409L21.1 6.1571C21.1247 6.12836 21.1402 6.09291 21.1446 6.05528C21.149 6.01764 21.142 5.97956 21.1246 5.94591C21.1072 5.91227 21.0801 5.8846 21.0469 5.86645C21.0136 5.84831 20.9757 5.84052 20.938 5.84409"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export const EditIcon = ({ size = 18 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 2.99994L15 5.99994M10 16.9999H18M2 12.9999L1 16.9999L5 15.9999L16.586 4.41394C16.9609 4.03889 17.1716 3.53027 17.1716 2.99994C17.1716 2.46961 16.9609 1.961 16.586 1.58594L16.414 1.41394C16.0389 1.039 15.5303 0.828369 15 0.828369C14.4697 0.828369 13.9611 1.039 13.586 1.41394L2 12.9999Z"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};
