import React, { useEffect, useState } from "react";
import StudentListHeader from "./ListView/StudentListHeader";
import { Box, CircularProgress, Typography } from "@mui/material";
import StudentListRow from "./ListView/StudentListRow";
import StudentCardView from "./CardView";
import { useSnackbar } from "@/hooks/useSnackbar";
import useDebounce from "@/hooks/useDebounce";
import axiosInstance from "@/utils/interceptor";
import { ROLE_TYPES } from "@/constant/Enums";
import useWindowDimensions from "@/hooks/useWindowDimension";
import InfiniteScroll from "react-infinite-scroll-component";

const LIMIT = 10;

const StudentList = ({ search, isLoading, setIsLoading }) => {
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [students, setStudents] = useState([]);
  const debouncedSearchTerm = useDebounce(search, 1000);

  const fetchData = async ({ search, skip }) => {
    const handleError = () => {
      setIsLoading(false);
      showSnackbar("Failed to fetch users", { type: "error" });
    };

    try {
      if (skip === 0) {
        setIsLoading(true);
      }
      const { data: respData } = await axiosInstance.post(
        `user/get-users`,
        {
          role: ROLE_TYPES.STUDENT,
        },
        {
          params: {
            skip,
            limit: LIMIT,
            search,
            isStudentDashboard: true,
          },
        }
      );
      if (respData.success) {
        if (skip === 0) {
          setStudents(respData.data);
        } else {
          setStudents((prev) => [...prev, ...respData.data]);
        }
        setSkip(+skip + LIMIT);
        setHasMore(+respData.data.length >= LIMIT);
      } else {
        handleError();
      }
      setIsLoading(false);
    } catch (error) {
      handleError();
      console.error("Something went wrong in fetchData due to", error);
    }
  };

  useEffect(() => {
    fetchData({
      search: debouncedSearchTerm,
      skip: 0,
    });
  }, [debouncedSearchTerm]);

  return (
    <Box width="100%" mt={4}>
      <Box
        sx={{
          border: "1px solid silver",
          borderRadius: 1,
          overflow: "hidden",
          p: 2,
          display: {
            xs: "none",
            md: "flex",
          },
          flexDirection: "column",
        }}
      >
        <StudentListHeader />
        <InfiniteScroll
          dataLength={students.length}
          next={() => fetchData({ search: debouncedSearchTerm, skip })}
          hasMore={hasMore}
          loader={
            <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          }
          endMessage={
            <Box sx={{ textAlign: "center", p: 2, color: "text.secondary" }}>
              <Typography variant="body2">
                {students.length > 0
                  ? "You've seen all users"
                  : "No users found matching your criteria"}
              </Typography>
            </Box>
          }
          scrollableTarget="scrollableDiv"
        >
          {students.map((m) => (
            <StudentListRow key={m.id} data={m} />
          ))}
        </InfiniteScroll>
      </Box>
      <Box
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        flexWrap="wrap"
        gap={4}
        mt={2}
        sx={{
          display: {
            xs: "flex",
            md: "none",
          },
        }}
      >
        <InfiniteScroll
          dataLength={students.length}
          next={() => fetchData({ search: debouncedSearchTerm, skip })}
          hasMore={hasMore}
          loader={
            <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          }
          endMessage={
            <Box sx={{ textAlign: "center", p: 2, color: "text.secondary" }}>
              <Typography variant="body2">
                {students.length > 0
                  ? "You've seen all users"
                  : "No users found matching your criteria"}
              </Typography>
            </Box>
          }
          scrollableTarget="scrollableDiv"
        >
          {students.map((m) => (
            <StudentCardView key={m.id} data={m} />
          ))}
        </InfiniteScroll>
      </Box>
    </Box>
  );
};

export default StudentList;
