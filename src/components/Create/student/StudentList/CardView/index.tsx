import { LocationIcon } from "@/components/Profile/ClassCard/ClassCardModal/Icon";
import { formatDate, formatTime } from "@/utils/dateTime";
import {
  getUserCountry,
  getUserFullName,
  getUserStudyingLanguage,
} from "@/utils/format";
import { Box, Card, Typography } from "@mui/material";
import Link from "next/link";
import React from "react";

const StudentCardView = ({ data }) => {
  const id = data.clerkId;
  return (
    <Card
      sx={{
        display: "flex",
        flexDirection: "column",
        width: {
          xs: "100%",
          md: "49%",
        },
        p: 3,
        textDecoration: "none",
        border: "1px solid #B3B3B3",
        boxShadow: "none",
        mb: 3,
      }}
    >
      <Link href={`/create/student-dashboard/student/${id}`}>
        <Box
          width="100%"
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Typography fontWeight={700} fontSize={16} color="#3C3C3C">
            {getUserFullName(data)}
          </Typography>
          <Typography color="#3C3C3C" fontSize={14}>
            {formatDate({ date: data.createdAt })}
            &nbsp;
            {formatTime({ date: data.createdAt })}
          </Typography>
        </Box>
        <Typography color="#6D6D6D" fontSize={14}>
          {data.email}
        </Typography>
        <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
          <LocationIcon />
          <Typography color="#3C3C3C" fontSize={14}>
            {getUserCountry(data)}
          </Typography>
        </Box>
        <Box display="flex" flexDirection="row">
          <Typography color="#6D6D6D" fontSize={14}>
            Studying Language:&nbsp;
          </Typography>
          <Typography color="#3C3C3C" fontSize={14}>
            {getUserStudyingLanguage(data)}
          </Typography>
        </Box>
      </Link>
    </Card>
  );
};

export default StudentCardView;
