import { <PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "@mui/material";
import React from "react";
import { formatDate, formatTime, getDateAsPerUserTZ } from "@/utils/dateTime";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { CustomProductType } from "@/api/mongoTypes";
import RemoveRedEyeIcon from "@mui/icons-material/RemoveRedEye";
import EditIcon from "@mui/icons-material/Edit";
import ShareIcon from "@mui/icons-material/Share";
import ArchiveIcon from "@mui/icons-material/Archive";
import CopyCode from "./CopyCode";
import TargetLanguage from "./TargetLanguage";
import useCustomProduct from "@/hooks/useCustomProduct";
import { PAYMENT_MODE, PAYMENT_STATUS } from "@/constant/Enums";
import { getPriceSymbol } from "@/utils/classes";

const commonButtonstyle = {
  p: 1,
  px: 3,
  width: "fit-content",
  fontSize: "0.75rem",
  gap: 2,
};

type CPCardProps = React.FC<{
  data: CustomProductType;
  setData: React.Dispatch<React.SetStateAction<CustomProductType[]>>;
  setIsArchieved: React.Dispatch<React.SetStateAction<boolean>>;
}>;
const CPCard: CPCardProps = ({ data, setData, setIsArchieved }) => {
  const {
    handleArchieve,
    handleEdit,
    handleShare,
    handleView,
    isArchieving,
    isEditing,
    isViewing,
    handleMarkAsPaid,
    isMarkingAsPaid,
  } = useCustomProduct({ data });

  const isDisabled = isArchieving || isEditing || isViewing || isMarkingAsPaid;

  return (
    <Card
      sx={{
        minHeight: "100%",
        width: {
          xs: "100%",
          md: 350,
          sm: 275,
        },
        borderRadius: 5,
        p: 4,
        boxShadow: "0px 2px 10px 0px #00000029",
        flexDirection: "column",
        display: "flex",
        position: "relative",
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          width: "100%",
          alignItems: "center",
          justifyContent: "end",
        }}
      >
        <CopyCode code={data.code} />
      </Box>
      <Typography fontWeight={700} fontSize="1.1rem" mt={0.5}>
        {data.title}
      </Typography>
      <Typography
        fontSize="0.9rem"
        mb={2}
        sx={{
          px: 2,
          py: 1,
          background: "#24c58329",
          width: "fit-content",
          borderRadius: 2,
        }}
      >
        {getPriceSymbol({ currency: data.currency })} {data.price}
      </Typography>
      <Box
        width="100%"
        height="100%"
        maxHeight={100}
        sx={{ overflow: "hidden" }}
      >
        <Typography
          fontSize="0.8rem"
          mb={2}
          sx={{
            display: "-webkit-box",
            WebkitLineClamp: 4,
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {data.description}
        </Typography>
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
          gap: 2,
        }}
      >
        <StartDateInfo data={data} />
        <TargetLanguage targetLanguage={data?.targetLanguage} />
      </Box>
      <PaymentStatus transactionDetails={data.transactionId} />
      <Button
        sx={{ fontSize: "0.8rem", mt: 2, width: "100%" }}
        onClick={() => {
          handleMarkAsPaid({
            handleSuccess: () => {
              setIsArchieved(true);
            },
          });
        }}
      >
        Mark as Paid (cash)
      </Button>
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          gap: 2,
          flexWrap: "wrap",
          alignItems: "center",
          justifyContent: "center",
          width: "100%",
          mt: 2,
        }}
      >
        <Button
          disabled={isDisabled}
          onClick={handleEdit}
          sx={commonButtonstyle}
        >
          <EditIcon />
          Edit
        </Button>
        <Button
          disabled={isDisabled}
          onClick={handleView}
          sx={commonButtonstyle}
        >
          <RemoveRedEyeIcon />
          View
        </Button>
        <Button onClick={handleShare} sx={commonButtonstyle}>
          <ShareIcon />
          Share Link
        </Button>
        <Button
          disabled={isDisabled}
          onClick={() => {
            handleArchieve({
              onSuccess: () => {
                setData((prev) =>
                  prev.map((m) => {
                    if (m._id === data._id) {
                      return {
                        ...m,
                        isExpired: true,
                      } as CustomProductType;
                    }
                    return m as CustomProductType;
                  })
                );
              },
            });
          }}
          sx={commonButtonstyle}
        >
          <ArchiveIcon />
          Archieve
        </Button>
      </Box>
    </Card>
  );
};

const PaymentStatus = ({ transactionDetails }) => {
  const status = (() => {
    if (transactionDetails?.status === PAYMENT_STATUS.SUCCESS) {
      return "Paid";
    }
    return "UnPaid";
  })();

  const bgColor = (() => {
    if (status === "Paid") {
      return "#00ff3942";
    }
    return "#ff000042";
  })();
  const color = (() => {
    if (status === "Paid") {
      return "green";
    }
    return "red";
  })();

  return (
    <Box display="flex" flexDirection="row" alignItems="center" sx={{ mt: 2 }}>
      <Typography fontSize="0.75rem" mb={0}>
        Payment Status:&nbsp;
      </Typography>
      <Typography
        sx={{ background: bgColor, borderRadius: 5, padding: "2px 10px" }}
        fontSize="0.85rem"
        color={color}
        textAlign="center"
        mb={0}
      >
        <b style={{ textTransform: "uppercase" }}>{status}</b>
      </Typography>
    </Box>
  );
};

const StartDateInfo = ({ data }) => {
  const dateAsPerUserTZ = getDateAsPerUserTZ({
    dateTime: data.startDate,
    timezone: data.timezone,
  });

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        width: "fit-content",
        gap: 1,
        background: "#8080803b",
        px: 2,
        py: 1,
        borderRadius: 2,
      }}
    >
      <AccessTimeIcon
        sx={{
          color: "#3C3C3C",
          fontSize: 12,
        }}
      />
      <Typography sx={{ color: "#3C3C3C", fontSize: 12, fontWeight: 600 }}>
        {formatDate({ date: new Date(dateAsPerUserTZ) })}{" "}
        {formatTime({ date: new Date(dateAsPerUserTZ) })}
      </Typography>
    </Box>
  );
};

export default CPCard;
