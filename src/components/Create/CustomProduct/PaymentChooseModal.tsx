import CustomButton from "@/components/CustomButton";
import { Box, Modal, Typography } from "@mui/material";
import Image from "next/image";
import React from "react";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import usePayment from "@/hooks/usePayment";
import { CustomProductType } from "@/api/mongoTypes";
import { useRouter } from "next/router";

type PaymentChooseModalProps = React.FC<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  data: CustomProductType;
}>;
const PaymentChooseModal: PaymentChooseModalProps = ({
  open,
  setOpen,
  data,
}) => {
  const router = useRouter();
  const { isMaking, makePayment } = usePayment();

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Modal open={open}>
      <Box
        sx={{
          bgcolor: "white",
          p: 4,
          mx: "auto",
          mt: "10%",
          maxWidth: 400,
          borderRadius: 2,
          boxShadow: 4,
          textAlign: "center",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <Typography fontSize="1.2rem" fontWeight={700} mb={2}>
            Select your preference
          </Typography>
          <Box sx={{ cursor: "pointer" }} onClick={handleClose}>
            <HighlightOffIcon />
          </Box>
        </Box>
        <Typography
          fontSize="0.9rem"
          color="text.secondary"
          sx={{ mb: 3, textAlign: "left" }}
        >
          Please select how you’d like to complete your purchase. You can pay
          securely online using Stripe or choo
        </Typography>
        <Image height={150} width={150} alt="patito" src="/patitoB.png" />
        <Box
          display="flex"
          flexDirection="column"
          width="100%"
          mt={2}
          aria-disabled={isMaking}
        >
          <CustomButton
            text="Pay using stripe"
            onClick={() => {
              makePayment({
                cartId: [],
                eventId: [],
                classesDetails: [],
                clubsDetails: [],
                price: data.price,
                productData: [
                  {
                    name: data.title,
                    description: data.description,
                    images: ["https://www.patitofeo.com/patitoB.png"],
                    price: data.price,
                  },
                ],
                eventsPriceDetails: [],
                currency: data.currency,
                customProductsDetails: {
                  price: data.price,
                  currency: data.currency,
                  customProductsInfo: data._id,
                },
              });
            }}
            sx={{
              width: "100%",
            }}
          />
          <CustomButton
            colortype="secondary"
            text="Pay using bank transfer"
            onClick={() => {
              router.push(`/classes/custom-products/bank-transfer/${data._id}`);
            }}
            sx={{
              width: "100%",
              mt: 2,
            }}
          />
        </Box>
      </Box>
    </Modal>
  );
};

export default PaymentChooseModal;
