import { useSnackbar } from "@/hooks/useSnackbar";
import { Box, Typography } from "@mui/material";
import React from "react";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";

type CopyCodeProps = React.FC<{
  code: string;
}>;
const CopyCode: CopyCodeProps = ({ code }) => {
  const { showSnackbar } = useSnackbar();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      showSnackbar("Copied the code successfully", {
        type: "success",
      });
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        gap: 2,
        alignItems: "center",
        justifyContent: "space-between",
        background: "#00ff004f",
        width: "fit-content",
        borderRadius: 2,
        px: 2,
        py: 1,
      }}
    >
      <Typography fontSize="0.85rem" color="green">
        {code}
      </Typography>
      <ContentCopyIcon
        onClick={handleCopy}
        sx={{ color: "green", fontSize: "0.85rem", cursor: "pointer" }}
      />
    </Box>
  );
};

export default CopyCode;
