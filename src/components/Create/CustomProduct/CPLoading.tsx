import { Box, Card, Container, Skeleton, Typography } from "@mui/material";
import React from "react";

const CPLoading = () => {
  return (
    <Container maxWidth="md">
      <Card
        sx={{
          mt: 10,
          p: 4,
        }}
      >
        <Skeleton width={300} height={40} sx={{ mx: "auto" }} />
        <Box
          sx={{
            minHeight: "300px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Skeleton variant="rectangular" width="80%" height={200} />
        </Box>
        <Box sx={{ display: "flex", justifyContent: "center", gap: 2 }}>
          <Skeleton width={120} height={40} />
          <Skeleton width={120} height={40} />
        </Box>
      </Card>
    </Container>
  );
};

export default CPLoading;
