import CustomButton from "@/components/CustomButton";
import { Box, Button, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";
import React from "react";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "800px",
  alignItems: "center",
  width: "100%",
  margin: "1rem auto",
  padding: { xs: "20px", sm: "0px" },
};

const CPExpired = () => {
  const router = useRouter();
  return (
    <Box sx={containerStyles}>
      <Image
        height={150}
        width={150}
        alt="Expired product illustration"
        src="/patitoB.png"
      />
      <Typography textAlign="center" fontSize="2rem" fontWeight={800}>
        This Product Has Expired
      </Typography>
      <Typography
        textAlign="center"
        fontSize="1rem"
        fontWeight={400}
        mt={1.5}
        color="text.secondary"
      >
        The product you’re trying to access is no longer available. Don’t
        worry—you can still explore and join our available classes below.
      </Typography>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        mt={3}
      >
        <CustomButton
          sx={{ width: "100%" }}
          text="View Available Classes"
          onClick={() => {
            router.push("/classes");
          }}
        />
      </Box>
    </Box>
  );
};

export default CPExpired;
