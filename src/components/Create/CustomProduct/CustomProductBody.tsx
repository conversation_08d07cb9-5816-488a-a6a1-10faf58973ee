import {
  Box,
  Button,
  Card,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CreateHeader from "../CreateHeader";
import { CURRENCY_ENUM } from "@/constant/Enums";
import { useSnackbar } from "@/hooks/useSnackbar";
import PriceInput from "@/components/PriceInput";
import { getEnglishAndSpanish } from "@/utils/common";
import { CustomProductType, LanguageType } from "@/api/mongoTypes";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { validateCp } from "@/utils/cpUtils";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import { formatDate, formatTime } from "@/utils/dateTime";
import SelectTimeZone from "../event/SelectTimeZone";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "800px",
  alignItems: "center",
  width: "100%",
  margin: "0 auto",
  padding: { xs: "20px", sm: "0px" },
};
const headerStyles = { marginBottom: "33px", maxWidth: "800px" };
const eventSubHeaderStyles = {
  textAlign: "center",
  fontSize: "20px",
  fontWeight: "500",
  marginBottom: "33px",
};
const eventTitleStyles = { marginBottom: "20px" };
const buttonContainerStyles = {
  marginTop: "75px",
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-evenly",
  gap: 10,
};

type CustomProductBodyProps = React.FC<{
  data: CustomProductType;
  languages: LanguageType[];
}>;
const BASE_URL = "/create/custom-product";

const CustomProductBody: CustomProductBodyProps = ({ data, languages }) => {
  const isUpdate = !!data?._id;
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [title, setTitle] = useState("");
  const [internalNotes, setInternalNotes] = useState("");
  const [description, setDescription] = useState("");
  const [currency, setCurrency] = useState<CURRENCY_ENUM>(CURRENCY_ENUM.MXN);
  const [targetLanguage, setTargetLanguage] = useState(null);
  const [price, setPrice] = useState<string | number>(10);
  const [startDate, setStartDate] = useState(dayjs(new Date()));
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [extendTwoDays, setExtendTwoDays] = useState(false);
  const [timezone, setTimezone] = useState("");

  const handleReset = () => {
    setTitle("");
    setDescription("");
    setCurrency(CURRENCY_ENUM.MXN);
    setPrice(10);
    setStartDate(dayjs(new Date()));
    setTargetLanguage(null);
    setInternalNotes("");
    setExtendTwoDays(false);
  };

  useEffect(() => {
    if (data) {
      setTitle(data.title);
      setDescription(data.description);
      setCurrency(data?.currency ?? CURRENCY_ENUM.MXN);
      setPrice(data.price);
      setStartDate(dayjs(data.startDate));
      setInternalNotes(data.internalNotes);
      setTargetLanguage(data.targetLanguage?._id);
      setTimezone(data.timezone);
    }
  }, [data]);

  const englishSpanishLanguages = getEnglishAndSpanish(languages);
  const isDisabled = isCreating || isUpdating;

  const handleUpdateClub = async () => {
    const handleError = () => {
      setIsUpdating(false);
      showSnackbar("Something went wrong while updating Custom Product", {
        type: "error",
      });
    };
    const error = validateCp({
      title,
      description,
      price: +price,
      currency,
      targetLanguage,
      startDate,
      internalNotes,
    });
    if (error) {
      showSnackbar(error, {
        type: "warning",
      });
      return;
    }

    try {
      const { data: respData } = await axiosInstance.post(
        `custom-product/update`,
        {
          title,
          description,
          internalNotes,
          currency,
          targetLanguage,
          price: +price,
          startDate,
          timezone,
          id: data._id,
          expirationDate: data.expirationDate,
          extendTwoDays,
        }
      );
      if (respData.success) {
        showSnackbar("Updated Custom Product successfully", {
          type: "success",
        });
        router.push(BASE_URL);
      } else {
        handleError();
      }
    } catch (error) {
      handleError();
      console.error("Smething went wrong in handleUpdateClub", error);
    }
  };

  const handleCreateClub = async () => {
    const handleError = () => {
      setIsCreating(false);
      showSnackbar("Something went wrong while updating Custom Product", {
        type: "error",
      });
    };
    const error = validateCp({
      title,
      description,
      price: +price,
      currency,
      targetLanguage,
      startDate,
      internalNotes,
    });
    if (error) {
      showSnackbar(error, {
        type: "warning",
      });
      return;
    }
    try {
      const { data } = await axiosInstance.post(`custom-product/create`, {
        title,
        description,
        price: +price,
        currency,
        targetLanguage,
        startDate,
        internalNotes,
        timezone,
      });
      if (data.success) {
        showSnackbar("Created Custom Product successfully", {
          type: "success",
        });
        router.push(BASE_URL);
      } else {
        handleError();
      }
    } catch (error) {
      handleError();
      console.error("Smething went wrong in handleCreateClub", error);
    }
  };

  return (
    <Box sx={containerStyles}>
      <CreateHeader
        text={isUpdate ? "Update Custom Product" : "Custom Product Creation"}
        bottomBorderNeeded
        style={headerStyles}
        backRoute={BASE_URL}
      />

      <TextField
        label="Enter  Title"
        variant="outlined"
        fullWidth
        value={title}
        sx={eventTitleStyles}
        onChange={(event) => {
          setTitle(event.target.value);
        }}
      />
      <PriceInput
        currency={currency}
        label="Enter price"
        price={price}
        onCurrencyChanged={(cur) => {
          setCurrency(cur);
        }}
        onPriceChanged={(val) => {
          if (val === "" || isNaN(val)) {
            setPrice("");
          } else {
            const numericValue = +val;
            if (numericValue > 0) {
              setPrice(val);
            }
          }
        }}
      />
      <FormControl fullWidth sx={{ mb: 4 }}>
        <InputLabel id="target-language-label" shrink>
          Target Language
        </InputLabel>
        <Select
          labelId="target-language-label"
          id="target-language"
          value={targetLanguage}
          onChange={(event) => setTargetLanguage(event.target.value)}
          label="Target Language"
        >
          {englishSpanishLanguages.map((languageLevel) => (
            <MenuItem key={languageLevel._id} value={languageLevel._id}>
              {languageLevel.nameInEnglish}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <TextField
        label="Enter Description"
        variant="outlined"
        value={description}
        onChange={(event) => {
          setDescription(event.target.value);
        }}
        multiline
        sx={{ mb: 5 }}
        rows={3}
        fullWidth
      />
      <TextField
        label="Enter Internal Notes"
        variant="outlined"
        value={internalNotes}
        onChange={(event) => {
          setInternalNotes(event.target.value);
        }}
        multiline
        sx={{ mb: 5 }}
        rows={3}
        fullWidth
      />
      <SelectTimeZone
        value={timezone}
        onChange={(event) => {
          setTimezone(event.target.value);
        }}
      />
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          sx={{ width: "100%", mt: 4 }}
          label="Start Date"
          value={startDate ? dayjs(startDate) : null}
          disablePast={!isUpdate}
          onChange={(newValue) => {
            setStartDate(newValue);
          }}
        />
      </LocalizationProvider>
      {isUpdate && (
        <Card sx={{ width: "100%", mt: 4, p: 4 }}>
          <Typography>
            This Custom product will be expired on{" "}
            {formatDate({ date: new Date(data?.expirationDate) })}{" "}
            {formatTime({
              date: new Date(data?.expirationDate),
            })}
          </Typography>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={extendTwoDays}
                  onChange={(e) => {
                    setExtendTwoDays(e.target.checked);
                  }}
                />
              }
              label="Extend two days"
            />
          </FormGroup>
        </Card>
      )}
      <Box sx={buttonContainerStyles}>
        <Button onClick={handleReset} disabled={isDisabled}>
          Reset
        </Button>
        {!isUpdate ? (
          <Button disabled={isDisabled} onClick={handleCreateClub}>
            Save
          </Button>
        ) : (
          <Button disabled={isDisabled} onClick={handleUpdateClub}>
            Update
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default CustomProductBody;
