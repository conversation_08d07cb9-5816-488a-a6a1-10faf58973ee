import { <PERSON>, <PERSON><PERSON>, Card, SxProps, Theme, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import CopyCode from "./CopyCode";
import ArchiveIcon from "@mui/icons-material/Archive";
import EditIcon from "@mui/icons-material/Edit";
import { CustomProductType } from "@/api/mongoTypes";
import { Maybe } from "@/types";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import { useSnackbar } from "@/hooks/useSnackbar";
import { getPriceSymbol } from "@/utils/classes";
import { getLanguageName } from "@/utils/common";
import { formatDate, formatTime, getDateAsPerUserTZ } from "@/utils/dateTime";
import PaymentChooseModal from "./PaymentChooseModal";
import BankTransferDetails from "./BankTransferDetails";
import CPExpired from "./CPExpired";
import CPLoading from "./CPLoading";
import CPNoData from "./CPNoData";
import useCustomProduct from "@/hooks/useCustomProduct";

const buttonStyle: SxProps<Theme> = {
  display: "flex",
  flexDirection: "row",
  gap: 2,
  width: "fit-content",
  fontSize: "0.85rem",
};

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "800px",
  alignItems: "center",
  width: "100%",
  margin: "0 auto",
  padding: { xs: "20px", sm: "0px" },
};

type CPViewProps = React.FC<{
  isAdmin?: boolean;
}>;
const CPView: CPViewProps = ({ isAdmin = false }) => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const { id } = router.query;
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<Maybe<CustomProductType>>(null);
  const [showModal, setShowModal] = useState(false);
  const [showBuyNow, setShowBuyNow] = useState(false);
  const { handleArchieve, handleEdit, isArchieving, isEditing } =
    useCustomProduct({ data });

  const targetLanguage = useMemo(() => {
    if (data) {
      return getLanguageName(data.targetLanguage);
    }
    return null;
  }, [data]);

  const handleError = () => {
    setIsLoading(false);
    showSnackbar("Failed to fetch custom product", {
      type: "warning",
    });
  };

  useEffect(() => {
    const fetchCustomProduct = async () => {
      try {
        setIsLoading(true);
        const { data } = await axiosInstance.get(`custom-product/get/${id}`, {
          params: isAdmin ? { isAdmin: true } : {},
        });
        if (data.success) {
          setData(data.data);
          setIsLoading(false);
        } else {
          handleError();
        }
      } catch (error) {
        console.log("Something went wrong in fetchCustomProduct", error);
        handleError();
      }
    };
    if (id) {
      fetchCustomProduct();
    }
  }, [id, isAdmin]);

  if (isLoading) {
    return <CPLoading />;
  }

  if (!data) {
    return <CPNoData />;
  }

  if (data?.isExpired) {
    return <CPExpired />;
  }
  const dateAsPerUserTZ = getDateAsPerUserTZ({
    dateTime: data.startDate,
    timezone: data.timezone,
  });
  const endDateAsPerUserTZ = getDateAsPerUserTZ({
    dateTime: data.expirationDate,
    timezone: data.timezone,
  });

  return (
    <>
      <Box sx={containerStyles}>
        <Card
          sx={{
            width: "100%",
            mt: 4,
            boxShadow: "0px 2px 10px 0px #00000029",
            border: "1px solid #E6E6E6",
            borderRadius: "19px",
            padding: "20px",
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: 2,
              width: "100%",
              justifyContent: "space-between",
              borderBottom: "1px solid #E6E6E6",
              py: 3,
            }}
          >
            <Typography fontSize="1.4rem" fontWeight={800}>
              {data?.title}
            </Typography>
            <Box
              sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "stretch",
                gap: 2,
              }}
            >
              {isAdmin ? (
                <>
                  <CopyCode code={data?.code} />
                  <Button
                    disabled={isArchieving}
                    sx={buttonStyle}
                    onClick={() => {
                      handleArchieve({});
                    }}
                  >
                    <ArchiveIcon
                      sx={{
                        fontSize: "0.85rem",
                      }}
                    />
                    Archieve
                  </Button>
                  <Button
                    disabled={isEditing}
                    sx={buttonStyle}
                    onClick={handleEdit}
                  >
                    <EditIcon
                      sx={{
                        fontSize: "0.85rem",
                      }}
                    />
                    Edit
                  </Button>
                </>
              ) : (
                <>
                  {!showBuyNow && (
                    <Button
                      onClick={() => {
                        setShowBuyNow(true);
                      }}
                      sx={buttonStyle}
                    >
                      Buy Now
                    </Button>
                  )}
                </>
              )}
            </Box>
          </Box>
          <Box sx={{ width: "100%", display: "flex", flexDirection: "column" }}>
            <Box
              sx={{
                width: "100%",
                display: "flex",
                flexDirection: "row",
              }}
            >
              <NameAndValue name="Price">
                <Typography>
                  {getPriceSymbol({ currency: data.currency })}
                  {String(data.price)}
                </Typography>
              </NameAndValue>
              <NameAndValue name="Target langauge">
                <Typography>{targetLanguage}</Typography>
              </NameAndValue>
            </Box>
            <Box
              sx={{
                width: "100%",
                display: "flex",
                flexDirection: "row",
              }}
            >
              <NameAndValue name="Start Date">
                <Typography>
                  {formatDate({ date: new Date(dateAsPerUserTZ) })}{" "}
                  {formatTime({ date: new Date(dateAsPerUserTZ) })}
                </Typography>
              </NameAndValue>
              {isAdmin && (
                <NameAndValue name="Expiration Date">
                  <Typography>
                    {formatDate({ date: new Date(endDateAsPerUserTZ) })}{" "}
                    {formatTime({ date: new Date(endDateAsPerUserTZ) })}
                  </Typography>
                </NameAndValue>
              )}
            </Box>

            {isAdmin && (
              <NameAndValue name="Internal Notes">
                <Typography>{data.internalNotes}</Typography>
              </NameAndValue>
            )}
            <NameAndValue name="Description">
              <Typography>{data.description}</Typography>
            </NameAndValue>
          </Box>
        </Card>

        {showBuyNow && <BankTransferDetails data={data} />}
      </Box>
      {showModal && (
        <PaymentChooseModal
          data={data}
          open={showModal}
          setOpen={setShowModal}
        />
      )}
    </>
  );
};

export default CPView;

const NameAndValue = ({ name, children }) => {
  return (
    <Box sx={{ width: "100%", mt: 4 }}>
      <Typography sx={{ fontSize: "0.8rem", fontWeight: "700" }}>
        {name}
      </Typography>
      <Box sx={{ width: "100%", color: "#6D6D6D" }}>{children}</Box>
    </Box>
  );
};
