import CustomButton from "@/components/CustomButton";
import { Box, Button, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";
import React from "react";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "800px",
  alignItems: "center",
  width: "100%",
  margin: "1rem auto",
  padding: { xs: "20px", sm: "0px" },
  justifyContent: "center",
};

const CPNoData = () => {
  const router = useRouter();
  return (
    <Box sx={containerStyles}>
      <Image
        height={150}
        width={150}
        alt="Expired product illustration"
        src="/patitoB.png"
      />
      <Typography textAlign="center" fontSize="2rem" fontWeight={800}>
        Oops! Something went wrong
      </Typography>
      <Typography
        textAlign="center"
        fontSize="1rem"
        fontWeight={400}
        mt={1.5}
        color="text.secondary"
      >
        We couldn’t load your custom product. Please refresh the page and try
        again.
      </Typography>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        mt={3}
        width="70%"
      >
        <CustomButton
          sx={{
            width: {
              xs: "100%",
              md: "70%",
            },
          }}
          text="Refresh"
          onClick={() => {
            router.reload();
          }}
        />
      </Box>
    </Box>
  );
};

export default CPNoData;
