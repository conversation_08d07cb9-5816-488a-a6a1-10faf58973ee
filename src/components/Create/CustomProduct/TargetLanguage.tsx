import { getLanguageName } from "@/utils/common";
import { Box, Typography } from "@mui/material";
import { useMemo } from "react";

const TargetLanguage = ({ targetLanguage }) => {
  const targetLanguageName = useMemo(() => {
    return getLanguageName(targetLanguage);
  }, [targetLanguage]);

  return (
    <Box sx={{ mt: 2 }}>
      <Typography fontSize="0.65rem" color="#14A79C" mb={0}>
        Target Language:
      </Typography>
      <Typography fontSize="0.85rem" color="#14A79C" textAlign="center" mb={0}>
        <b style={{ textTransform: "uppercase" }}>{targetLanguageName}</b>
      </Typography>
    </Box>
  );
};

export default TargetLanguage;
