import { Box, Typography } from "@mui/material";
import React from "react";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import CopyBox from "./CopyBox";

const BankInfo = () => {
  const bankName = process.env.NEXT_PUBLIC_BANK_NAME ?? "";
  const accountNumber = process.env.NEXT_PUBLIC_BANK_ACCOUNT_NUMBER ?? "";
  const accountHolder = process.env.NEXT_PUBLIC_BANK_ACCOUNT_HOLDER ?? "";

  return (
    <Box
      display="flex"
      flexDirection="column"
      width="100%"
      sx={{
        background: "#f5f5f5",
        borderRadius: "10px",
        padding: "20px",
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        width="100%"
        alignItems="center"
        justifyContent="start"
        gap={2}
        mb={2}
      >
        <AccountBalanceIcon />
        <Typography fontWeight={700}>Bank Transfer Details</Typography>
      </Box>

      <CopyBox name="BANK" value={bankName} />
      <CopyBox name="ACCOUNT NUMBER (CLABE)" value={accountNumber} />
      <CopyBox name="ACCOUNT HOLDER" value={accountHolder} />
    </Box>
  );
};

export default BankInfo;
