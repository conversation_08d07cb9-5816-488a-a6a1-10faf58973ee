/* eslint-disable react/no-unescaped-entities */
import React, { useState } from "react";
import StepIndex from "./StepIndex";
import { useSnackbar } from "@/hooks/useSnackbar";
import { Box, Button, Input, Typography } from "@mui/material";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import TrendingFlatIcon from "@mui/icons-material/TrendingFlat";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";

const Steps = ({ isTransferring, setIsTransferring, id }) => {
  return (
    <>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="start"
        gap={2}
        mt={8}
        width="100%"
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 2,
            justifyContent: "center",
            background: "#F3B358",
            p: "3px",
            borderRadius: "5px",
          }}
        >
          <AccessTimeIcon />
        </Box>
        <Typography fontWeight={700}>Transfer Instructions</Typography>
      </Box>
      <StepIndex
        index={1}
        title="Complete the transfer with payment reference"
        helperText={`Replace "[Your Full Name]" with your actual full name`}
      >
        <NameStep />
      </StepIndex>
      <StepIndex
        index={2}
        title="Send payment receipt to Whatsapp"
        helperText={`Take a screenshotof your payment receipt and send it via WhatsApp`}
      >
        <WhatsappStep />
      </StepIndex>
      <StepIndex index={3} title="Enter your confirmation code:" helperText="">
        <ConfirmationStep
          isTransferring={isTransferring}
          setIsTransferring={setIsTransferring}
          id={id}
        />
      </StepIndex>
    </>
  );
};

export default Steps;

const ConfirmationStep = ({ isTransferring, setIsTransferring, id }) => {
  const [code, setCode] = useState("");
  const { showSnackbar } = useSnackbar();
  const router = useRouter();

  const handleVerify = async () => {
    const handleError = (message?: string) => {
      setIsTransferring(false);
      showSnackbar(message ?? "Failed to verify the code", {
        type: "error",
      });
    };

    try {
      if (!code) {
        showSnackbar("Please enter the code", {
          type: "warning",
        });
        return;
      }
      if (code.length < 4) {
        showSnackbar("Code should be of 4 digits", {
          type: "warning",
        });
        return;
      }
      setIsTransferring(true);
      const { status, data } = await axiosInstance.post(
        "custom-product/transfer",
        {
          code,
          productId: id,
        }
      );
      if (status === 409) {
        if (data.message === "Product is expired") {
          showSnackbar("Product is expired", {
            type: "error",
          });
          router.push("/classes");
          return;
        }

        handleError(data.message);
        return;
      }
      if (data.success) {
        showSnackbar("Purchased the custom product successfully", {
          type: "success",
        });
        router.push(`/payment/${data.data}`);
      } else {
        handleError();
      }
    } catch (error) {
      console.error("Something went wrong in handleVerify", error);
      handleError();
    }
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Typography fontSize="0.75rem" color="gray">
        Once we confirm your transfer, we'll provide you with a unique
        confirmation code
      </Typography>
      <Box
        sx={{
          width: {
            xs: "100%",
            md: "70%",
          },
        }}
      >
        <Input
          sx={{ width: "100%", mb: 4 }}
          value={code}
          onChange={(e) => {
            setCode(e.target.value);
          }}
        />
        <Button
          onClick={handleVerify}
          sx={{
            width: "100%",
            borderRadius: 0,
            fontSize: "0.85rem",
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          Verify & Complete Order
          <TrendingFlatIcon />
        </Button>
      </Box>
    </Box>
  );
};

const NameStep = () => {
  const { showSnackbar } = useSnackbar();
  const text = "Patito Feo + [Your Full Name]";

  return (
    <Box
      onClick={async () => {
        try {
          await navigator.clipboard.writeText(text);
          showSnackbar(`Copied successfully`, {
            type: "success",
          });
        } catch (err) {
          console.error("Failed to copy:", err);
          showSnackbar(`Failed to copy.Please try again.`, {
            type: "error",
          });
        }
      }}
      sx={{
        background: "#f3b35830",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        p: 4,
        cursor: "pointer",
        gap: 2,
        my: 1,
        borderLeft: "2px solid #F3B358",
      }}
    >
      <Typography color="#b36c08" fontSize="0.85rem">
        {text}
      </Typography>
      <ContentCopyIcon
        sx={{
          fontSize: "1rem",
          color: "#b36c08",
        }}
      />
    </Box>
  );
};

const WhatsappStep = () => {
  const { showSnackbar } = useSnackbar();
  const whatsappNumber = "+52 91724 239853";

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      p={2}
      gap={2}
      px={4}
      my={1}
      sx={{
        border: "1px solid green",
        borderRadius: "5px",
        background: "#00800008",
      }}
    >
      <Box display="flex" flexDirection="row" gap={2} alignItems="center">
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 2,
            justifyContent: "center",
            background: "green",
            p: "5px",
            width: "fit-content",
            borderRadius: "50%",
          }}
        >
          <WhatsAppIcon sx={{ color: "#fff" }} />
        </Box>
        <Box display="flex" flexDirection="column">
          <Typography fontSize="0.7rem" color="green">
            WhatsApp
          </Typography>
          <Typography fontSize="1.25rem" color="darkgreen" fontWeight={800}>
            {whatsappNumber}
          </Typography>
        </Box>
      </Box>
      <ContentCopyIcon
        onClick={async () => {
          try {
            await navigator.clipboard.writeText(whatsappNumber);
            showSnackbar(`Copied successfully`, {
              type: "success",
            });
          } catch (err) {
            console.error("Failed to copy:", err);
            showSnackbar(`Failed to copy.Please try again.`, {
              type: "error",
            });
          }
        }}
        sx={{
          fontSize: "1rem",
          color: "green",
        }}
      />
    </Box>
  );
};
