/* eslint-disable react/no-unescaped-entities */
import { Box, Button, Card, Typography } from "@mui/material";
import React, { useState } from "react";
import BankInfo from "./BankInfo";
import Steps from "./Steps";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import CreditCardIcon from "@mui/icons-material/CreditCard";
import usePayment from "@/hooks/usePayment";
import { CustomProductType } from "@/api/mongoTypes";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "800px",
  alignItems: "center",
  width: "100%",
  margin: "1rem auto",
  padding: { xs: "20px", sm: "0px" },
};

type BankTransferDetailsProps = React.FC<{
  data: CustomProductType;
}>;
const BankTransferDetails: BankTransferDetailsProps = ({ data }) => {
  const [isStripe, setIsStripe] = useState(false);
  const { isMaking, makePayment } = usePayment();
  const [isTransferring, setIsTransferring] = useState(false);

  return (
    <Box sx={containerStyles}>
      <Typography textAlign="center" fontSize="1.85rem" fontWeight={800}>
        Complete your payment
      </Typography>
      <Typography textAlign="center">
        Choose your preferred payment method to finalize your purchase
      </Typography>

      <Card
        sx={{ width: "100%", p: 8, mt: 6 }}
        aria-disabled={isMaking || isTransferring}
      >
        <Typography fontWeight={800} mb={4}>
          Payment Method
        </Typography>

        <PaymentMethodTabs setIsStripe={setIsStripe} isStripe={isStripe} />

        {isStripe ? (
          <Box
            width="100%"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Button
              onClick={() => {
                makePayment({
                  cartId: [],
                  eventId: [],
                  classesDetails: [],
                  clubsDetails: [],
                  price: data.price,
                  productData: [
                    {
                      name: data.title,
                      description: data.description,
                      images: ["https://www.patitofeo.com/patitoB.png"],
                      price: data.price,
                    },
                  ],
                  eventsPriceDetails: [],
                  currency: data.currency,
                  customProductsDetails: {
                    price: data.price,
                    currency: data.currency,
                    customProductsInfo: data._id,
                  },
                });
              }}
            >
              Buy Now
            </Button>
          </Box>
        ) : (
          <>
            <BankInfo />
            <Steps
              isTransferring={isTransferring}
              id={data._id}
              setIsTransferring={setIsTransferring}
            />
          </>
        )}
      </Card>
    </Box>
  );
};

const PaymentMethodTabs = ({ isStripe, setIsStripe }) => {
  return (
    <Box
      display="flex"
      gap={2}
      sx={{
        width: "100%",
        flexDirection: {
          xs: "column",
          md: "row",
        },
        background: "#8080802e",
        p: 1,
        borderRadius: 2,
        mb: 4,
      }}
    >
      <Tab
        text="Bank Transfer"
        isActive={!isStripe}
        onClick={() => {
          setIsStripe(false);
        }}
      >
        <AccountBalanceIcon />
      </Tab>
      <Tab
        isActive={isStripe}
        text="Pay with Stripe"
        onClick={() => {
          setIsStripe(true);
        }}
      >
        <CreditCardIcon />
      </Tab>
    </Box>
  );
};

const Tab = ({ text, children, isActive, onClick }) => {
  return (
    <Box
      sx={{
        cursor: "pointer",
        fontWeight: isActive ? 700 : 500,
        color: isActive ? "black" : "gray",
        width: {
          xs: "100%",
          md: "50%",
        },
        background: isActive ? "#8080802e" : "#********",
        display: "flex",
        flexDirection: "row",
        p: 2,
        gap: 4,
        fontSize: "0.65rem",
        borderRadius: 2,
        alignItems: "center",
        justifyContent: "center",
      }}
      onClick={onClick}
    >
      {children}
      <Typography fontSize="0.85rem">{text}</Typography>
    </Box>
  );
};

export default BankTransferDetails;
