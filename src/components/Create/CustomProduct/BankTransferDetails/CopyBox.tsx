import { useSnackbar } from "@/hooks/useSnackbar";
import { Box, Typography } from "@mui/material";
import React from "react";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";

type CopyBoxProps = React.FC<{
  name: string;
  value: string;
}>;
const CopyBox: CopyBoxProps = ({ name, value }) => {
  const { showSnackbar } = useSnackbar();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(value);
      showSnackbar(`Copied the ${name.toLocaleLowerCase()} successfully`, {
        type: "success",
      });
    } catch (err) {
      console.error("Failed to copy:", err);
      showSnackbar(
        `Failed to copy the ${name.toLowerCase()}.Please try again.`,
        {
          type: "error",
        }
      );
    }
  };
  return (
    <Box display="flex" flexDirection="column" width="100%" mt={3}>
      <Typography fontSize={10}>{name}</Typography>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        sx={{
          background: "#e3e3e3",
          borderRadius: "10px",
          padding: "10px",
        }}
      >
        <Typography fontSize="0.9rem">{value}</Typography>
        <Box
          onClick={handleCopy}
          sx={{
            backgroundColor: "#F3B358",
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            p: 1,
            borderRadius: "5px",
            cursor: "pointer",
          }}
        >
          <ContentCopyIcon
            sx={{
              fontSize: "1rem",
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default CopyBox;
