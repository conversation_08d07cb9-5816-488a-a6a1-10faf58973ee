import { Box, Typography } from "@mui/material";
import React from "react";

type StepIndexProps = React.FC<{
  index: number;
  title: string;
  helperText?: string;
  children: React.ReactNode;
}>;
const StepIndex: StepIndexProps = ({ index, children, title, helperText }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      width="100%"
      alignItems="start"
      mt={6}
      gap={3}
    >
      <Typography
        sx={{
          background: "#F3B358",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          height: 25,
          width: 25,
          borderRadius: 15,
          fontSize: "0.8rem",
        }}
      >
        {index}
      </Typography>
      <Box display="flex" flexDirection="column" width="100%">
        <Typography fontWeight={700} fontSize="0.85rem">
          {title}
        </Typography>

        {children}

        {helperText && (
          <Typography fontWeight={300} fontSize="0.75rem" color="gray">
            {title}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default StepIndex;
