import React from "react";
import { Card, Typography, Checkbox, Box, Avatar, Chip } from "@mui/material";

type VideoCardProps = React.FC<{
  isModal: boolean;
  isChecked?: boolean;
  onRemoveClicked?: () => void;
  onCheckedClicked?: (e: boolean) => void;
  data: any;
}>;

const VideoCard: VideoCardProps = ({
  isModal,
  onRemoveClicked,
  onCheckedClicked,
  data,
  isChecked,
}) => {
  const tags = ["Business", "Technology", "Swearing"];
  const [isSelected, setIsSelected] = React.useState(isChecked);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsSelected(event.target.checked);
  };

  return (
    <Card
      sx={{
        display: "flex",
        flexDirection: "row",
        width: "100%",
        p: 2,
        marginBottom: 4,
        boxShadow: "0px 2px 10px 0px rgba(0, 0, 0, 0.1)",
        gap: 2,
      }}
    >
      <Box width="15%">
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
          height={80}
          sx={{ borderRadius: 2, backgroundColor: "rgba(217, 217, 217, 1)" }}
        >
          <svg
            width="23"
            height="14"
            viewBox="0 0 23 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.11913 1.09521H12.9763C13.7593 1.09521 14.5102 1.40627 15.0639 1.95995C15.6176 2.51363 15.9287 3.26458 15.9287 4.0476V9.95236C15.9287 10.7354 15.6176 11.4863 15.0639 12.04C14.5102 12.5937 13.7593 12.9047 12.9763 12.9047H4.11913C3.33611 12.9047 2.58516 12.5937 2.03148 12.04C1.4778 11.4863 1.16675 10.7354 1.16675 9.95236V4.0476C1.16675 3.26458 1.4778 2.51363 2.03148 1.95995C2.58516 1.40627 3.33611 1.09521 4.11913 1.09521ZM15.9287 5.52379L19.4715 2.86664C19.6908 2.70216 19.9516 2.60199 20.2247 2.57737C20.4977 2.55275 20.7722 2.60465 21.0174 2.72725C21.2626 2.84985 21.4688 3.03831 21.6129 3.27152C21.7571 3.50472 21.8334 3.77345 21.8334 4.0476V9.95236C21.8334 10.2265 21.7571 10.4952 21.6129 10.7284C21.4688 10.9616 21.2626 11.1501 21.0174 11.2727C20.7722 11.3953 20.4977 11.4472 20.2247 11.4226C19.9516 11.398 19.6908 11.2978 19.4715 11.1333L15.9287 8.47617V5.52379Z"
              stroke="black"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </Box>
      </Box>
      <Box width="85%">
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Typography
            sx={{
              textAlign: "center",
              fontSize: 15,
            }}
            variant="h6"
            fontWeight="600"
            component="h2"
          >
            {data?.title}
          </Typography>
          <div>
            {isModal ? (
              <Checkbox
                sx={{ p: 0, color: "rgba(191, 191, 191, 1)" }}
                checked={isSelected}
                onChange={(e) => {
                  const checked = e.target.checked;
                  onCheckedClicked(checked);
                  handleChange(e);
                }}
              />
            ) : (
              <RemoveIcon onClick={onRemoveClicked} />
            )}
          </div>
        </Box>
        <Typography
          mb={2}
          fontSize={13}
          sx={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            color: "rgba(151, 151, 151, 1)",
          }}
        >
          {data?.description}
        </Typography>
        <Box display="flex" flexDirection="row" mb={5} sx={{ gap: 2 }}>
          {data?.categories?.map((m: string) => (
            <Tag text={m} key={m} />
          ))}
        </Box>
        <Box display="flex" flexDirection="row" justifyContent="space-between">
          <UserDetails
            src={data?.creator?.profileImageUrl}
            name={`${data?.creator?.firstName} ${data?.creator?.lastName}`}
          />
          <Typography fontSize={13} sx={{ color: "rgba(179, 179, 179, 1)" }}>
            {data.level}
          </Typography>
        </Box>
      </Box>
    </Card>
  );
};

const RemoveIcon = (props) => {
  return (
    <Box
      {...props}
      sx={{
        cursor: "pointer",
      }}
    >
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.758057 11.243L6.00106 6.00002L11.2441 11.243M11.2441 0.757019L6.00006 6.00002L0.758057 0.757019"
          stroke="black"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </Box>
  );
};

const UserDetails = ({
  src = "/static/images/avatar/1.jpg",
  name = "lmao shjroop",
}) => {
  return (
    <Box display="flex" flexDirection="row" alignItems="center" sx={{ gap: 2 }}>
      <Avatar
        sx={{ height: 12, width: 12, background: "rgba(109, 109, 109, 1)" }}
        alt="Remy Sharp"
        src={src}
      />
      <Typography fontSize={10} sx={{ color: "rgba(109, 109, 109, 1)" }}>
        {name}
      </Typography>
    </Box>
  );
};

const Tag = ({ text }) => {
  return (
    <Chip
      label={text}
      sx={{
        p: 1,
        background: "#D7F7F5",
        color: "#14A79C",
        height: 22,
        fontSize: 12,
      }}
    />
  );
};

export default VideoCard;
