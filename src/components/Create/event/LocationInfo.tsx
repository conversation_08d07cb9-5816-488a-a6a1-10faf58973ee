import React from "react";
import { Box, Typography } from "@mui/material";

type LocationProps = React.FC<{
  location: String;
}>;
const LocationInfo: LocationProps = ({ location }) => {
  return (
    <Box display="flex" flexDirection="row" alignItems="center">
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M6.18525 14.7267C7.04783 13.944 7.84615 13.0933 8.5725 12.1827C10.1025 10.2605 11.0332 8.36525 11.0963 6.68C11.1212 5.9951 11.0078 5.31219 10.7629 4.67209C10.518 4.03198 10.1466 3.4478 9.67087 2.95444C9.19515 2.46109 8.62486 2.06868 7.99409 1.80065C7.36331 1.53263 6.68498 1.39449 5.99963 1.39449C5.31427 1.39449 4.63594 1.53263 4.00516 1.80065C3.37439 2.06868 2.8041 2.46109 2.32838 2.95444C1.85265 3.4478 1.48125 4.03198 1.23634 4.67209C0.991443 5.31219 0.878071 5.9951 0.903 6.68C0.96675 8.36525 1.89825 10.2605 3.4275 12.1827C4.15385 13.0933 4.95217 13.944 5.81475 14.7267C5.89775 14.8018 5.9595 14.8562 6 14.8903L6.18525 14.7267ZM5.4465 15.6005C5.4465 15.6005 0 11.0135 0 6.5C0 4.9087 0.632141 3.38258 1.75736 2.25736C2.88258 1.13214 4.4087 0.5 6 0.5C7.5913 0.5 9.11742 1.13214 10.2426 2.25736C11.3679 3.38258 12 4.9087 12 6.5C12 11.0135 6.5535 15.6005 6.5535 15.6005C6.2505 15.8795 5.75175 15.8765 5.4465 15.6005ZM6 8.6C6.55695 8.6 7.0911 8.37875 7.48492 7.98492C7.87875 7.5911 8.1 7.05695 8.1 6.5C8.1 5.94305 7.87875 5.4089 7.48492 5.01508C7.0911 4.62125 6.55695 4.4 6 4.4C5.44305 4.4 4.9089 4.62125 4.51508 5.01508C4.12125 5.4089 3.9 5.94305 3.9 6.5C3.9 7.05695 4.12125 7.5911 4.51508 7.98492C4.9089 8.37875 5.44305 8.6 6 8.6ZM6 9.5C5.20435 9.5 4.44129 9.18393 3.87868 8.62132C3.31607 8.05871 3 7.29565 3 6.5C3 5.70435 3.31607 4.94129 3.87868 4.37868C4.44129 3.81607 5.20435 3.5 6 3.5C6.79565 3.5 7.55871 3.81607 8.12132 4.37868C8.68393 4.94129 9 5.70435 9 6.5C9 7.29565 8.68393 8.05871 8.12132 8.62132C7.55871 9.18393 6.79565 9.5 6 9.5Z"
          fill="#6D6D6D"
        />
      </svg>
      <Typography
        sx={{
          fontSize: 12,
          color: "#6D6D6D",
        }}
      >
        {location}
      </Typography>
    </Box>
  );
};

export default LocationInfo;
