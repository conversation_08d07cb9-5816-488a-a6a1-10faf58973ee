import { EventOnType } from "@/api/mongoTypes";
import { getBgColor } from "@/components/CustomButton";
import { CURRENCY_ENUM } from "@/constant/Enums";
import useCart from "@/hooks/useCart";
import usePayment from "@/hooks/usePayment";
import { useSnackbar } from "@/hooks/useSnackbar";
import { EventSchemaWithSingleEvent, MaybeObjectId } from "@/types";
import { getEventOnDetails, getRemainingSeats } from "@/utils/classes";
import { formatDate } from "@/utils/dateTime";
import { Box, Button, Card, Typography } from "@mui/material";
import React, { useMemo } from "react";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import ShoppingBagIcon from "@mui/icons-material/ShoppingBag";

type EventDateInfoProps = React.FC<{
  eventOn: MaybeObjectId<EventOnType>[] & (EventOnType | EventOnType[]);
  data: EventSchemaWithSingleEvent;
  isLoggedIn: boolean;
  redirectToSignup?: () => void;
}>;
const EventDateInfo: EventDateInfoProps = ({
  eventOn,
  data,
  isLoggedIn,
  redirectToSignup,
}) => {
  const isEventsOnArray = Array.isArray(eventOn);

  if (
    isEventsOnArray &&
    eventOn &&
    eventOn[0] &&
    "startDateTime" in eventOn[0]
  ) {
    const eventsOn = eventOn as EventOnType[];
    return (
      <Box display="flex" flexDirection="column">
        {eventsOn.map((m, i) => (
          <DateRow
            eventsOn={m}
            key={i}
            data={data}
            isLoggedIn={isLoggedIn}
            redirectToSignup={redirectToSignup}
          />
        ))}
      </Box>
    );
  }

  const eventsOn = eventOn as EventOnType;
  return (
    <DateRow
      eventsOn={eventsOn}
      data={data}
      isLoggedIn={isLoggedIn}
      redirectToSignup={redirectToSignup}
    />
  );
};

type DateRowProps = React.FC<{
  eventsOn: EventOnType;
  data: EventSchemaWithSingleEvent;
  isLoggedIn: boolean;
  redirectToSignup?: () => void;
}>;
const DateRow: DateRowProps = ({
  eventsOn,
  data,
  isLoggedIn,
  redirectToSignup,
}) => {
  const { startDate, sartTime, endDate, endTime, isStartEndDateSame } =
    useMemo(() => {
      return getEventOnDetails(eventsOn);
    }, [eventsOn]);

  const { isMaking, makePayment } = usePayment();
  const { handleAddToCart, isAddingToCart } = useCart({
    onDeleteSuccess: () => {},
    onAddingSuccess: () => {},
  });
  const { showSnackbar } = useSnackbar();

  const handleBuy = async () => {
    try {
      makePayment({
        cartId: [],
        price: data.price,
        productData: [
          {
            name: data.title,
            description: data.description,
            images: ["https://www.patitofeo.com/patitoB.png"],
            price: data.price,
          },
        ],
        eventId: [data._id],
        clubsDetails: [],
        classesDetails: [],
        eventsPriceDetails: [
          {
            eventInfo: String(data._id),
            price: +data.price,
            currency: data?.currency ?? CURRENCY_ENUM.USD,
            eventOn: eventsOn._id,
          },
        ],
        currency: data?.currency ?? CURRENCY_ENUM.USD,
      });
    } catch (error) {
      console.error("Something went wrong in handleBuy due to ", error);
      showSnackbar("Failed to buy event", {
        type: "error",
      });
    }
  };
  const remaingCount = getRemainingSeats(data, eventsOn);

  if (startDate < new Date()) {
    return <></>;
  }
  return (
    <Card
      sx={{
        display: "flex",
        flexDirection: "row",
        gap: 2,
        width: "100%",
        mt: 2,
        justifyContent: "space-between",
        p: 2,
        boxShadow: "0px 2px 10px 0px #0000000d;",
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        width="75%"
      >
        <Typography fontSize="0.75rem">
          {formatDate({ date: new Date(startDate) })}
          {isStartEndDateSame ? (
            <>
              &nbsp; {sartTime} - {endTime}
            </>
          ) : (
            <>
              &nbsp; {sartTime} - {formatDate({ date: new Date(endDate) })}{" "}
              {endTime}
            </>
          )}
        </Typography>
        {typeof remaingCount === "number" && (
          <Box
            sx={{
              backgroundColor: "#f3b35854",
              padding: 1,
              borderRadius: 10,
              px: 3,
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <Typography fontSize="0.75rem" mb={0}>
              Available Spots:
              <b>&nbsp;{remaingCount}</b>
            </Typography>
          </Box>
        )}
      </Box>
      <Box
        display="flex"
        alignItems="center"
        flexDirection="row"
        gap={2}
        width="25%"
      >
        <Button
          sx={{
            height: 30,
            width: 30,
            minWidth: 30,
            background: getBgColor("primary"),
            "&:hover": {
              background: getBgColor("primary"),
            },
          }}
          disabled={isAddingToCart || isMaking}
          onClick={(e) => {
            e.stopPropagation();
            handleAddToCart({
              eventId: isLoggedIn ? data?._id : data,
              eventOn: eventsOn,
            });
          }}
        >
          <ShoppingCartIcon sx={{ height: 20, width: 20 }} />
        </Button>
        <Button
          disabled={isMaking || isAddingToCart}
          onClick={(e) => {
            e.stopPropagation();
            if (isLoggedIn) {
              handleBuy();
            } else {
              localStorage.setItem("BUY_EVENT", JSON.stringify(data));
              redirectToSignup();
            }
          }}
          sx={{
            height: 30,
            width: 30,
            minWidth: 30,
            background: getBgColor("secondary"),
            "&:hover": {
              background: getBgColor("secondary"),
            },
          }}
        >
          <ShoppingBagIcon sx={{ height: 20, width: 20 }} />
        </Button>
      </Box>
    </Card>
  );
};

export default EventDateInfo;
