import React, { useEffect, useState } from "react";
import {
  Box,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Typography,
  Grid,
  Button,
  Card,
} from "@mui/material";
import { DatePicker, TimePicker } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useSnackbar } from "@/hooks/useSnackbar";
import CreateHeader from "@/components/Create/CreateHeader";
import SelectFileCard, { Remove } from "@/components/Create/SelectFileCard";
import OutlinedInput from "@mui/material/OutlinedInput";
import Chip from "@mui/material/Chip";
import { environment } from "@/api/aws";
import axios from "axios";
import { format, isBefore } from "date-fns";
import { v4 as uuidv4 } from "uuid";
import { categories } from "data/categories";
import { CURRENCY_ENUM, EVENT_MODE_TYPE } from "@/constant/Enums";
import { EventOnType, EventSchemaType, LanguageType } from "@/api/mongoTypes";
import dayjs from "dayjs";
import Image from "next/image";
import { validateEvent } from "@/utils/eventUtils";
import { uploadFileOnAws } from "@/utils/uploadFileOnAws";
import SelectTimeZone from "./SelectTimeZone";
import PriceInput from "@/components/PriceInput";
import { getEnglishAndSpanish } from "@/utils/common";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import ImagesCarousel from "@/components/classes/OnlineClub/ImagesCarousel";
import ImageSection from "../club/ImageSection";
import { EventSchemaWithSingleEvent } from "@/types";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "800px",
  alignItems: "center",
  width: "100%",
  margin: "0 auto",
  padding: { xs: "20px", sm: "0px" },
};
const eventSubHeaderStyles = {
  textAlign: "center",
  fontSize: "20px",
  fontWeight: "500",
  marginBottom: "33px",
};
const headerStyles = { marginBottom: "33px", maxWidth: "800px" };
const imageDisclaimerLabelStyles = {
  color: "#787878",
  fontWeight: "500",
  fontsize: "14px",
  marginBottom: "33px",
  marginTop: "20px",
};
const formBodyStyles = {
  display: "flex",
  flexDirection: "column",
  gap: 2,
  width: "100%",
  margin: "0 auto",
};
const eventTitleStyles = { marginBottom: "20px" };
const eventDescriptionStyles = { marginBottom: "20px" };
const LanguageProficiencyStyles = { marginBottom: "20px" };
const categoryStyles = { marginBottom: "50px" };
const categoryChipContainerStyles = {
  display: "flex",
  flexWrap: "wrap",
  gap: 1,
};
const chipStyles = {
  backgroundColor: "#D7F7F5",
  color: "#14A79C",
  textTransform: "uppercase",
};
const categoryMenuItemStyles = { textTransform: "uppercase" };
const modeTitleStyles = {
  textAlign: "center",
  fontWeight: "500",
  fontSize: "20px",
  marginBottom: "27px",
};
const modeContainerStyles = { marginBottom: "33px" };
const modeFormControlStyles = { marginBottom: "20px" };
const urlStyles = { marginBottom: "30px" };
const pickerStyles = { width: "100%" };
const buttonContainerStyles = {
  marginTop: "75px",
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  gap: "10px",
};

const validateURL = async (value) => {
  const urlRegex =
    /^(https?:\/\/)?([\w\-]+\.)+[a-z]{2,}(\/[\w\-._~:/?#[\]@!$&'()*+,;=]*)?$/i;
  if (!urlRegex.test(value)) {
    return false;
  }
  try {
    const { data } = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}api/utils/is-url-valid`,
      {
        url: value,
      }
    );
    return data.data;
  } catch (error) {
    return false;
  }
};

type EventBodyProps = React.FC<{
  proficiencies: any;
  id: string;
  data: EventSchemaWithSingleEvent;
  languages: LanguageType[];
}>;

const EventBody: EventBodyProps = ({
  data = null,
  proficiencies,
  languages,
}) => {
  const isUpdate = !!data?._id;
  const { showSnackbar } = useSnackbar();
  const [file, setFile] = useState(null);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [languageProficiency, setLanguageProficiency] = useState("");
  const [category, setCategory] = useState([]);
  const [mode, setMode] = useState("");
  const [url, setUrl] = useState("");
  const [location, setLocation] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [isUrlValid, setIsUrlValid] = useState(false);
  const [price, setPrice] = useState<string | number>(10);
  const [currency, setCurrency] = useState<CURRENCY_ENUM>(CURRENCY_ENUM.MXN);
  const [maxNumberOfRegistrations, setMaxNumberOfRegistrations] = useState(1);
  const [targetLanguage, setTargetLanguage] = useState("");
  const [eventOn, setEventOn] = useState([]);
  const [isDeleting, setIsDeleting] = useState(false);
  // const [selectedEventImg, setSelectedEventImg] = useState(data?.eventImageUrl);
  const [updateImg, setUpdateImg] = useState(null);
  const [showSelect, setShowSelect] = useState(false);
  const [deletedImageKeys, setDeletedImageKeys] = useState([]);
  const [images, setImages] = useState([]);
  const [imagesKeysAndIds, setImagesKeysAndIds] = useState([]);

  const router = useRouter();

  // useEffect(() => {
  //   setSelectedEventImg(data?.eventImageUrl);
  // }, [data?.eventImageUrl]);

  const handleSelectUpdateImg = (event) => {
    try {
      if (event.target.files && event.target.files.length > 0) {
        const selectedFile = event.target.files[0];
        setUpdateImg(selectedFile);
      }
    } catch (error) {
      console.error("Something went wrong in handleSelectFile due to ", error);
      showSnackbar("Something went wrong in selecting file", {
        type: "error",
      });
    }
  };

  useEffect(() => {
    if (mode === EVENT_MODE_TYPE.ONLINE) {
      (async () => {
        setIsValidating(true);
        const isvalid = await validateURL(url);
        setIsUrlValid(isvalid);
        setIsValidating(false);
      })();
    } else {
      setIsValidating(false);
      setIsUrlValid(true);
    }
  }, [url, mode]);

  function getDateWithTime(time) {
    const currentDate = dayjs().format("YYYY-MM-DD"); // Extract current date
    const dateTimeString = `${currentDate}T${time}`; // Combine with time
    return dayjs(dateTimeString); // Return as Day.js object
  }

  const storeLocalData = (data) => {
    setTitle(data?.title);
    setDescription(data?.description);
    setCategory(data?.categories);
    setMode(data?.mode);
    setLocation(data?.location);
    setUrl(data?.url);
    setLanguageProficiency(data?.proficiencyLevel?._id);
    setPrice(data?.price ? +data?.price : 0);
    setCurrency(data?.currency ?? CURRENCY_ENUM.MXN);
    setMaxNumberOfRegistrations(data.maxNumberOfRegistrations);
    setTargetLanguage(data.targetLanguage);
    setEventOn(
      data.eventOn.map((m) => ({
        ...m,
        startDate: dayjs(m.startDate),
        startTime: getDateWithTime(m.startTime),
        endDate: dayjs(m.endDate),
        endTime: getDateWithTime(m.endTime),
      }))
    );
    setImages(data.images);
    setImagesKeysAndIds(data.imagesKeysAndIds);
  };

  const INITIAL = {
    startDate: null,
    startTime: null,
    timezone: "",
    endDate: null,
    endTime: null,
  };

  useEffect(() => {
    if (data) {
      storeLocalData(data);
    } else {
      setEventOn([INITIAL]);
    }
  }, [data]);

  const handleSelectFile = async (event) => {
    try {
      if (event.target.files && event.target.files.length > 0) {
        const selectedFile = event.target.files[0];
        setFile(selectedFile); // Assuming `setFile` is defined elsewhere
      }
    } catch (error) {
      console.error("Something went wrong in handleSelectFile due to ", error);
      showSnackbar("Something went wrong in selecting file", {
        type: "error",
      });
    }
  };

  const handleTitleInputChange = (event) => {
    setTitle(event.target.value);
  };

  const handleDescriptionInputChange = (event) => {
    setDescription(event.target.value);
  };
  const handleLanguageProficiencySelectChange = (event) => {
    setLanguageProficiency(event.target.value);
  };
  const handleModeChange = (event) => {
    setMode(event.target.value);
  };
  const handleUrlChange = (event) => {
    const value = event.target.value;
    setLocation("");
    setUrl(value);
  };
  const handleLocationChange = (event) => {
    setUrl("");
    setLocation(event.target.value);
  };
  const uploadEventImage = async (file, id) => {
    const fileName = file?.name;
    const data = await uploadFileOnAws({
      file: file,
      key: `${environment}/event/${id}/event_${fileName}`,
    });
    return data;
  };

  const handleCategoryChange = (event) => {
    const {
      target: { value },
    } = event;
    setCategory(typeof value === "string" ? value.split(",") : value);
  };

  const handleReset = () => {
    if (isUpdate) {
      storeLocalData(data);
    } else {
      setFile(null);
      setTitle("");
      setDescription("");
      setLanguageProficiency("");
      setCategory([]);
      setMode("");
      setUrl("");
      setLocation("");
      setIsUrlValid(false);
      setPrice(0);
      setMaxNumberOfRegistrations(1);
      setTargetLanguage("");
      setImagesKeysAndIds([]);
      setImages([]);
    }
  };

  const handleCreateEvent = async () => {
    const error = validateEvent({
      title,
      description,
      languageProficiency,
      category,
      mode,
      location,
      url,
      price,
      currency,
      maxNumberOfRegistrations,
      targetLanguage,
      eventOn,
    });
    if (error) {
      showSnackbar(error, {
        type: "warning",
      });
      return;
    }
    try {
      setIsCreating(true);
      let imagesKeysAndIds = [];
      if (images.length > 0) {
        await Promise.all(
          images.map(async (m) => {
            const clubImageId = uuidv4();
            const uploadedEventImage = await uploadEventImage(m, clubImageId);
            if (!uploadedEventImage.isError && uploadedEventImage.data.Key) {
              const newKeyAndId = {
                key: uploadedEventImage.data.Key,
                id: clubImageId,
              };
              imagesKeysAndIds.push(newKeyAndId);
            }
          })
        );
      }
      if (imagesKeysAndIds.length === 0) {
        showSnackbar("Failed to upload image,please try again", {
          type: "warning",
        });
        return;
      }
      const payload = {
        title,
        description,
        mode,
        proficiencyLevel: languageProficiency,
        // eventImageId,
        // eventImageKey: uploadedEventImage.data.Key,
        imagesKeysAndIds,
        categories: category,
        location,
        url,
        price: +price,
        currency,
        maxNumberOfRegistrations,
        targetLanguage,
        eventOn: eventOn.map((m) => ({
          ...m,
          startDate: format(m.startDate.$d, "yyyy-MM-dd"),
          startTime: format(m.startTime.$d, "HH:mm:ss"),
          // startDate will be same as endDate, i am passing startDate and is not a bug
          endDate: format(m.startDate.$d, "yyyy-MM-dd"),
          endTime: format(m.endTime.$d, "HH:mm:ss"),
        })),
      };
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/event/create`,
        payload
      );
      if (data.success && data?.data?._id) {
        handleReset();
        showSnackbar("Created event successfully", {
          type: "success",
        });
        router.push("/create/event");
      } else {
        showSnackbar("Something went wrong while creating Event", {
          type: "error",
        });
      }
      setIsCreating(false);
    } catch (error) {
      console.error("serror", error);
      setIsCreating(false);
      showSnackbar("Something went wrong while creating Event", {
        type: "error",
      });
    }
  };

  const handleUpdateEvent = async () => {
    const error = validateEvent({
      title,
      description,
      languageProficiency,
      category,
      mode,
      location,
      url,
      price,
      currency,
      maxNumberOfRegistrations,
      targetLanguage,
      eventOn,
    });
    if (error) {
      showSnackbar(error, {
        type: "warning",
      });
      return;
    }

    const handleError = () => {
      setIsUpdating(false);
      showSnackbar("Something went wrong while updating Event", {
        type: "error",
      });
    };

    try {
      setIsUpdating(true);
      let allImagesKeysAndIds = [];
      const localImages = images.filter((f) => f?.name);
      allImagesKeysAndIds = [...imagesKeysAndIds];
      if (localImages.length > 0) {
        await Promise.all(
          localImages.map(async (m) => {
            const clubImageId = uuidv4();
            const uploadedEventImage = await uploadEventImage(m, clubImageId);
            if (!uploadedEventImage.isError && uploadedEventImage.data.Key) {
              const newIds = {
                key: uploadedEventImage.data.Key,
                id: clubImageId,
              };
              allImagesKeysAndIds.push(newIds);
            }
          })
        );
      }
      const filteredIds = allImagesKeysAndIds.filter(
        (f) => !deletedImageKeys.includes(f.id)
      );
      const { data: updatedEvent } = await axios.put(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/event/update`,
        {
          id: data._id,
          title,
          description,
          mode,
          proficiencyLevel: languageProficiency,
          categories: category,
          location,
          url,
          price: +price,
          currency,
          imagesKeysAndIds: filteredIds,
          deletedImageKeys,
          maxNumberOfRegistrations,
          targetLanguage,
          eventOn: eventOn.map((m) => ({
            ...m,
            startDate: format(m.startDate.$d, "yyyy-MM-dd"),
            startTime: format(m.startTime.$d, "HH:mm:ss"),
            // startDate will be same as endDate, i am passing startDate and is not a bug
            endDate: format(m.startDate.$d, "yyyy-MM-dd"),
            endTime: format(m.endTime.$d, "HH:mm:ss"),
          })),
        }
      );
      if (updatedEvent.success && updatedEvent?.data?._id) {
        showSnackbar("Updated event successfully", {
          type: "success",
        });
        router.push("/create/event");
      } else {
        showSnackbar("Something went wrong while updating Event", {
          type: "error",
        });
      }
      setIsUpdating(false);
    } catch (error) {
      console.error("Something went wrong while updating the event", error);
      handleError();
    }
  };

  const handleDelete = async () => {
    const totalCount = Object.values(data?.boughtCount ?? {}).reduce(
      (sum, val) => sum + val,
      0
    );

    if (totalCount > 0) {
      showSnackbar(
        `You cannot delete this event as it has been booked by ${totalCount} users`,
        {
          type: "error",
        }
      );
      return;
    }

    const handleError = () => {
      setIsDeleting(true);
      showSnackbar("Failed to delete event", {
        type: "error",
      });
    };

    try {
      const { data: deletedData, status } = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/event/delete/${data._id}`
      );
      if (status === 409) {
        showSnackbar(deletedData.message, {
          type: "error",
        });
        return;
      }
      if (deletedData.success && deletedData?.data) {
        showSnackbar("Deleted the event successfully", {
          type: "success",
        });
        router.push("/create/event");
      } else {
        handleError();
      }
      setIsDeleting(false);
    } catch (error) {
      handleError();
    }
  };

  const englishSpanishLanguages = getEnglishAndSpanish(languages);
  const isDisabled = isValidating || isCreating || isUpdating || isDeleting;

  return (
    <Box sx={containerStyles}>
      <CreateHeader
        text={isUpdate ? "Update Event" : "Event Creation"}
        bottomBorderNeeded
        backRoute="/create/event"
        style={headerStyles}
      />
      <Typography sx={eventSubHeaderStyles}>Event Details</Typography>
      {images.length > 0 && (
        <ImagesCarousel
          key={images.length}
          sx={{ mb: 5 }}
          images={images.map((m) => {
            if (m?.name) {
              return URL.createObjectURL(m);
            }
            return m.url;
          })}
          currency={currency}
          price={price}
        />
      )}
      <ImageSection
        images={images}
        showSnackbar={showSnackbar}
        setImages={setImages}
        isMultiple
        setDeletedImageKeys={setDeletedImageKeys}
      />
      <Box sx={formBodyStyles}>
        {/* Event Title Input */}
        <TextField
          label="Enter Event Title"
          variant="outlined"
          fullWidth
          value={title}
          sx={eventTitleStyles}
          onChange={handleTitleInputChange}
        />
        <PriceInput
          currency={currency}
          label="Enter Event price"
          price={price}
          onCurrencyChanged={(cur) => {
            setCurrency(cur);
          }}
          onPriceChanged={(val) => {
            if (val === "" || isNaN(val)) {
              setPrice("");
            } else {
              const numericValue = +val;
              if (numericValue > 0) {
                setPrice(val);
              }
            }
          }}
        />
        <TextField
          label="Maximum number of registrations"
          variant="outlined"
          fullWidth
          type="number"
          value={maxNumberOfRegistrations}
          onChange={(e) => {
            const val = +e.target.value;
            setMaxNumberOfRegistrations(val);
          }}
        />
        <FormControl
          fullWidth
          sx={{
            my: 2,
          }}
        >
          <InputLabel id="target-language-label" shrink>
            Target Language
          </InputLabel>
          <Select
            labelId="target-language-label"
            id="target-language"
            value={targetLanguage}
            onChange={(event) => setTargetLanguage(event.target.value)}
            label="Target Language"
          >
            {englishSpanishLanguages.map((languageLevel) => (
              <MenuItem key={languageLevel._id} value={languageLevel._id}>
                {languageLevel.nameInEnglish}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {/* Description Input */}
        <TextField
          label="Enter Description"
          variant="outlined"
          value={description}
          onChange={handleDescriptionInputChange}
          multiline
          rows={3}
          fullWidth
          sx={eventDescriptionStyles}
        />
        {/* Language Proficiency Dropdown */}
        <FormControl fullWidth sx={LanguageProficiencyStyles}>
          <InputLabel>Language Proficiency</InputLabel>
          <Select
            label="languageProficiency"
            value={languageProficiency}
            onChange={handleLanguageProficiencySelectChange}
          >
            {proficiencies?.map((languageLevel) => (
              <MenuItem key={languageLevel._id} value={languageLevel._id}>
                {languageLevel.pfLevel.en}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {/* Category Dropdown */}
        <FormControl fullWidth sx={categoryStyles}>
          <InputLabel>Categories</InputLabel>
          <Select
            multiple
            value={category}
            onChange={handleCategoryChange}
            input={<OutlinedInput id="select-multiple-chip" label="Chip" />}
            renderValue={(selected) => (
              <Box sx={categoryChipContainerStyles}>
                {selected.map((value) => (
                  <Chip key={value} label={value} sx={chipStyles} />
                ))}
              </Box>
            )}
          >
            {categories.map((category) => (
              <MenuItem
                key={category.name}
                value={category.name}
                sx={categoryMenuItemStyles}
              >
                {category.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {/* Mode Dropdown */}
        <Typography sx={modeTitleStyles}>Location</Typography>
        <Box sx={modeContainerStyles}>
          <FormControl fullWidth sx={modeFormControlStyles}>
            <InputLabel>Select Mode</InputLabel>
            <Select label="mode" value={mode} onChange={handleModeChange}>
              <MenuItem value="ONLINE">Online</MenuItem>
              <MenuItem value="OFFLINE">Offline</MenuItem>
            </Select>
          </FormControl>
          {/* URL option if mode is set to online  */}
          {mode == "ONLINE" && (
            <TextField
              label="Enter Event URL"
              variant="outlined"
              fullWidth
              type="url"
              sx={{
                ...urlStyles,
                "& .MuiOutlinedInput-root": {
                  "& fieldset": {
                    borderColor: isUrlValid ? "default" : "red",
                  },
                  "&:hover fieldset": {
                    borderColor: isUrlValid ? "default" : "red",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: isUrlValid ? "default" : "red",
                  },
                },
              }}
              value={url}
              onChange={handleUrlChange}
              error={!isUrlValid}
              helperText={
                <>
                  {isValidating ? (
                    "Validating URL..."
                  ) : isUrlValid ? (
                    ""
                  ) : (
                    <Typography sx={{ color: "red" }}>Invalid URL</Typography>
                  )}
                </>
              }
            />
          )}
          {mode == "OFFLINE" && (
            <TextField
              label="Enter Location Address"
              variant="outlined"
              fullWidth
              sx={urlStyles}
              value={location}
              onChange={handleLocationChange}
            />
          )}
        </Box>

        <Card sx={{ p: 4 }}>
          <Typography variant="h6" textAlign="center" mb={3}>
            Date & Time
          </Typography>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            {eventOn.map((m, i) => (
              <Card key={i} sx={{ p: 4, border: "1px solid silver", mb: 4 }}>
                <Box
                  display="flex"
                  flexDirection="row"
                  width="100%"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Typography>{i + 1} Date/time</Typography>
                  {i !== 0 && (
                    <Button
                      onClick={() => {
                        setEventOn((prev) => prev.filter((f, j) => j !== i));
                      }}
                      sx={{
                        width: "fit-content",
                        background: "red",
                        "&:hover": {
                          background: "red",
                        },
                      }}
                    >
                      <DeleteIcon />
                    </Button>
                  )}
                </Box>
                <SelectTimeZone
                  value={m.timezone}
                  onChange={(event) => {
                    setEventOn((prev) =>
                      prev.map((m, j) => {
                        if (i === j) {
                          return {
                            ...m,
                            timezone: event.target.value,
                          };
                        } else {
                          return m;
                        }
                      })
                    );
                  }}
                />
                <Grid container spacing={2} marginTop={2}>
                  <Grid item xs={6}>
                    <DatePicker
                      sx={pickerStyles}
                      label="Start Date"
                      value={m.startDate ? dayjs(m.startDate) : null}
                      disablePast={!isUpdate}
                      onChange={(newValue) => {
                        setEventOn((prev) =>
                          prev.map((m, j) => {
                            if (i === j) {
                              return {
                                ...m,
                                startDate: newValue,
                              };
                            } else {
                              return m;
                            }
                          })
                        );
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TimePicker
                      sx={pickerStyles}
                      label="Start Time"
                      value={m.startTime ? dayjs(m.startTime) : null}
                      onChange={(newValue) => {
                        setEventOn((prev) =>
                          prev.map((m, j) => {
                            if (i === j) {
                              return {
                                ...m,
                                startTime: newValue,
                              };
                            } else {
                              return m;
                            }
                          })
                        );
                      }}
                    />
                  </Grid>
                </Grid>
                <Grid container spacing={2} marginTop={2}>
                  <Grid item xs={6}>
                    <TimePicker
                      sx={pickerStyles}
                      label="End Time"
                      value={m.endTime ? dayjs(m.endTime) : null}
                      onChange={(newValue) => {
                        // setStartDate(newValue);
                        setEventOn((prev) =>
                          prev.map((m, j) => {
                            if (i === j) {
                              return {
                                ...m,
                                endTime: newValue,
                              };
                            } else {
                              return m;
                            }
                          })
                        );
                      }}
                    />
                  </Grid>
                </Grid>
              </Card>
            ))}
            <Box
              p={2}
              width="100%"
              display="flex"
              flexDirection="row"
              alignItems="center"
              justifyContent="end"
            >
              <Button
                onClick={() => {
                  setEventOn((prev) => [...prev, INITIAL]);
                }}
                sx={{
                  width: "fit-content",
                }}
              >
                <AddIcon />
                <Typography>Add</Typography>
              </Button>
            </Box>
          </LocalizationProvider>
        </Card>
        <Box sx={buttonContainerStyles}>
          <Button onClick={handleReset} disabled={isCreating}>
            Reset
          </Button>
          {!isUpdate ? (
            <>
              <Button
                disabled={!isUrlValid || isValidating || isCreating}
                onClick={handleCreateEvent}
              >
                Save
              </Button>
            </>
          ) : (
            <>
              <Button
                sx={{
                  background: "red",
                  "&:hover": {
                    background: "red",
                  },
                }}
                disabled={isDisabled}
                onClick={handleDelete}
              >
                Delete
              </Button>
              <Button
                disabled={!isUrlValid || isValidating || isUpdating}
                onClick={handleUpdateEvent}
              >
                Update
              </Button>
            </>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default EventBody;
