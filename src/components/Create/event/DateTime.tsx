import { formatDate, formatTime, getDateAsPerUserTZ } from "@/utils/dateTime";
import { Box, Typography } from "@mui/material";
import React from "react";

type DateTimeProps = React.FC<{
  isStart: boolean;
  dateTime: Date;
  timezone: string;
}>;
const DateTime: DateTimeProps = ({ isStart, timezone, dateTime }) => {
  const dateAsPerUserTZ = getDateAsPerUserTZ({
    dateTime,
    timezone,
  });

  return (
    <Box display="flex" flexDirection="column">
      <Typography sx={{ fontSize: 11, color: "#818181" }}>
        {isStart ? "Start" : "End"}
      </Typography>
      <Typography sx={{ color: "#3C3C3C", fontSize: 12, fontWeight: 600 }}>
        {formatDate({ date: new Date(dateAsPerUserTZ) })}{" "}
        {formatTime({ date: new Date(dateAsPerUserTZ) })}
      </Typography>
    </Box>
  );
};

export default DateTime;
