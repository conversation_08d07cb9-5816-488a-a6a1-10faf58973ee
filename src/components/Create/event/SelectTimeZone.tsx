import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { timezone } from "data/timezone";
import React, { useMemo, useState } from "react";
import InputAdornment from "@mui/material/InputAdornment";
import SearchIcon from "@mui/icons-material/Search";

const SelectTimeZone = ({ value, onChange }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [open, setOpen] = useState(false);

  const filteredAndSortedLanguages = useMemo(() => {
    return timezone
      .filter((tz) => tz.name.toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [timezone, searchTerm]);

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleSelectChange = (event) => {
    onChange(event);
    setOpen(false);
    setSearchTerm("");
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSearchTerm(""); // Clear search when closing
  };

  return (
    <FormControl fullWidth margin="normal">
      <InputLabel id="timezone-label">Time Zone</InputLabel>
      <Select
        labelId="timezone-label"
        value={value}
        onChange={handleSelectChange}
        onOpen={handleOpen}
        onClose={handleClose}
        open={open}
        MenuProps={{
          PaperProps: {
            sx: {
              maxHeight: 300,
            },
          },
        }}
      >
        <Box
          sx={{
            px: 2,
            pb: 1,
            pt: 1,
            position: "sticky",
            top: 0,
            backgroundColor: "white",
            zIndex: 1,
          }}
        >
          <TextField
            size="small"
            placeholder="Search timezones..."
            value={searchTerm}
            onChange={handleSearchChange}
            onClick={(e) => e.stopPropagation()}
            onKeyDown={(e) => e.stopPropagation()}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{
              width: "100%",
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: "rgba(0, 0, 0, 0.23)",
                },
                "&:hover fieldset": {
                  borderColor: "rgba(0, 0, 0, 0.87)",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "rgba(0, 95, 86, 1)",
                },
              },
            }}
          />
        </Box>

        {filteredAndSortedLanguages.length > 0 ? (
          filteredAndSortedLanguages.map((zone) => (
            <MenuItem value={zone.tzCode} key={zone.tzCode}>
              {zone.name}
            </MenuItem>
          ))
        ) : (
          <MenuItem disabled>
            <Box sx={{ color: "text.secondary", fontStyle: "italic" }}>
              No timezone found
            </Box>
          </MenuItem>
        )}
      </Select>
    </FormControl>
  );
};

export default SelectTimeZone;
