import { Box, Button, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";
import React from "react";

const NoEvent = () => {
  const router = useRouter();

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      sx={{
        height: "100%",
        width: "100%",
        minHeight: "70vh",
      }}
    >
      <Box
        sx={{
          position: "relative",
          height: "60%",
          minHeight: 250,
          width: "100%",
        }}
        mt={10}
      >
        <Image alt="images" src="/images/cart/empty.svg" fill />
      </Box>

      <Typography textAlign="center" mt={10}>
        No such Community experience
      </Typography>

      <Button
        onClick={() => {
          router.back();
        }}
        sx={{
          backgroundColor: "#14A79C",
          width: {
            xs: "100%",
            sm: "90%",
            md: "30%",
          },
          fontWeight: "500",
          fontSize: 14,
          borderRadius: 1,
          marginTop: 10,
        }}
      >
        Go Back
      </Button>
    </Box>
  );
};

export default NoEvent;
