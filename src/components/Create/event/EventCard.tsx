import { Box, Card, Chip, Typography } from "@mui/material";
import Image from "next/image";
import React, { useState } from "react";
import ModeStatus from "./ModeStatus";
import LocationInfo from "./LocationInfo";
import Tag from "@/components/Tag";
import { EVENT_MODE_TYPE } from "@/constant/Enums";
import { formatDate, formatTime } from "@/utils/dateTime";
import CustomButton from "@/components/CustomButton";
import { useSnackbar } from "@/hooks/useSnackbar";
import { getPriceSymbol } from "@/utils/classes";
import { getLanguageName, getProficiencyEn } from "@/utils/common";
import axiosInstance from "@/utils/interceptor";
import { EventSchemaWithSingleEvent } from "@/types";
import TargetLanguage from "@/components/classes/TargetLanguage";
import EventDateInfo from "./EventDateInfo";

type EventCardProps = React.FC<{
  data: EventSchemaWithSingleEvent;
  onClick?: () => void;
  redirectToSignup?: () => void;
  isLoggedIn?: boolean;
  isAdmin?: boolean;
  onDelete?: () => void;
}>;

const EventCard: EventCardProps = ({
  data,
  onClick,
  isLoggedIn,
  redirectToSignup,
  isAdmin = false,
  onDelete = () => {},
}) => {
  const { showSnackbar } = useSnackbar();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    const totalCount = Object.values(data.boughtCount).reduce(
      (sum, val) => sum + val,
      0
    );

    if (totalCount > 0) {
      showSnackbar(
        `You cannot delete this event as it has been booked by ${totalCount} users`,
        {
          type: "error",
        }
      );
      return;
    }

    const handleError = () => {
      setIsDeleting(true);
      showSnackbar("Failed to delete event", {
        type: "error",
      });
    };

    try {
      const { data: deletedData, status } = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/event/delete/${data._id}`
      );
      if (status === 409) {
        showSnackbar(deletedData.message, {
          type: "error",
        });
        return;
      }
      if (deletedData.success && deletedData?.data) {
        showSnackbar("Deleted the event successfully", {
          type: "success",
        });
        onDelete && onDelete();
      } else {
        handleError();
      }
      setIsDeleting(false);
    } catch (error) {
      console.log("erropr", error);
      handleError();
    }
  };

  const proficiency = getProficiencyEn({
    data: data?.proficiencyLevel,
  });

  return (
    <Card
      onClick={() => {
        if (onClick) {
          onClick();
        }
      }}
      sx={{
        height: 450,
        // height: isAdmin ? 450 : 350,
        width: {
          xs: "100%",
          md: 350,
          sm: 275,
        },
        borderRadius: 5,
        p: 4,
        boxShadow: "0px 2px 10px 0px #00000029",
        flexDirection: "column",
        display: "flex",
        justifyContent: "space-between",
        // border: isExpired ? "1px solid red" : "",
        position: "relative",
      }}
    >
      {/* {isExpired && (
        <Box
          sx={{
            background: "red",
            color: "#fff",
            padding: "5px 10px",
            borderRadius: 10,
            position: "absolute",
            top: 5,
            right: 5,
            zIndex: 100,
            fontSize: 12,
          }}
        >
          <Typography fontSize={12}>Expired</Typography>
        </Box>
      )} */}

      <Box
        width="100%"
        height={150}
        sx={{ position: "relative", overflow: "hidden", borderRadius: 3 }}
      >
        <Image
          fill
          alt="beruh"
          src={data?.images[0]?.url ?? ""}
          quality={75}
          objectFit="cover"
          priority
        />

        {isAdmin ? (
          <>
            {proficiency && (
              <Chip
                label={proficiency}
                sx={{
                  position: "absolute",
                  top: 5,
                  left: 5,
                  fontSize: 12,
                  textTransform: "uppercase",
                  padding: 0.2,
                  borderRadius: 1,
                  height: "auto",
                  background: "rgba(0, 0, 0, 0.52)",
                  color: "#fff",
                  "& .MuiChip-label": {
                    fontSize: 10,
                    fontWeight: "normal",
                    color: "#fff",
                    padding: "0.2rem",
                  },
                }}
              />
            )}
            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              justifyContent="space-between"
              width="100%"
              p={1}
              sx={{
                position: "absolute",
                bottom: 2,
              }}
            >
              <Box
                sx={{
                  background: "rgba(0, 0, 0, 0.52)",
                  color: "#fff",
                  p: 2,
                  borderRadius: 2,
                }}
              >
                <Typography sx={{ fontSize: 12 }}>
                  {getPriceSymbol({
                    currency: data.currency,
                  })}
                  {data.price}
                  <span style={{ fontSize: 10, fontWeight: 400 }}>
                    &nbsp;onwards
                  </span>
                </Typography>
              </Box>
            </Box>
          </>
        ) : (
          <>
            <Typography
              sx={{
                position: "absolute",
                top: 5,
                left: 5,
                fontSize: 12,
                color: "#fff",
              }}
            >
              Created at : {formatTime({ date: data.createdAt })},
              {formatDate({ date: data.createdAt })}
            </Typography>
            {proficiency && (
              <Chip
                label={proficiency}
                sx={{
                  position: "absolute",
                  bottom: 10,
                  right: 10,
                  fontSize: 12,
                  textTransform: "uppercase",
                  padding: 0.2,
                  borderRadius: 1,
                  height: "auto",

                  color: "#6D6D6D",
                  background: "#FFFFFF",
                  "& .MuiChip-label": {
                    fontSize: 12, // Adjust Typography font size
                    fontWeight: "normal", // Example styling
                    color: "#6D6D6D", // Change text color
                    padding: "0.2rem",
                  },
                }}
              />
            )}
          </>
        )}
      </Box>

      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <ModeStatus isOnline={data.mode === EVENT_MODE_TYPE.ONLINE} />
        {data?.location && <LocationInfo location={data.location} />}
      </Box>
      <Typography sx={{ fontWeight: "bolder", fontSize: 16 }}>
        {data.title}
      </Typography>
      <Typography
        textOverflow="ellipsis"
        sx={{
          fontSize: 15,
          overflow: "hidden",
          color: "#B3B3B3",
          whiteSpace: "nowrap",
          fontWeight: 300,
        }}
      >
        {data.description}
      </Typography>

      <Box display="flex" flexDirection="row" alignItems="center">
        {data?.categories?.map((m) => (
          <Tag text={m} key={m} />
        ))}
      </Box>

      <TargetLanguage name={getLanguageName(data?.targetLanguage)} />

      <EventDateInfo
        isLoggedIn={isLoggedIn}
        eventOn={data?.eventOn}
        data={data}
        redirectToSignup={redirectToSignup}
      />

      {isAdmin ? (
        <CustomButton
          disabled={isDeleting}
          text="Delete"
          colortype="secondary"
          sx={{
            width: "100%",
            background: "red",
            mt: 3,
            "&:hover": {
              background: "red",
            },
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleDelete();
          }}
        />
      ) : (
        <></>
      )}
    </Card>
  );
};

export default EventCard;
