import { environment } from "@/api/aws";
import { useSnackbar } from "@/hooks/useSnackbar";
import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { v4 as uuidv4 } from "uuid";
import ExistingVideosModal from "@/components/Create/modal/ExistingVideosModal";
import UploadVideosModal from "@/components/Create/modal/UploadVideosModal";
import CreateHeader from "@/components/Create/CreateHeader";
import SelectFileCard from "@/components/Create/SelectFileCard";
import CustomButton from "@/components/CustomButton";
import VideoCard from "@/components/Create/VideoCard";
import { uploadFileOnAws } from "@/utils/uploadFileOnAws";
import Head from "next/head";
import { useRouter } from "next/router";
import { LanguageProficiencyType } from "@/api/mongoTypes";
import axiosInstance from "@/utils/interceptor";

const BASE_URL = "/create/collection";

type CreateEditCollectionProps = React.FC<{
  data: any;
  proficiencies: LanguageProficiencyType[];
}>;
const CreateEditCollection: CreateEditCollectionProps = ({
  data,
  proficiencies,
}) => {
  const router = useRouter();
  const isUpdate = !!data?._id;
  console.log({
    data,
    isUpdate,
  });
  const { showSnackbar } = useSnackbar();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [file, setFile] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [languageProficiency, setLanguageProficiency] = useState("");

  const [isExistingOpen, setIsExistingOpen] = useState(false);
  const [isUVideoOpen, setUVideoOpen] = useState(false);
  const [existingCollection, setExistingCollection] = useState([]);

  const handleSelectFile = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      if (event.target.files && event.target.files.length > 0) {
        const selectedFile = event.target.files[0];
        setFile(selectedFile);
      }
    } catch (error) {
      console.error(`omething went wrong in handleSelectFile due to `, error);
      showSnackbar("Something went wrong in selecting file", {
        type: "error",
      });
    }
  };

  const getCategories = () => {
    let array = [];
    existingCollection.map((m) => {
      m.categories.map((n) => {
        if (!array.includes(n)) {
          array.push(n);
        }
      });
    });
    return array;
  };

  const uploadImageTos3 = async (id: string) => {
    const avatarFileName = file?.name;
    const data = await uploadFileOnAws({
      file,
      key: `${environment}/videocollection/${id}/collection_${avatarFileName}`,
    });
    return data;
  };

  const handleCreateCollection = async () => {
    const handleError = () => {
      showSnackbar("Failed to create collection", {
        type: "error",
      });
      setIsCreating(false);
    };

    if (!name) {
      showSnackbar("Please enter name", {
        type: "warning",
      });
      return;
    }
    if (!description) {
      showSnackbar("Please enter description", {
        type: "warning",
      });
      return;
    }
    if (!file) {
      showSnackbar("Please select collection image", {
        type: "warning",
      });
      return;
    }
    // if (existingCollection.length === 0) {
    //   showSnackbar(
    //     "Please select atleast one existing video or create a new one",
    //     {
    //       type: "warning",
    //     }
    //   );
    //   return;
    // }
    try {
      setIsCreating(true);
      const imageId = uuidv4();
      const uploadedImage = await uploadImageTos3(imageId);
      if (!uploadedImage.isError && uploadedImage.data.Key) {
        const { data } = await axiosInstance.post(`collection/create`, {
          name,
          desc: description,
          imageId,
          proficiencyLevel: languageProficiency,
          imageKey: uploadedImage.data.Key,
          videoIds: existingCollection.map((m) => m._id),
          // level,
          categories: getCategories(),
          videoDuration: existingCollection.reduce(
            (acc, curr) => acc + curr.duration,
            0
          ),
        });
        if (data.success) {
          setExistingCollection([]);
          setName("");
          setDescription("");
          setFile(null);
          showSnackbar("Created collection successfully", {
            type: "success",
          });
          router.push(BASE_URL);
        } else {
          handleError();
        }
      }
      setIsCreating(false);
    } catch (error) {
      handleError();
    }
  };

  const handleUpdateCollection = async () => {
    const handleError = () => {
      showSnackbar("Failed to Update collection", {
        type: "error",
      });
      setIsUpdating(false);
    };

    if (!name) {
      showSnackbar("Please enter name", {
        type: "warning",
      });
      return;
    }
    if (!description) {
      showSnackbar("Please enter description", {
        type: "warning",
      });
      return;
    }
    if (!file) {
      showSnackbar("Please select collection image", {
        type: "warning",
      });
      return;
    }
    if (existingCollection.length === 0) {
      showSnackbar(
        "Please select atleast one existing video or create a new one",
        {
          type: "warning",
        }
      );
      return;
    }
    try {
      setIsUpdating(true);
      const imagesValues: any = {};
      if (typeof file !== "string" && file && file?.name) {
        const imageId = uuidv4();
        const uploadedImage = await uploadImageTos3(imageId);
        if (!uploadedImage.isError && uploadedImage.data.Key) {
          imagesValues.imageId = imageId;
          imagesValues.imageKey = uploadedImage.data.Key;
        } else {
          showSnackbar("Failed to upload image.Please try again", {
            type: "error",
          });
          return;
        }
      }
      if (
        typeof file === "string" ||
        (typeof file !== "string" &&
          file?.name &&
          imagesValues &&
          imagesValues.imageId &&
          imagesValues.imageKey)
      ) {
        const { data: respData } = await axiosInstance.put(
          `collection/update`,
          {
            id: data._id,
            name,
            proficiencyLevel: languageProficiency,
            desc: description,
            ...imagesValues,
            videoIds: existingCollection.map((m) => m._id),
            // level,
            categories: getCategories(),
            videoDuration: existingCollection.reduce(
              (acc, curr) => acc + curr.duration,
              0
            ),
          }
        );
        if (respData.success) {
          router.push(BASE_URL);
          setExistingCollection([]);
          setName("");
          setDescription("");
          setFile(null);
          showSnackbar("Created collection successfully", {
            type: "success",
          });
        } else {
          handleError();
        }
      }
      setIsUpdating(false);
    } catch (error) {
      handleError();
    }
  };

  useEffect(() => {
    if (data) {
      setName(data.name);
      setDescription(data.desc);
      // setLevel(data.level);
      setFile(data.coverImageUrl);
      setExistingCollection(data.videos);
      setLanguageProficiency(data?.proficiencyLevel?._id);
    }
  }, [data]);

  return (
    <>
      <Head>
        <title>{isUpdate ? "Edit" : "Create"} Collection</title>
      </Head>
      <Box
        display="flex"
        flexDirection="column"
        gap={5}
        mt={20}
        maxWidth="800px"
        alignItems="center"
        width="100%"
        mx="auto"
        p={10}
      >
        <CreateHeader
          maxWidth="800px"
          text={isUpdate ? "Edit Collection" : "Video Collection Creation"}
          bottomBorderNeeded
          backRoute={BASE_URL}
        />
        <TextField
          sx={{ marginTop: 10 }}
          placeholder="Enter collection name"
          variant="outlined"
          fullWidth
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
        <TextField
          placeholder="Enter collection description"
          variant="outlined"
          fullWidth
          type="text"
          multiline
          maxRows={4}
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
        {/* <FormControl fullWidth>
          <InputLabel>
            <Typography color="#c2c2c2">Select Level</Typography>
          </InputLabel>
          <Select
            value={level}
            onChange={(e) => {
              const val = e.target.value;
              if (
                val === LEVEL_TYPES.BEGINNER ||
                val === LEVEL_TYPES.ADVANCED ||
                val === LEVEL_TYPES.INTERMEDIATE
              ) {
                setLevel(val);
              }
            }}
            label="Select Level"
          >
            <MenuItem value={LEVEL_TYPES.BEGINNER}>Beginner</MenuItem>
            <MenuItem value={LEVEL_TYPES.ADVANCED}>Advanced</MenuItem>
            <MenuItem value={LEVEL_TYPES.INTERMEDIATE}>Intermediate</MenuItem>
          </Select>
        </FormControl> */}
        <FormControl fullWidth sx={{ mb: "20px" }}>
          <InputLabel>Language Proficiency</InputLabel>
          <Select
            label="languageProficiency"
            value={languageProficiency}
            onChange={(event) => {
              setLanguageProficiency(event.target.value);
            }}
          >
            {proficiencies?.map((languageLevel) => (
              <MenuItem key={languageLevel._id} value={languageLevel._id}>
                {languageLevel.pfLevel.en}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <p style={{ textAlign: "center", fontWeight: "bold" }}>
          Collection Image
        </p>
        <SelectFileCard
          file={file}
          isImage
          onSelect={handleSelectFile}
          setFile={setFile}
          text="Upload Image"
        />
        <p style={{ textAlign: "center", fontWeight: "bold" }}>Add Videos</p>
        <Box
          alignItems="center"
          justifyContent="center"
          display="flex"
          gap={2}
          mb={10}
          sx={{
            width: "100%",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
          }}
        >
          <CustomButton
            colortype="secondary"
            text="Add Existing Videos"
            sx={{
              width: {
                xs: "100%",
                sm: "50%",
                md: "33%",
              },
            }}
            onClick={() => {
              setIsExistingOpen(true);
            }}
          />
          <CustomButton
            colortype="secondary"
            text="Upload Video"
            bgtype="outline"
            sx={{
              width: {
                xs: "100%",
                sm: "50%",
                md: "33%",
              },
            }}
            onClick={() => {
              setUVideoOpen(true);
            }}
          />
        </Box>

        {existingCollection.map((m, i) => (
          <VideoCard
            key={i}
            isModal={false}
            data={m}
            onRemoveClicked={() => {
              setExistingCollection((prev) => {
                const removedList = prev.filter((f) => f._id !== m._id);
                return removedList;
              });
            }}
          />
        ))}
        <Box
          mt={15}
          gap={2}
          display="flex"
          flexDirection="row"
          alignItems="center"
        >
          {isUpdate ? (
            <Button
              type="submit"
              disabled={isUpdating}
              onClick={handleUpdateCollection}
            >
              {isUpdating ? "updating..." : "Update Collection"}
            </Button>
          ) : (
            <Button
              type="submit"
              sx={{ width: "fit-content", fontSize: "1.55rem", px: 6 }}
              disabled={isCreating}
              onClick={handleCreateCollection}
            >
              {isCreating ? "creating..." : "Create Collection"}
            </Button>
          )}
        </Box>
      </Box>
      {isExistingOpen && (
        <ExistingVideosModal
          existingCollection={existingCollection}
          setExistingCollection={setExistingCollection}
          open={isExistingOpen}
          setOpen={setIsExistingOpen}
        />
      )}
      <UploadVideosModal
        existingCollection={existingCollection}
        setExistingCollection={setExistingCollection}
        open={isUVideoOpen}
        setOpen={setUVideoOpen}
      />
    </>
  );
};

export default CreateEditCollection;
