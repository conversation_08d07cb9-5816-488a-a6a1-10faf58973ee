import axios from "axios";
import React, { useEffect, useState } from "react";
import {
  Box,
  TextField,
  Button,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Chip,
  Stack,
  Card,
  Divider,
  Typography,
  Skeleton,
} from "@mui/material";
import getBlobDuration from "get-blob-duration";
import { useSnackbar } from "@/hooks/useSnackbar";
import SectionTitle from "./SectionTitle";
import SelectFileCard from "../SelectFileCard";
import InfoMessage from "@/components/Infomessage";
import { langugaeStateTypes } from "@/types";
import { LanguageProficiencyType, LanguageType } from "@/api/mongoTypes";
import Checkbox from "@mui/material/Checkbox";
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormLabel from "@mui/material/FormLabel";
import { LEVEL_TYPES } from "@/constant/Enums";
import { getProficiencyEn } from "@/utils/common";

const VideoDetails = ({
  title,
  setTitle,
  description,
  setDescription,
  languageProficiency,
  setLanguageProficiency,
  language,
  setLanguage,
  selectedCategories,
  setSelectedCategories,
  duration,
  setDuration,
  file,
  setFile,
  isModal = false,
  categories,
  setCategories,
  isFeatured,
  setIsFeatured,
  level,
  setLevel,
}) => {
  const { showSnackbar } = useSnackbar();
  const [isFetching, setIsFetching] = useState(false);
  const [languages, setLanguages] = useState<LanguageType[]>([]);
  const [proficiencies, setProficiencies] = useState<LanguageProficiencyType[]>(
    []
  );
  // const [categories, setCategories] = useState([]);

  const handleSelectFile = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      if (event.target.files && event.target.files.length > 0) {
        const selectedFile = event.target.files[0];
        setFile(selectedFile);
        const fileDuration = await getBlobDuration(selectedFile);
        setDuration(fileDuration);
      }
    } catch (error) {
      console.error(`omething went wrong in handleSelectFile due to `, error);
      showSnackbar("Something went wrong in selecting file", {
        type: "error",
      });
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsFetching(true);
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
        );
        const { data: categories } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/utils/get-categories`
        );
        setLanguages(data?.languages ?? []);
        setProficiencies(data?.languageProficiencies ?? []);
        setCategories(categories?.data ?? []);
        setIsFetching(false);
      } catch (error) {
        console.error(`Somethign went wrong in fetchData due to `, error);
        setIsFetching(false);
      }
    };
    fetchData();
  }, []);

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      gap={2}
      maxWidth="600px"
      width="100%"
      mx="auto"
    >
      <SectionTitle text="Video Details" />
      <Divider />

      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        width="100%"
        gap={4}
        mt={isModal ? 0 : 5}
      >
        {isFetching ? (
          <Loading />
        ) : (
          <>
            <TextField
              placeholder="Enter Video Title "
              variant="outlined"
              fullWidth
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
            <TextField
              variant="outlined"
              placeholder="Enter Description"
              fullWidth
              value={description}
              multiline
              maxRows={4}
              onChange={(e) => setDescription(e.target.value)}
            />

            <FormGroup aria-label="position" row>
              <FormControlLabel
                value="end"
                control={
                  <Checkbox
                    checked={isFeatured}
                    onChange={(e) => {
                      setIsFeatured(e.target.checked);
                    }}
                  />
                }
                label="Featured"
                labelPlacement="end"
              />
            </FormGroup>

            <Box
              display="flex"
              sx={{
                flexDirection: {
                  xs: "column",
                  sm: "row",
                },
              }}
              gap={2}
              width="100%"
              mx="auto"
            >
              <FormControl fullWidth>
                <InputLabel>
                  <Typography color="#c2c2c2"> Select Language</Typography>
                </InputLabel>
                <Select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  label="Select Language"
                >
                  {languages.map((m) => (
                    <MenuItem key={m._id} value={m._id}>
                      {m.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>
                  <Typography color="#c2c2c2">Select Proficiency</Typography>
                </InputLabel>
                <Select
                  value={languageProficiency}
                  onChange={(e) => setLanguageProficiency(e.target.value)}
                  label="Select Proficiency"
                >
                  {proficiencies.map((m) => (
                    <MenuItem key={m._id} value={m._id}>
                      {getProficiencyEn({
                        data: m,
                      })}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            {/* <FormControl fullWidth>
              <InputLabel>
                <Typography color="#c2c2c2">Select Level</Typography>
              </InputLabel>
              <Select
                value={level}
                onChange={(e) => setLevel(e.target.value)}
                label="Select Level"
              >
                <MenuItem value={LEVEL_TYPES.BEGINNER}>Beginner</MenuItem>
                <MenuItem value={LEVEL_TYPES.ADVANCED}>Advanced</MenuItem>
                <MenuItem value={LEVEL_TYPES.INTERMEDIATE}>
                  Intermediate
                </MenuItem>
              </Select>
            </FormControl> */}

            <Box width="100%">
              {selectedCategories.length > 0 && (
                <Stack direction="row" spacing={1} mb={10}>
                  {selectedCategories.map((m, i) => (
                    <Chip
                      key={i}
                      label={categories?.find((f) => f._id === m)?.name}
                      onDelete={() => {
                        setSelectedCategories((prev) =>
                          prev.filter((f) => f !== m)
                        );
                      }}
                    />
                  ))}
                </Stack>
              )}
              <FormControl fullWidth>
                <InputLabel>
                  <Typography color="#c2c2c2">Select Categories</Typography>
                </InputLabel>
                <Select
                  value={language}
                  onChange={(e) => {
                    const value = e.target.value;
                    setSelectedCategories((prev) => [...prev, value]);
                  }}
                  placeholder="Select Categories"
                >
                  {categories.map((m) => {
                    if (!selectedCategories.includes(m._id)) {
                      return (
                        <MenuItem key={m._id} value={m._id}>
                          {m.name}
                        </MenuItem>
                      );
                    }
                  })}
                </Select>
              </FormControl>
            </Box>

            <SelectFileCard
              file={file}
              isImage={false}
              setFile={setFile}
              text="Upload Video"
              onSelect={handleSelectFile}
            />
            <InfoMessage message="File must be MP4" />
          </>
        )}
      </Box>
    </Box>
  );
};

const Loading = () => {
  return (
    <>
      <Skeleton variant="rectangular" width="100%" height={60} />
      <Skeleton variant="rectangular" width="100%" height={60} />
      <Skeleton variant="rectangular" width="100%" height={60} />
      <Skeleton variant="rectangular" width="50%" height={90} />
    </>
  );
};

export default VideoDetails;
