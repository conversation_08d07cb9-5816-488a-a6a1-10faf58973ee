import { Box } from "@mui/material";
import React from "react";
import SelectFileCard from "../SelectFileCard";
import { useSnackbar } from "@/hooks/useSnackbar";
import SectionTitle from "./SectionTitle";
import { Maybe } from "@/types";
import InfoMessage from "@/components/Infomessage";

type UploadThumbnailsProps = React.FC<{
  selectedThumb: Maybe<File | string>;
  setSelectedThumb: React.Dispatch<React.SetStateAction<File>>;
}>;

const UploadThumbnails: UploadThumbnailsProps = ({
  selectedThumb,
  setSelectedThumb,
}) => {
  const { showSnackbar } = useSnackbar();

  const handleSelectFile = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      if (event.target.files && event.target.files.length > 0) {
        const selectedFile = event.target.files[0];
        setSelectedThumb(selectedFile);
      }
    } catch (error) {
      console.error(`omething went wrong in handleSelectFile due to `, error);
      showSnackbar("Something went wrong in selecting file", {
        type: "error",
      });
    }
  };

  return (
    <>
      <Box
        alignItems="center"
        justifyContent="center"
        display="flex"
        flexDirection="column"
        width="100%"
        mx="auto"
      >
        <SectionTitle text="Thumbnail" style={{ marginBottom: 10 }} />
        <SelectFileCard
          file={selectedThumb}
          setFile={setSelectedThumb}
          isImage
          onSelect={handleSelectFile}
          text="Upload Image"
        />
        <InfoMessage
          message="File must be PNG or JPG"
          sx={{ marginTop: "0.75rem" }}
        />
      </Box>
    </>
  );
};

export default UploadThumbnails;
