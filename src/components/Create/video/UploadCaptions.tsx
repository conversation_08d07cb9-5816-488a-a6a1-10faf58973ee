import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { Maybe } from "@/types";

type UploadCaptionsProps = {
  language: "English" | "Spanish";
  selectedSubtitle: Maybe<File | string>;
  setSelectedSubtitle: React.Dispatch<React.SetStateAction<Maybe<File>>>;
};

const UploadCaptions: React.FC<UploadCaptionsProps> = ({
  selectedSubtitle,
  setSelectedSubtitle,
  language = "English",
}) => {
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    console.log("file", file);
    if (file) {
      setSelectedSubtitle(file);
    }
  };

  const id = `upload-subtitle-${language}`;

  const handleView = () => {
    if (typeof selectedSubtitle === "string") {
      window.open(selectedSubtitle, "_blank");
    }
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      sx={{
        border: "1px dotted silver",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        p: 2,
        mb: 5,
        mt: 1,
        width: "100%",
        borderRadius: 2,
      }}
    >
      <input
        accept=".srt,.vtt,.txt"
        style={{ display: "none" }}
        id={id}
        type="file"
        onChange={handleFileChange}
      />
      <label htmlFor={id}>
        <Button
          variant="contained"
          color="primary"
          style={{
            width: "fit-content",
            marginTop: 5,
            marginBottom: 5,
          }}
          component="span"
        >
          {selectedSubtitle ? "Change Subtitle" : `Add ${language} Subtitle`}
        </Button>
      </label>
      {selectedSubtitle && (
        <>
          <p style={{ marginTop: 10, textAlign: "center", fontSize: 12 }}>
            Selected: {getFileName(selectedSubtitle)}
          </p>
          {typeof selectedSubtitle === "string" && (
            <Typography
              color="green"
              sx={{
                border: "1px dotted green",
                width: "fit-content",
                px: 2,
                borderRadius: 5,
                cursor: "pointer",
                mb: 3,
                fontSize: 12,
              }}
              onClick={handleView}
            >
              View Uploaded File
            </Typography>
          )}
        </>
      )}
    </Box>
  );
};

const getFileName = (file: Maybe<File | string>) => {
  if (!file) {
    return null;
  }
  if (typeof file === "string") {
    const pathname = new URL(file).pathname;
    const filename = decodeURIComponent(pathname.split("/").pop() || "file");
    return filename;
  }
  return file.name;
};

export default UploadCaptions;
