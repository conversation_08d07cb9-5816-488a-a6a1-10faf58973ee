import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Button,
  Divider,
  Typography,
  MenuItem,
  Skeleton,
} from "@mui/material";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import SectionTitle from "./SectionTitle";
import axios from "axios";

const AddToCollection = ({ selectedCollection, setSelectedCollection }) => {
  const [isFetching, setIsFetching] = useState(false);
  const [collections, setCollections] = useState([]);

  useEffect(() => {
    const fetchCollections = async () => {
      try {
        setIsFetching(true);
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/collection/all`
        );
        if (data.success) {
          setCollections(data.data);
        }
        setIsFetching(false);
      } catch (error) {
        setIsFetching(false);
        console.error(
          `Something went wrong at fetchCollections due to `,
          error
        );
      }
    };
    fetchCollections();
  }, []);

  return (
    <>
      <Divider />
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        my={10}
      >
        <SectionTitle text="Add to Collection" />
        <Box width="100%" mt={10}>
          {isFetching ? (
            <>
              <Skeleton variant="rectangular" width="100%" height={80} />
            </>
          ) : (
            <Select
              sx={{
                width: "100%",
              }}
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={selectedCollection ?? "SELECT_COLLECTION"}
              label="Age"
              onChange={(e: SelectChangeEvent) => {
                setSelectedCollection(e.target.value as string);
              }}
            >
              <MenuItem value="SELECT_COLLECTION" disabled>
                <Typography color="#c2c2c2">Select Collection</Typography>
              </MenuItem>
              {collections.map((m) => (
                <MenuItem value={m._id} key={m._id}>
                  {m.name}
                </MenuItem>
              ))}
            </Select>
          )}
        </Box>
      </Box>
    </>
  );
};

export default AddToCollection;
