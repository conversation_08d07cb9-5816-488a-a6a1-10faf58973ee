import { environment } from "@/api/aws";
import React, { useEffect, useState } from "react";
import { Box, Divider } from "@mui/material";
import { useSnackbar } from "@/hooks/useSnackbar";
import CreateHeader from "@/components/Create/CreateHeader";
import VideoDetails from "@/components/Create/video/VideoDetails";
import AddToCollection from "@/components/Create/video/AddToCollection";
import SelectCreator from "@/components/Create/video/SelectCreator";
import CustomButton from "@/components/CustomButton";
import {
  createCreatorService,
  creatorUserValidator,
} from "@/utils/creatorUtils";
import {
  getCategoryNamesBasedOnId,
  handleCreateVideo,
  validateUploadVideoFields,
} from "@/utils/videoUtil";
import UploadThumbnails from "@/components/Create/video/UploadThumbnails";
import UploadCaptions from "@/components/Create/video/UploadCaptions";
import { Maybe } from "@/types";
import { LEVEL_TYPES } from "@/constant/Enums";
import Head from "next/head";
import SectionTitle from "@/components/Create/video/SectionTitle";
import { VideoType } from "@/api/mongoTypes";
import { useRouter } from "next/router";

type VideoCreateEditProps = React.FC<{
  data: Maybe<VideoType>;
}>;

const VideoCreateEdit: VideoCreateEditProps = ({ data }) => {
  const isUpdate = !!data?._id;
  const { showSnackbar } = useSnackbar();
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);
  const [isFeatured, setIsFeatured] = useState(false);
  const [file, setFile] = useState<File | undefined | string>();
  const [duration, setDuration] = useState(0);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [language, setLanguage] = useState("");
  const [languageProficiency, setLanguageProficiency] = useState("");
  const [selectedThumbnail, setSelectedThumbnail] =
    useState<Maybe<File | string>>(null);
  const [selectedEnglishSubtitle, setSelectedEnglishSubtitle] =
    useState<Maybe<File | string>>(null);
  const [selectedSpanishSubtitle, setSelectedSpanishSubtitle] =
    useState<Maybe<File | string>>(null);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [selectedCreator, setSelectedCreator] = useState(null);
  const [categories, setCategories] = useState([]);
  const [level, setLevel] = useState(LEVEL_TYPES.BEGINNER);

  const [avatarFile, setAvatarFile] = useState(null);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");

  const [isUpdating, setIsUpdating] = useState(false);

  const handleCreate = async () => {
    const validationError = validateUploadVideoFields({
      title,
      description,
      language,
      languageProficiency,
      selectedCreator,
      selectedCategories,
      selectedThumbnail,
      selectedCollection,
      selectedEnglishSubtitle,
      selectedSpanishSubtitle,
    });
    if (validationError) {
      showSnackbar(validationError, {
        type: "error",
      });
      return;
    }

    let newlyCreatorId = null;
    if (typeof file !== "string" && file?.name) {
      setIsUploading(true);
      if (selectedCreator === "CREATE_NEW") {
        const inputError = creatorUserValidator({
          firstName,
          lastName,
          email,
          avatarFile,
        });
        if (inputError) {
          showSnackbar(inputError, {
            type: "error",
          });
          return;
        }

        await createCreatorService({
          environment,
          avatarFile,
          firstName,
          lastName,
          email,
          onImageUploadFail: () => {
            showSnackbar(
              "Failed to upload image.Please try to create user again",
              {
                type: "error",
              }
            );
          },
          onCreateCreatorFail: () => {
            showSnackbar("Failed to create user", {
              type: "error",
            });
          },
          onCreateCreatorSuccess: ({ data }) => {
            newlyCreatorId = data?.data?._id;
            setAvatarFile(null);
            setFirstName("");
            setLastName("");
            setEmail("");
            showSnackbar("Created user successfully", {
              type: "success",
            });
          },
        });
      }

      await handleCreateVideo({
        title,
        description,
        language,
        proficiencyLevel: languageProficiency,
        categoriesId: selectedCategories,
        categoriesName: getCategoryNamesBasedOnId(
          categories,
          selectedCategories
        ),
        userId:
          selectedCreator === "CREATE_NEW" ? newlyCreatorId : selectedCreator,
        duration,
        collectionId: selectedCollection,
        file,
        level,
        selectedThumbnail,
        isFeatured,
        selectedEnglishSubtitle,
        selectedSpanishSubtitle,
        onVideoCrateSuccess: () => {},
        onVideoUploadSuccess: () => {
          setFile(null);
          setSelectedCategories([]);
          setTitle("");
          setDescription("");
          setLanguage("");
          setLanguageProficiency("");
          setSelectedCollection(null);
          setSelectedCreator(null);
          setIsFeatured(false);
          setLevel(LEVEL_TYPES.BEGINNER);
          setSelectedSpanishSubtitle(null);
          setSelectedEnglishSubtitle(null);
          setSelectedThumbnail(null);
          showSnackbar("Created video successfully", {
            type: "success",
          });
        },
        onVideoCreateError: () => {
          showSnackbar(
            "Something went wrong while uploading .Please try again",
            {
              type: "error",
            }
          );
        },
      });
      setIsUploading(false);
    } else {
      showSnackbar("Please select file", {
        type: "error",
      });
    }
  };

  const handleUpdate = async () => {
    const handleError = () => {
      setIsUpdating(false);
      showSnackbar("Failed to update video", {
        type: "error",
      });
    };

    const validationError = validateUploadVideoFields({
      title,
      description,
      language,
      languageProficiency,
      selectedCreator,
      selectedCategories,
      selectedThumbnail,
      selectedCollection,
      selectedEnglishSubtitle,
      selectedSpanishSubtitle,
    });
    if (validationError) {
      showSnackbar(validationError, {
        type: "error",
      });
      return;
    }

    try {
      setIsUpdating(true);
      let newlyCreatorId = null;
      if (selectedCreator === "CREATE_NEW") {
        const inputError = creatorUserValidator({
          firstName,
          lastName,
          email,
          avatarFile,
        });
        if (inputError) {
          showSnackbar(inputError, {
            type: "error",
          });
          return;
        }

        await createCreatorService({
          environment,
          avatarFile,
          firstName,
          lastName,
          email,
          onImageUploadFail: () => {
            showSnackbar(
              "Failed to upload image.Please try to create user again",
              {
                type: "error",
              }
            );
          },
          onCreateCreatorFail: () => {
            showSnackbar("Failed to create user", {
              type: "error",
            });
          },
          onCreateCreatorSuccess: ({ data }) => {
            newlyCreatorId = data?.data?._id;
            setAvatarFile(null);
            setFirstName("");
            setLastName("");
            setEmail("");
            showSnackbar("Created user successfully", {
              type: "success",
            });
          },
        });
      }

      await handleCreateVideo({
        isUpdate: true,
        title,
        id: data?._id,
        description,
        language,
        proficiencyLevel: languageProficiency,
        categoriesId: selectedCategories,
        categoriesName: getCategoryNamesBasedOnId(
          categories,
          selectedCategories
        ),
        userId:
          selectedCreator === "CREATE_NEW" ? newlyCreatorId : selectedCreator,
        duration,
        collectionId: selectedCollection,
        file,
        level,
        selectedThumbnail,
        isFeatured,
        selectedEnglishSubtitle,
        selectedSpanishSubtitle,
        onVideoCrateSuccess: () => {},
        onVideoUploadSuccess: () => {
          setFile(null);
          setSelectedCategories([]);
          setTitle("");
          setDescription("");
          setLanguage("");
          setLanguageProficiency("");
          setSelectedCollection(null);
          setSelectedCreator(null);
          setIsFeatured(false);
          setLevel(LEVEL_TYPES.BEGINNER);
          setSelectedSpanishSubtitle(null);
          setSelectedEnglishSubtitle(null);
          setSelectedThumbnail(null);
          showSnackbar("Updated video successfully", {
            type: "success",
          });
          router.push("/create/video");
        },
        onVideoCreateError: () => {
          showSnackbar(
            "Something went wrong while updating .Please try again",
            {
              type: "error",
            }
          );
        },
      });

      setIsUpdating(false);
    } catch (error) {
      handleError();
    }
  };

  useEffect(() => {
    if (data) {
      setIsFeatured(Boolean(data.isFeatured));
      setTitle(data.title);
      setFile(data.videoUrl);
      setDescription(data.description);
      setSelectedCategories(data.categoryIds);
      setLanguage(String(data.language));
      setLanguageProficiency(String(data.proficiencyLevel));
      setSelectedCreator(data.creator?._id);
      setLevel(data.level);
      setSelectedCollection(
        data.collectionIds.length > 0 ? data.collectionIds[0] : null
      );
      setSelectedEnglishSubtitle(data.enSubtitleUrl);
      setSelectedSpanishSubtitle(data.esSubtitleUrl);
      setSelectedThumbnail(data.thumbnailUrl);
    }
  }, [data]);

  return (
    <>
      <Head>
        <title>Upload Video</title>
      </Head>
      <Box
        display="flex"
        flexDirection="column"
        gap={2}
        mt={20}
        maxWidth="600px"
        width="100%"
        mx="auto"
        p={10}
      >
        <CreateHeader
          text="Video Upload"
          bottomBorderNeeded
          maxWidth="600px"
          sx={{ marginBottom: "2rem" }}
          backRoute="/create/video"
        />

        <VideoDetails
          title={title}
          setTitle={setTitle}
          description={description}
          setDescription={setDescription}
          languageProficiency={languageProficiency}
          setLanguageProficiency={setLanguageProficiency}
          language={language}
          setLanguage={setLanguage}
          selectedCategories={selectedCategories}
          setSelectedCategories={setSelectedCategories}
          duration={duration}
          setDuration={setDuration}
          file={file}
          setFile={setFile}
          isFeatured={isFeatured}
          setIsFeatured={setIsFeatured}
          categories={categories}
          setCategories={setCategories}
          level={level}
          setLevel={setLevel}
        />
        <Divider
          sx={{
            marginTop: 10,
            marginBottom: 10,
          }}
        />
        <UploadThumbnails
          selectedThumb={selectedThumbnail}
          setSelectedThumb={setSelectedThumbnail}
        />
        <Divider
          sx={{
            marginTop: 10,
            marginBottom: 10,
          }}
        />
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
        >
          <SectionTitle text="Captions / Subtitles" />
          <UploadCaptions
            language="English"
            key="English-UploadCaptions"
            selectedSubtitle={selectedEnglishSubtitle}
            setSelectedSubtitle={setSelectedEnglishSubtitle}
          />
          <UploadCaptions
            key="Spanish-UploadCaptions"
            language="Spanish"
            selectedSubtitle={selectedSpanishSubtitle}
            setSelectedSubtitle={setSelectedSpanishSubtitle}
          />
        </Box>
        <Divider
          sx={{
            marginTop: 10,
          }}
        />
        <SelectCreator
          selectedCreator={selectedCreator}
          setSelectedCreator={setSelectedCreator}
          firstName={firstName}
          setFirstName={setFirstName}
          lastName={lastName}
          setLastName={setLastName}
          email={email}
          setEmail={setEmail}
          setAvatarFile={setAvatarFile}
          avatarFile={avatarFile}
        />
        <AddToCollection
          selectedCollection={selectedCollection}
          setSelectedCollection={setSelectedCollection}
        />

        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
        >
          {isUpdate ? (
            <CustomButton
              disabled={isUpdating}
              onClick={handleUpdate}
              sx={{
                width: {
                  xs: "100%",
                  sm: "50%",
                  lg: "33%",
                },
                marginTop: 20,
              }}
              text={isUpdating ? "updating..." : "Update Video"}
            />
          ) : (
            <CustomButton
              disabled={isUploading}
              onClick={handleCreate}
              sx={{
                width: {
                  xs: "100%",
                  sm: "50%",
                  lg: "33%",
                },
                marginTop: 20,
              }}
              text={isUploading ? "uploading..." : "Upload Video"}
            />
          )}
        </Box>
      </Box>
    </>
  );
};

export default VideoCreateEdit;
