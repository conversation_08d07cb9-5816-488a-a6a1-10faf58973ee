import { Box, Card, Skeleton } from "@mui/material";
import React from "react";

const Loading = () => {
  return (
    <Box width="100%">
      {new Array(2).fill("").map((m, i) => (
        <Card
          key={i}
          sx={{
            display: "flex",
            flexDirection: "column",
            p: 3,
            mb: 4,
          }}
        >
          <Skeleton sx={{ width: 150, height: "20%" }} />
          <Skeleton sx={{ width: 50, height: "20%" }} />
          <Skeleton sx={{ width: "40%", height: 20 }} />
          <Skeleton sx={{ width: "100%", height: 55 }} />
          <Skeleton sx={{ width: "40%", height: 20 }} />
          <Skeleton sx={{ width: "100%", height: 65 }} />
        </Card>
      ))}
    </Box>
  );
};

export default Loading;
