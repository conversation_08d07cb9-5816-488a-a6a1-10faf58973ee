import PricingCardTopInfo from "@/components/PricingCard/PricingCardTopInfo";
import { Box, Button, Typography } from "@mui/material";
import React, { useState } from "react";
import EditPlanModal from "./EditPlanModal";
import PlanRow from "./PlanRow";
import { ClassesPricingType } from "@/api/mongoTypes";
import { getPricingTitle } from "@/utils/format";
import { getOriginalPrice } from "@/utils/classes";

type EditPlanCardProps = React.FC<{
  data: ClassesPricingType;
  setClasses: React.Dispatch<React.SetStateAction<ClassesPricingType[]>>;
}>;
const EditPlanCard: EditPlanCardProps = ({ data, setClasses }) => {
  const [isOpen, setIsOpen] = useState(false);

  const title = getPricingTitle({
    data: data,
    needSubtitle: false,
  });

  return (
    <>
      <Box
        width="100%"
        p={3}
        sx={{
          border: "1px solid #D4D4D4",
          borderRadius: 2,
        }}
      >
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          width="100%"
          justifyContent="space-between"
          my={2}
        >
          <Typography fontWeight={700} fontSize={20}>
            {title}
          </Typography>
          <Button
            onClick={() => {
              setIsOpen(true);
            }}
          >
            Edit
          </Button>
        </Box>
        <PricingCardTopInfo
          isLimited={data.isLimitedDeal}
          isPopular={data.isPopular}
        />
        {data.plans.map((m, i) => (
          <>
            <PlanRow
              index={i}
              key={i}
              plan={m}
              // plan={{
              //   ...m,
              //   price: getOriginalPrice({
              //     discount: m.discount,
              //     discountedPrice: m.price,
              //   }),
              // }}
              data={data}
            />
            {i === 0 && (
              <Typography p={2} fontSize={14} color="#696969">
                Customization options
              </Typography>
            )}
          </>
        ))}
      </Box>
      {isOpen && (
        <EditPlanModal
          setData={setClasses}
          data={data}
          open={isOpen}
          setOpen={setIsOpen}
        />
      )}
    </>
  );
};

export default EditPlanCard;
