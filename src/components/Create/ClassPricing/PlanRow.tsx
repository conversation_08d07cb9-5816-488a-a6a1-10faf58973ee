import { Box, SxProps, TextField, Theme, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import DeleteIcon from "@mui/icons-material/Delete";
import { ClassesPricingPlan, ClassesPricingType } from "@/api/mongoTypes";
import {
  getDuration,
  getNumberOfDuration,
  getPlansTitle,
} from "@/utils/format";
import { getDiscountedPrice, getOriginalPrice } from "@/utils/classes";

const textFieldStyle: SxProps<Theme> = {
  padding: 0,
  "& input": {
    fontSize: { xs: "12px", md: "14px" },
    padding: "2px 5px",
  },
};
type PlanRowProps = React.FC<{
  isEdit?: boolean;
  handleDelete?: () => void;
  plan: ClassesPricingPlan;
  data: ClassesPricingType;
  index: number;
  setData?: React.Dispatch<React.SetStateAction<ClassesPricingPlan[]>>;
}>;
const PlanRow: PlanRowProps = ({
  isEdit = false,
  handleDelete = () => {},
  plan,
  data,
  setData,
  index,
}) => {
  const [noOfDuration, setNoOfDuration] = useState(plan.duration);
  const [discount, setDiscount] = useState(plan.discount);
  const [orgPrice, setOrgPrice] = useState(
    getOriginalPrice({
      discount: plan.discount,
      discountedPrice: plan.price,
    })
  );

  const text = getPlansTitle({
    duration: plan.duration,
    subType: data.subType,
    type: data.type,
  });

  useEffect(() => {
    setNoOfDuration(plan.duration);
    setDiscount(plan.discount);
    if (!isEdit) {
      setOrgPrice(
        getOriginalPrice({
          discount: plan.discount,
          discountedPrice: plan.price,
        })
      );
    }
  }, [plan, isEdit]);

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      width="100%"
      justifyContent="center"
      p={2}
      mt={2}
      gap={3}
      sx={{
        background: "#FAFAFA",
      }}
    >
      <Box width="25%" display="flex" flexDirection="row" alignItems="center">
        {isEdit && index !== 0 ? (
          <>
            <Typography
              fontSize={14}
              sx={{
                textWrap: "nowrap",
              }}
              color="#737373"
            >
              {getNumberOfDuration({
                type: data.type,
                subType: data.subType,
              })}
              &nbsp;
            </Typography>
            <TextField
              type="number"
              inputProps={{ min: 1 }}
              sx={textFieldStyle}
              value={noOfDuration}
              onChange={(e) => {
                const currentValue = +e.target.value;
                setNoOfDuration(+currentValue);
                setData((prev) =>
                  prev.map((p, i) => {
                    if (i === index) {
                      return {
                        ...p,
                        duration: currentValue,
                      };
                    }
                    return p;
                  })
                );
              }}
            />
          </>
        ) : (
          <Typography fontWeight={700} color="#000" fontSize="0.85rem">
            {/* {noOfDuration} {getDuration(data.durationType)} */}
            {text.replace(":", "")}
          </Typography>
        )}
      </Box>
      <Box width="25%" display="flex" flexDirection="row" alignItems="center">
        <Typography
          fontSize={14}
          color="#737373"
          fontWeight={500}
          sx={{
            textWrap: "nowrap",
          }}
        >
          Original Price:
        </Typography>
        &nbsp;
        {isEdit ? (
          <>
            <Typography color="#000" fontWeight={800}>
              $
            </Typography>
            &nbsp;
            <TextField
              type="number"
              sx={textFieldStyle}
              value={orgPrice}
              onChange={(e) => {
                const currentValue = +e.target.value;
                setOrgPrice(currentValue);
                setData((prev) =>
                  prev.map((p, i) => {
                    if (i === index) {
                      return {
                        ...p,
                        price: Number(
                          getDiscountedPrice({
                            discount,
                            price: currentValue,
                          })
                        ),
                      };
                    }
                    return p;
                  })
                );
              }}
            />
          </>
        ) : (
          <Typography color="#000" fontWeight={800}>
            &nbsp;$
            {orgPrice.toFixed(2)}
          </Typography>
        )}
      </Box>
      <Box width="25%" display="flex" flexDirection="row" alignItems="center">
        <Typography fontSize={14} color="#737373" fontWeight={500}>
          Discount:
        </Typography>
        &nbsp;
        {isEdit ? (
          <>
            <TextField
              type="number"
              sx={textFieldStyle}
              value={discount}
              onChange={(e) => {
                const currentValue = +e.target.value;
                if (currentValue < 100) {
                  setDiscount(currentValue);
                  setData((prev) =>
                    prev.map((p, i) => {
                      if (i === index) {
                        return {
                          ...p,
                          discount: currentValue,
                          price: getDiscountedPrice({
                            discount: currentValue,
                            price: orgPrice,
                          }),
                        } as any;
                      }
                      return p;
                    })
                  );
                }
              }}
            />
            &nbsp;
            <Typography color="#14AE5C" fontWeight={800}>
              %
            </Typography>
          </>
        ) : (
          <Typography color="#14AE5C" fontWeight={800}>
            &nbsp;{discount > 0 ? `${discount.toFixed(2)}%` : "-"}
          </Typography>
        )}
      </Box>
      <Box width="25%" display="flex" flexDirection="row" alignItems="center">
        <Typography fontSize={14} color="#282828" fontWeight={500}>
          Final Price
        </Typography>
        &nbsp;
        <Typography fontWeight={800}>
          &nbsp;$&nbsp;
          {getDiscountedPrice({
            discount,
            price: orgPrice,
          })}
        </Typography>
      </Box>
      {isEdit && index !== 0 && (
        <Box
          onClick={handleDelete}
          sx={{
            cursor: "pointer",
          }}
        >
          <DeleteIcon />
        </Box>
      )}
    </Box>
  );
};

export default PlanRow;
