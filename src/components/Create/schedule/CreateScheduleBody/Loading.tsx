import { Box, Card, Skeleton } from "@mui/material";
import React from "react";

const Loading = () => {
  return (
    <Box
      sx={{ width: "100%", border: "1px solid silver", borderRadius: 2 }}
      p={2}
    >
      <Box sx={{ width: "100%" }}>
        <Skeleton
          variant="rectangular"
          width="100%"
          height={40}
          sx={{ borderRadius: 2 }}
        />
      </Box>
      <Box
        display="flex"
        flexDirection="row"
        gap={2}
        mt={2}
        sx={{ width: "100%" }}
      >
        <Box sx={{ width: "30%", height: 300 }}>
          <Skeleton
            variant="rectangular"
            width="100%"
            height="100%"
            sx={{ borderRadius: 2 }}
          />
        </Box>
        <Box sx={{ width: "70%", height: 300 }}>
          <Skeleton
            variant="rectangular"
            width="100%"
            height="100%"
            sx={{ borderRadius: 2 }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default Loading;
