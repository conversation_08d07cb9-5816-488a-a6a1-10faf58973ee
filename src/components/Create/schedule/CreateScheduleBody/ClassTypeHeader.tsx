import { Box, Typography } from "@mui/material";
import Image from "next/image";
import React from "react";

const ClassTypeHeader = () => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="start"
      gap={5}
      width="100%"
      mt={10}
    >
      <Image
        style={{ borderRadius: 5 }}
        src="/images/classes/classesInPerson.webp"
        height={30}
        width={30}
        alt="image"
      />
      <Typography fontSize={18} fontWeight={800}>
        In-Person Classess
      </Typography>
    </Box>
  );
};

export default ClassTypeHeader;
