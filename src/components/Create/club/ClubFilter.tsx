import React, { useState } from "react";
import {
  Button,
  Menu,
  MenuItem,
  Checkbox,
  Typography,
  Box,
} from "@mui/material";
import { LEARNING_LANGUAGE } from "@/constant/Enums";
import { FilterIcon } from "@/components/Profile/CommonFilters/ClassesFilter";

const filters = [
  {
    id: 1,
    name: "All",
    value: "",
  },
  {
    id: 2,
    name: "English",
    value: LEARNING_LANGUAGE.ENGLISH,
  },
  {
    id: 3,
    name: "Spanish",
    value: LEARNING_LANGUAGE.SPANISH,
  },
];

const ClassesFilter = ({ targetLanguage, setTargetLanguage }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCheckboxChange = (name) => {
    if (targetLanguage.includes(name)) {
      setTargetLanguage("");
    } else {
      setTargetLanguage(name);
    }
  };

  return (
    <div>
      <Button
        onClick={handleClick}
        sx={{
          padding: "8px 12px",
          textTransform: "none",
          border: "1px solid rgba(0, 0, 0, 0.23)",
          background: "transparent",
          width: "max-content",
          color: "#000",
          "&:hover": {
            background: "transparent",
          },
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <FilterIcon />
          <Typography sx={{ fontSize: 12 }}>
            {targetLanguage.length > 1 ? targetLanguage : "Filter"}
          </Typography>
        </Box>
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        {filters.map((className) => (
          <MenuItem
            key={className.id}
            sx={{ padding: "0 8px" }}
            onClick={() => handleCheckboxChange(className.value)}
          >
            <Checkbox
              size="small"
              checked={className.value === targetLanguage}
            />
            <Typography sx={{ fontSize: 12 }}>{className.name}</Typography>
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
};

export default ClassesFilter;
