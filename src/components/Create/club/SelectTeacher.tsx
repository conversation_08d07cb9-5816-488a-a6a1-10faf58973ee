import {
  Box,
  Card,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import React from "react";
import SelectAvatar from "../SelectAvatar";
import { getUserFullName } from "@/utils/format";
import DeleteIcon from "@mui/icons-material/Delete";
import Link from "next/link";
import { useSnackbar } from "@/hooks/useSnackbar";

const SelectTeacher = ({ selectedCreators, setSelectedCreators, teachers }) => {
  const { showSnackbar } = useSnackbar();
  const notSelectedTeachers = teachers.filter(
    (t) => !selectedCreators.includes(t._id)
  );

  const selectedTeachers = teachers.filter((t) =>
    selectedCreators.includes(t._id)
  );

  return (
    <Card
      sx={{
        width: "100%",
        p: 3,
        mb: 8,
      }}
    >
      <Typography fontWeight={800} mb={2}>
        Select Teacher
      </Typography>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        gap={2}
        flexWrap="wrap"
      >
        {selectedTeachers.map((m, i) => (
          <SelectedUser
            teacher={m}
            key={i}
            handleDelete={() => {
              setSelectedCreators((prev) => prev.filter((f) => m._id !== f));
            }}
          />
        ))}
      </Box>

      <Select
        multiple
        labelId="demo-simple-select-label"
        id="demo-simple-select"
        sx={{
          width: "100%",
          ".MuiSelect-select": {
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 2,
            // Ensure placeholder text is visible
            color: selectedCreators.length === 0 ? "#c2c2c2" : "inherit",
          },
          mt: 5,
        }}
        value={selectedCreators}
        onChange={(e: SelectChangeEvent) => {
          const selectedValue = e.target.value;
          // Ensure only valid teacher IDs are set
          setSelectedCreators(
            Array.isArray(selectedValue) ? selectedValue : [selectedValue]
          );
        }}
        // Explicitly handle rendering of the placeholder
        renderValue={(selected) => {
          if (!selected || selected.length === 0) {
            return <Typography color="#c2c2c2">Select teacher</Typography>;
          }
          return selected
            .map((id) => {
              const teacher = teachers.find((t) => t._id === id);
              return teacher ? getUserFullName(teacher) : "";
            })
            .filter(Boolean)
            .join(", ");
        }}
        displayEmpty // Ensure the Select renders even when empty
      >
        {notSelectedTeachers.map((m) => (
          <MenuItem
            key={m._id}
            value={m._id}
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: 2,
            }}
          >
            <SelectAvatar
              profileImageUrl={m.profileImageUrl}
              firstName={m.firstName}
            />
            {m.firstName} {m.lastName}
          </MenuItem>
        ))}
        {notSelectedTeachers.length === 0 && (
          <Box sx={{ textAlign: "center", p: 2 }}>
            <Link
              href="/create/roles-management/"
              onClick={() => {
                showSnackbar(
                  "Assign teacher role to the existing user in the role management",
                  {
                    type: "warning",
                  },
                  60000
                );
              }}
            >
              Create teacher
            </Link>
          </Box>
        )}
      </Select>
    </Card>
  );
};

const SelectedUser = ({ teacher, handleDelete }) => {
  return (
    <Card
      sx={{
        borderRadius: 2,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        p: 4,
        position: "relative",
        width: 150,
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="end"
        width="100%"
        mb={4}
      >
        <DeleteIcon
          onClick={handleDelete}
          sx={{ cursor: "pointer", color: "red" }}
        />
      </Box>

      <Box mb={5}>
        <SelectAvatar
          size={60}
          firstName={teacher.firstName}
          profileImageUrl={teacher.profileImageUrl}
        />
      </Box>
      <Typography>{getUserFullName(teacher)}</Typography>
    </Card>
  );
};

export default SelectTeacher;
