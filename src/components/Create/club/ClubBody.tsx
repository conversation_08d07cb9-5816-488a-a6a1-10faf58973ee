/* eslint-disable react/jsx-no-undef */
import {
  Box,
  Button,
  Chip,
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CreateHeader from "../CreateHeader";
import { categories } from "data/categories";
import { validateClub } from "@/utils/clubUtil";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { v4 as uuidv4 } from "uuid";
import { uploadFileOnAws } from "@/utils/uploadFileOnAws";
import { environment } from "@/api/aws";
import {
  ClubType,
  LanguageProficiencyType,
  LanguageType,
  UserType,
} from "@/api/mongoTypes";
import SelectCreator from "../video/SelectCreator";
import { CURRENCY_ENUM, LEVEL_TYPES } from "@/constant/Enums";
import ImageSection from "./ImageSection";
import HighlightsContainer from "./HighlightsContainer";
import SelectTeacher from "./SelectTeacher";
import PriceInput from "@/components/PriceInput";
import ImagesCarousel from "@/components/classes/OnlineClub/ImagesCarousel";
import { getEnglishAndSpanish, maybeObjectId } from "@/utils/common";
import { useRouter } from "next/router";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "800px",
  alignItems: "center",
  width: "100%",
  margin: "0 auto",
  padding: { xs: "20px", sm: "0px" },
};

const eventSubHeaderStyles = {
  textAlign: "center",
  fontSize: "20px",
  fontWeight: "500",
  marginBottom: "33px",
};

const formBodyStyles = {
  display: "flex",
  flexDirection: "column",
  gap: 2,
  width: "100%",
  margin: "0 auto",
};
const categoryChipContainerStyles = {
  display: "flex",
  flexWrap: "wrap",
  gap: 1,
};
const eventTitleStyles = { marginBottom: "20px" };
const eventDescriptionStyles = { marginBottom: "20px" };
const headerStyles = { marginBottom: "33px", maxWidth: "800px" };
const categoryStyles = { marginBottom: "50px" };
const categoryMenuItemStyles = { textTransform: "uppercase" };
const chipStyles = {
  backgroundColor: "#D7F7F5",
  color: "#14A79C",
  textTransform: "uppercase",
};
const LanguageProficiencyStyles = { marginBottom: "20px" };
const buttonContainerStyles = {
  marginTop: "75px",
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-evenly",
  gap: 10,
};

const backRoute = "/create/club";

type ClubBodyProps = React.FC<{
  data: ClubType;
  proficiencies: LanguageProficiencyType[];
  teachers: UserType[];
  languages: LanguageType[];
}>;
const ClubBody: ClubBodyProps = ({
  data = null,
  proficiencies,
  teachers,
  languages,
}) => {
  const isUpdate = !!data?._id;
  const { showSnackbar } = useSnackbar();
  const router = useRouter();

  const [title, setTitle] = useState("");
  const [theme, setTheme] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState<string | number>(10);
  const [languageProficiency, setLanguageProficiency] = useState("");
  const [highlights, setHighlights] = useState([""]);
  const [category, setCategory] = useState([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [deletedImageKeys, setDeletedImageKeys] = useState([]);
  const [images, setImages] = useState([]);
  const [imagesKeysAndIds, setImagesKeysAndIds] = useState([]);
  const [selectedCreators, setSelectedCreators] = useState([]);
  const [currency, setCurrency] = useState<CURRENCY_ENUM>(CURRENCY_ENUM.MXN);
  const [targetLanguage, setTargetLanguage] = useState(null);

  useEffect(() => {
    if (data) {
      setTitle(data.title);
      setTheme(data.theme);
      setDescription(data.about);
      setPrice(+data.price);
      setLanguageProficiency(data?.proficiencyLevel?._id);
      setHighlights(data.highlights);
      setImages(data.images);
      setCategory(data.categories);
      setCurrency(data?.currency ?? CURRENCY_ENUM.MXN);
      setImagesKeysAndIds(data.imagesKeysAndIds);
      setSelectedCreators(data.teachers.map((m) => m._id));
      setTargetLanguage(maybeObjectId(data.targetLanguage));
    }
  }, [data]);

  const handleCategoryChange = (event) => {
    const {
      target: { value },
    } = event;
    setCategory(typeof value === "string" ? value.split(",") : value);
  };

  const handleLanguageProficiencySelectChange = (event) => {
    setLanguageProficiency(event.target.value);
  };

  const handleReset = () => {
    setTitle("");
    setDescription("");
    setPrice(10);
    setLanguageProficiency("");
    setHighlights([""]);
    setCategory([]);
    setImages([]);
    setTheme("");
    setDeletedImageKeys([]);
    setImagesKeysAndIds([]);
    setSelectedCreators([]);
  };

  const uploadClubImage = async (file, id) => {
    const fileName = file?.name;
    const data = await uploadFileOnAws({
      file: file,
      key: `${environment}/club/${id}/club_${fileName}`,
    });
    return data;
  };

  const handleCreateClub = async () => {
    try {
      const error = validateClub({
        title,
        price: +price,
        categories: category,
        proficiencyLevel: languageProficiency,
        about: description,
        highlights,
        theme,
        currency,
        selectedCreators,
        targetLanguage,
      });
      if (error) {
        showSnackbar(error, {
          type: "warning",
        });
        return;
      }
      setIsCreating(true);
      let imagesKeysAndIds = [];
      if (images.length > 0) {
        await Promise.all(
          images.map(async (m) => {
            const clubImageId = uuidv4();
            const uploadedEventImage = await uploadClubImage(m, clubImageId);
            if (!uploadedEventImage.isError && uploadedEventImage.data.Key) {
              const newKeyAndId = {
                key: uploadedEventImage.data.Key,
                id: clubImageId,
              };
              imagesKeysAndIds.push(newKeyAndId);
            }
          })
        );
      }

      const payload = {
        title,
        about: description,
        price,
        theme,
        proficiencyLevel: languageProficiency,
        categories: category,
        highlights,
        imagesKeysAndIds,
        teachers: selectedCreators,
        currency,
        targetLanguage,
      };
      const { data } = await axiosInstance.post(`club/create`, payload);
      if (data.success && data?.data?._id) {
        handleReset();
        showSnackbar("Created club successfully", {
          type: "success",
        });
        router.push(backRoute);
      } else {
        showSnackbar("Something went wrong while creating club", {
          type: "error",
        });
      }
      setIsCreating(false);
    } catch (error) {
      setIsCreating(false);
    }
  };

  const handleUpdateClub = async () => {
    try {
      const error = validateClub({
        title,
        price: +price,
        categories: category,
        proficiencyLevel: languageProficiency,
        about: description,
        highlights,
        theme,
        selectedCreators,
        currency,
        targetLanguage,
      });
      if (error) {
        showSnackbar(error, {
          type: "warning",
        });
        return;
      }
      setIsUpdating(true);
      const localImages = images.filter((f) => f?.name);
      let allImagesKeysAndIds = [];
      allImagesKeysAndIds = [...imagesKeysAndIds];
      if (localImages.length > 0) {
        await Promise.all(
          localImages.map(async (m) => {
            const clubImageId = uuidv4();
            const uploadedEventImage = await uploadClubImage(m, clubImageId);
            if (!uploadedEventImage.isError && uploadedEventImage.data.Key) {
              const newIds = {
                key: uploadedEventImage.data.Key,
                id: clubImageId,
              };
              allImagesKeysAndIds.push(newIds);
            }
          })
        );
      }
      const filteredIds = allImagesKeysAndIds.filter(
        (f) => !deletedImageKeys.includes(f.id)
      );
      const payload = {
        title,
        about: description,
        price,
        theme,
        proficiencyLevel: languageProficiency,
        categories: category,
        highlights,
        imagesKeysAndIds: filteredIds,
        deletedImageKeys,
        id: data._id,
        teachers: selectedCreators,
        currency,
        targetLanguage,
      };
      const { data: respData } = await axiosInstance.put(
        `club/update`,
        payload
      );
      if (respData.success && respData?.data?._id) {
        showSnackbar("Updated club successfully", {
          type: "success",
        });
        router.push(backRoute);
      } else {
        showSnackbar("Something went wrong while updating club", {
          type: "error",
        });
      }
      setIsUpdating(false);
    } catch (error) {
      setIsUpdating(false);
    }
  };

  const englishSpanishLanguages = getEnglishAndSpanish(languages);

  return (
    <Box sx={containerStyles}>
      <CreateHeader
        text={isUpdate ? "Update Club" : "Club Creation"}
        bottomBorderNeeded
        style={headerStyles}
        backRoute={backRoute}
      />
      <Typography sx={eventSubHeaderStyles}>Club Details</Typography>
      {images && images.length > 0 && (
        <ImagesCarousel
          sx={{ mb: 5 }}
          images={images.map((m) => {
            if (m?.name) {
              return URL.createObjectURL(m);
            }
            return m.url;
          })}
          currency={currency}
          price={price}
        />
      )}
      <ImageSection
        images={images}
        showSnackbar={showSnackbar}
        setImages={setImages}
        isMultiple
        setDeletedImageKeys={setDeletedImageKeys}
      />
      <SelectTeacher
        teachers={teachers}
        selectedCreators={selectedCreators}
        setSelectedCreators={setSelectedCreators}
      />
      {/* <SelectCreator
        selectedCreators={selectedCreators}
        setSelectedCreators={setSelectedCreators}
        allowMultiple
        allowCreate={false}
      /> */}
      <Box sx={formBodyStyles}>
        <TextField
          label="Enter Club Title"
          variant="outlined"
          fullWidth
          value={title}
          sx={eventTitleStyles}
          onChange={(event) => {
            setTitle(event.target.value);
          }}
        />
        <PriceInput
          // isDisabled={!!data}
          showInfoAtBottom
          currency={currency}
          label="Enter club price"
          price={price}
          onCurrencyChanged={(cur) => {
            setCurrency(cur);
          }}
          onPriceChanged={(val) => {
            if (val === "" || isNaN(val)) {
              setPrice("");
            } else {
              const numericValue = +val;
              if (numericValue > 0) {
                setPrice(val);
              }
            }
          }}
        />
        <TextField
          label="Enter Club Theme"
          variant="outlined"
          fullWidth
          value={theme}
          sx={eventTitleStyles}
          onChange={(event) => {
            setTheme(event.target.value);
          }}
        />

        <FormControl fullWidth sx={LanguageProficiencyStyles}>
          <InputLabel>Language Proficiency</InputLabel>
          <Select
            label="languageProficiency"
            value={languageProficiency}
            onChange={handleLanguageProficiencySelectChange}
          >
            {proficiencies?.map((languageLevel) => (
              <MenuItem key={languageLevel._id} value={languageLevel._id}>
                {languageLevel.pfLevel.en}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl fullWidth sx={LanguageProficiencyStyles}>
          <InputLabel id="target-language-label" shrink>
            Target Language
          </InputLabel>
          <Select
            labelId="target-language-label"
            id="target-language"
            value={targetLanguage}
            onChange={(event) => setTargetLanguage(event.target.value)}
            label="Target Language"
          >
            {englishSpanishLanguages.map((languageLevel) => (
              <MenuItem key={languageLevel._id} value={languageLevel._id}>
                {languageLevel.nameInEnglish}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl fullWidth sx={{ mb: 5 }}>
          <InputLabel>Categories</InputLabel>
          <Select
            multiple
            value={category}
            onChange={handleCategoryChange}
            input={<OutlinedInput id="select-multiple-chip" label="Chip" />}
            renderValue={(selected) => (
              <Box sx={categoryChipContainerStyles}>
                {selected.map((value) => (
                  <Chip key={value} label={value} sx={chipStyles} />
                ))}
              </Box>
            )}
          >
            {categories.map((category) => (
              <MenuItem
                key={category.name}
                value={category.name}
                sx={categoryMenuItemStyles}
              >
                {category.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <TextField
          label="Enter Club About"
          variant="outlined"
          value={description}
          onChange={(event) => {
            setDescription(event.target.value);
          }}
          multiline
          sx={{ mb: 5 }}
          rows={3}
          fullWidth
        />
        <HighlightsContainer
          highlights={highlights}
          setHighlights={setHighlights}
        />
      </Box>

      <Box sx={buttonContainerStyles}>
        <Button onClick={handleReset} disabled={isCreating}>
          Reset
        </Button>
        {!isUpdate ? (
          <Button disabled={isCreating} onClick={handleCreateClub}>
            Save
          </Button>
        ) : (
          <Button disabled={isUpdating} onClick={handleUpdateClub}>
            Update
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default ClubBody;
