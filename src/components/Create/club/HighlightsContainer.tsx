import { <PERSON>, But<PERSON>, <PERSON>, TextField, Typography } from "@mui/material";
import React from "react";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import AddCircleOutline from "@mui/icons-material/AddCircleOutline";

const eventTitleStyles = { marginBottom: "20px" };

const HighlightsContainer = ({ highlights, setHighlights }) => {
  return (
    <Card sx={{ p: 2, mb: 5, mt: 4 }}>
      <Typography
        sx={{
          mb: 5,
          fontWeight: 600,
        }}
      >
        Club Highlights
      </Typography>
      {highlights.map((m, i) => (
        <Box
          key={`highlight-${i}`}
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          sx={eventTitleStyles}
          gap={2}
        >
          <TextField
            multiline
            rows={2}
            label={`Club Highlight ${i + 1}`}
            variant="outlined"
            fullWidth
            value={m}
            onChange={(e) => {
              const changedValue = e.target.value;
              setHighlights((prev) => {
                const newHighlights = prev.map((h, j) => {
                  if (j === i) {
                    return changedValue;
                  } else {
                    return h;
                  }
                });
                return newHighlights;
              });
            }}
          />
          {highlights.length !== 1 && (
            <Box
              sx={{ height: "100%", cursor: "pointer" }}
              onClick={() => {
                setHighlights((prev) => prev.filter((f, j) => j !== i));
              }}
            >
              <DeleteOutlinedIcon />
            </Box>
          )}
        </Box>
      ))}
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "flex-end",
        }}
      >
        <Button
          onClick={() => {
            setHighlights((prev) => [...prev, ""]);
          }}
        >
          <AddCircleOutline />
          &nbsp;Add
        </Button>
      </Box>
    </Card>
  );
};

export default HighlightsContainer;
