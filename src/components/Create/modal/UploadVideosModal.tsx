import React, { useState } from "react";
import Modal from "@mui/material/Modal";
import Box from "@mui/material/Box";
import ModalHeader from "./ModalHeader";
import ModalFooter from "./ModalFooter";
import VideoDetails from "../video/VideoDetails";
import SelectCreator from "../video/SelectCreator";
import {
  getCategoryNamesBasedOnId,
  handleCreateVideo,
  validateUploadVideoFields,
} from "@/utils/videoUtil";
import { useSnackbar } from "@/hooks/useSnackbar";
import AddToCollection from "../video/AddToCollection";
import UploadCaptions from "../video/UploadCaptions";
import UploadThumbnails from "../video/UploadThumbnails";
import { Maybe, ValueOf } from "@/types";
import { Divider } from "@mui/material";
import { CategoryType } from "@/api/mongoTypes";
import { LEVEL_TYPES } from "@/constant/Enums";
import SectionTitle from "../video/SectionTitle";

const UploadVideosModal = ({
  open,
  setOpen,
  existingCollection,
  setExistingCollection,
}) => {
  const { showSnackbar } = useSnackbar();
  const [duration, setDuration] = useState(0);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [language, setLanguage] = useState("");
  const [languageProficiency, setLanguageProficiency] = useState("");
  const [selectedCreator, setSelectedCreator] = useState("");
  const [file, setFile] = useState<File | undefined>();
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [selectedThumbnail, setSelectedThumbnail] = useState<Maybe<File>>(null);
  const [selectedSubtitle, setSelectedSubtitle] = useState<Maybe<File>>(null);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isFeatured, setIsFeatured] = useState(false);
  const [level, setLevel] = useState(LEVEL_TYPES.BEGINNER);
  const [selectedEnglishSubtitle, setSelectedEnglishSubtitle] =
    useState<Maybe<File>>(null);
  const [selectedSpanishSubtitle, setSelectedSpanishSubtitle] =
    useState<Maybe<File>>(null);

  const handleClose = () => setOpen(false);

  const addToCollection = async () => {
    try {
      setIsCreating(true);
      const validationError = validateUploadVideoFields({
        title,
        description,
        language,
        languageProficiency,
        selectedCreator,
        selectedCategories,
        selectedThumbnail,
        selectedEnglishSubtitle,
        selectedSpanishSubtitle,
        selectedCollection,

        ignoreCollection: true,
      });
      if (validationError) {
        showSnackbar(validationError, {
          type: "error",
        });
        setIsCreating(false);
        return;
      }
      await handleCreateVideo({
        title,
        description,
        language,
        proficiencyLevel: languageProficiency,
        categoriesId: selectedCategories,
        categoriesName: getCategoryNamesBasedOnId(
          categories,
          selectedCategories
        ),
        userId: selectedCreator,
        duration,
        collectionId: "",
        file,
        level,
        isFeatured,
        selectedThumbnail,
        selectedEnglishSubtitle,
        selectedSpanishSubtitle,
        onVideoCrateSuccess: () => {},
        onVideoUploadSuccess: ({ data }) => {
          setExistingCollection((prev) => [...prev, data]);
          setFile(null);
          setSelectedCategories([]);
          setTitle("");
          setDescription("");
          setLanguage("");
          setLanguageProficiency("");
          setSelectedCollection(null);
          setSelectedCreator(null);
          setIsFeatured(false);
          setLevel(LEVEL_TYPES.BEGINNER);
          setSelectedSubtitle(null);
          setSelectedThumbnail(null);
          showSnackbar("Created video successfully", {
            type: "success",
          });
          handleClose();
        },
        onVideoCreateError: () => {
          showSnackbar(
            "Something went wrong while uploading .Please try again",
            {
              type: "error",
            }
          );
        },
      });
      setIsCreating(false);
    } catch (error) {
      setIsCreating(false);
      console.error(`Something went wrong in addToCollection due to `, error);
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            sm: 600,
          },
          height: {
            xs: "100%",
            sm: 600,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
          overflowY: "scroll",
        }}
      >
        <ModalHeader close={handleClose} title="Upload video" />
        <Box p={10}>
          <VideoDetails
            isModal
            title={title}
            setTitle={setTitle}
            description={description}
            setDescription={setDescription}
            languageProficiency={languageProficiency}
            setLanguageProficiency={setLanguageProficiency}
            language={language}
            setLanguage={setLanguage}
            selectedCategories={selectedCategories}
            setSelectedCategories={setSelectedCategories}
            duration={duration}
            setDuration={setDuration}
            file={file}
            setFile={setFile}
            categories={categories}
            setCategories={setCategories}
            isFeatured={isFeatured}
            setIsFeatured={setIsFeatured}
            level={level}
            setLevel={setLevel}
          />
          <Divider
            sx={{
              marginTop: 10,
              marginBottom: 10,
            }}
          />
          <SelectCreator
            selectedCreator={selectedCreator}
            setSelectedCreator={setSelectedCreator}
          />
          {/* <AddToCollection
            selectedCollection={selectedCollection}
            setSelectedCollection={setSelectedCollection}
          /> */}
          <Divider
            sx={{
              marginTop: 10,
              marginBottom: 10,
            }}
          />
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
          >
            <SectionTitle text="Captions / Subtitles" />
            <UploadCaptions
              language="English"
              key="English-UploadCaptions"
              selectedSubtitle={selectedEnglishSubtitle}
              setSelectedSubtitle={setSelectedEnglishSubtitle}
            />
            <UploadCaptions
              key="Spanish-UploadCaptions"
              language="Spanish"
              selectedSubtitle={selectedSpanishSubtitle}
              setSelectedSubtitle={setSelectedSpanishSubtitle}
            />
          </Box>
          <Divider
            sx={{
              marginTop: 10,
              marginBottom: 10,
            }}
          />
          <UploadThumbnails
            selectedThumb={selectedThumbnail}
            setSelectedThumb={setSelectedThumbnail}
          />
        </Box>
        <ModalFooter
          title={isCreating ? "Adding..." : "Add to Collection"}
          onClick={addToCollection}
        />
      </Box>
    </Modal>
  );
};

export default UploadVideosModal;
