import { useEffect, useState } from "react";
import { Box, Button, Typography, Modal } from "@mui/material";
import ModalHeader from "./ModalHeader";
import VideoCard from "../VideoCard";
import axios from "axios";
import InfiniteScroll from "react-infinite-scroll-component";

const LIMIT = 10;

const ExistingVideosModal = ({
  open,
  setOpen,
  existingCollection,
  setExistingCollection,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [skip, setSkip] = useState(0);
  const [videos, setVideos] = useState([]);

  const handleClose = () => setOpen(false);

  const fetchVideos = async ({ skip = 0 }) => {
    try {
      if (skip === 0) {
        setIsLoading(true);
      }
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/video/all`,
        {
          params: {
            skip,
            limit: LIMIT,
            needCreator: true,
          },
        }
      );
      if (data.success) {
        setVideos((prev) => [...prev, ...data.data]);
        if (data.data.length < LIMIT) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }
      }
      setSkip(skip + LIMIT);
      setIsLoading(false);
    } catch (error) {
      console.error(`Something went wrong in fetchVideos due to `, error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchVideos({ skip: 0 });
    }
  }, [open]);

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            sm: "100%",
            md: 900,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
        }}
      >
        <ModalHeader close={handleClose} title="Existing Videos " />

        <Typography
          sx={{
            textAlign: "center",
            fontSize: 14,
            color: "rgba(100, 116, 139, 1)",
          }}
          id="modal-modal-title"
          my={5}
          variant="h6"
          component="h2"
        >
          Select videos to include in your new collection
        </Typography>

        <Box
          maxHeight="400px"
          id="scrollable-container"
          sx={{ px: 20, overflowY: "scroll" }}
        >
          <InfiniteScroll
            scrollableTarget="scrollable-container"
            dataLength={videos.length}
            next={() => {
              fetchVideos({
                skip,
              });
            }}
            hasMore={hasMore}
            loader={<h4>Loading...</h4>}
            endMessage={
              <p style={{ textAlign: "center" }}>
                <b>Yay! You have seen it all</b>
              </p>
            }
          >
            {videos.map((m, i) => (
              <VideoCard
                data={m}
                key={i}
                isChecked={existingCollection.some((f) => f._id === m._id)}
                isModal
                onCheckedClicked={(e) => {
                  if (e) {
                    setExistingCollection((prev) => [...prev, m]);
                  } else {
                    setExistingCollection((prev) => {
                      const removedList = prev.filter((f) => f._id !== m._id);
                      return removedList;
                    });
                  }
                }}
              />
            ))}
          </InfiniteScroll>
        </Box>

        <Footer onClick={handleClose} />
      </Box>
    </Modal>
  );
};

const Footer = ({ onClick }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      sx={{
        fontSize: 20,
        fontWeight: 600,
        padding: 3,
        borderTop: "1px solid silver",
      }}
    >
      <Button onClick={onClick} sx={{ width: 300 }}>
        Add to Collection
      </Button>
    </Box>
  );
};

export default ExistingVideosModal;
