// import React, {
//   useEffect,
//   useState,
//   forwardRef,
//   ForwardedRef,
//   useCallback,
// } from "react";
// import MUIStepper from "./MUIStepper";
// import Button from "@mui/material/Button";
// import TextField from "@mui/material/TextField";
// import { useTransition, animated, useSpringRef } from "react-spring";
// import { useForm } from "react-hook-form";
// import { userCreateValidator } from "@/api/validators";
// import {
//   FormControl,
//   FormHelperText,
//   InputLabel,
//   MenuItem,
//   Select,
//   Typography,
// } from "@mui/material";
// import { useMutation, useQuery } from "react-query";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { LanguageProficiencyType, LanguageType } from "@/api/mongoTypes";

// const steps = ["uno", "dos", "tres", "cuatro"];
// const btnStyle = {
//   margin: "15px 0 5px",
//   height: "55px",
// };

// function SignupForm({ makeFormVisible }, ref: ForwardedRef<HTMLDivElement>) {
//   const { data: languageData } = useQuery<{
//     languages: LanguageType[];
//     languageProficiencies: LanguageProficiencyType[];
//   }>("getLanguages", () =>
//     fetch("/api/getLanguages").then((res) => res.json())
//   );
//   const {
//     register,
//     handleSubmit,
//     setValue,
//     setError,
//     watch,
//     formState: { errors, touchedFields, dirtyFields, isValid },
//   } = useForm({ resolver: zodResolver(userCreateValidator), mode: "all" });
//   const proficiencies = watch("proficiencies", {} as Record<string, string>);
//   const languagesValue = watch("languages", [] as string[]);
//   const primaryLanguage = watch("primaryLanguage");
//   const primaryLanguageObj = languageData?.languages.find(
//     (l) => l.code === primaryLanguage
//   );
//   const primaryLanguageId = primaryLanguageObj?._id;
//   const nativeSpeakerObj = languageData?.languageProficiencies.find(
//     (proficiency) => proficiency.proficiencyLevel_en === "Native Speaker"
//   );
//   const nativeSpeakerId = nativeSpeakerObj?._id;

//   const [languages, setLanguages] = useState([] as LanguageType[]);
//   const [stepIndex, setStepIndex] = useState(0);

//   const transRef = useSpringRef();
//   const data = [
//     <div key="data1" className="mx-auto flex flex-col">
//       <div className="my-4 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
//         <TextField
//           {...register("firstName", { required: true })}
//           variant="outlined"
//           error={!!errors.firstName?.message}
//           helperText={errors.firstName?.message as string}
//           required
//           autoFocus={true}
//           fullWidth
//           autoComplete="given-name"
//           label="Nombre/First Name"
//         />
//         <TextField
//           {...register("lastName", { required: true })}
//           hidden={stepIndex !== 0}
//           variant="outlined"
//           error={!!errors.lastName?.message}
//           helperText={errors.lastName?.message as string}
//           required
//           fullWidth
//           autoComplete="family-name"
//           label="Apellido/Last Name"
//         />
//       </div>
//       <div className="w-full">
//         <FormControl fullWidth>
//           <InputLabel id="primaryLanguage">
//             Idioma Primario/Primary Language
//           </InputLabel>
//           <Select
//             {...register("primaryLanguage", { required: true })}
//             labelId="primaryLanguage"
//             label="Idioma Primario/Primary Language"
//             defaultValue={"en"}
//           >
//             {languageData?.languages.map((l) => (
//               <MenuItem key={l.code} value={l.code}>
//                 {l.name}
//               </MenuItem>
//             ))}
//           </Select>
//         </FormControl>
//       </div>
//     </div>,
//     <div key="data2" className="mx-auto flex flex-col">
//       <div className="my-4 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
//         <TextField
//           {...register("email", { required: true })}
//           variant="outlined"
//           error={!!errors.email?.message}
//           helperText={errors.email?.message as string}
//           required
//           fullWidth
//           label="Email"
//           autoComplete="email"
//           autoFocus={true}
//         />
//         <TextField
//           {...register("phone", { required: false })}
//           variant="outlined"
//           error={!!errors.phone?.message}
//           helperText={errors.phone?.message as string}
//           fullWidth
//           autoComplete="tel"
//           label="Whatsapp Teléfono/Phone Number"
//         />
//       </div>
//     </div>,
//     <div key="data3" className="mx-auto flex flex-col">
//       <div className="my-4 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
//         <FormControl
//           fullWidth
//           className="w-full mt-4"
//           error={!!errors.languages}
//         >
//           <InputLabel id="languages-select-label">
//             What other languages do you speak?
//           </InputLabel>
//           <Select
//             {...register("languages", {
//               required: true,
//             })}
//             onChange={(e) => {
//               const lFiltered = languageData?.languages.filter((l) =>
//                 (e.target.value as string[]).includes(l._id)
//               );
//               setLanguages(lFiltered);
//               setValue("languages", (e.target.value as string[]) || []);
//               const profs = lFiltered?.reduce(
//                 (acc, l) => ({
//                   ...acc,
//                   [l._id]:
//                     proficiencies?.[l._id] ||
//                     languageData.languageProficiencies[0]._id,
//                 }),
//                 {} as Record<string, string>
//               );
//               setValue("proficiencies", profs || {});
//             }}
//             value={languagesValue || []}
//             label="Select one or more languages"
//             labelId="languages-select-label"
//             multiple
//           >
//             {languageData?.languages
//               .filter((language) => language._id !== primaryLanguageId)
//               .map((language) => (
//                 <MenuItem key={language._id} value={language._id}>
//                   {language.name}
//                 </MenuItem>
//               ))}
//           </Select>
//           {!!errors.languages && (
//             <FormHelperText>
//               {errors.languages.message as string}
//             </FormHelperText>
//           )}
//         </FormControl>
//       </div>
//       {languages.map((l) => (
//         <>
//           <FormControl
//             fullWidth
//             className="w-full mt-4"
//             error={!!errors[l._id]}
//           >
//             <InputLabel id={`language-${l._id}-select-label`}>
//               {l.name} proficiency
//             </InputLabel>

//             <Select
//               onChange={(e) => {
//                 setValue("proficiencies", {
//                   ...proficiencies,
//                   [l._id]: e.target.value || [],
//                 });
//               }}
//               value={
//                 proficiencies[l._id] ||
//                 languageData.languageProficiencies[0] ||
//                 []
//               }
//               label={`${l.name} proficiency`}
//               labelId={`language-${l._id}-select-label`}
//               error={!!errors[l._id]}
//             >
//               {(
//                 languageData?.languageProficiencies as LanguageProficiencyType[]
//               )?.map((prof) => (
//                 <MenuItem key={prof._id} value={prof._id}>
//                   {prof.proficiencyLevel_en}
//                 </MenuItem>
//               ))}
//             </Select>
//             {!!errors[l._id] && (
//               <FormHelperText>This is required!</FormHelperText>
//             )}
//           </FormControl>
//         </>
//       ))}
//     </div>,
//     <div key="data4" className="mx-auto flex flex-col">
//       <div className="my-4 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
//         <TextField
//           {...register("password", { required: true })}
//           variant="outlined"
//           error={!!errors.password?.message}
//           helperText={errors.password?.message as string}
//           required
//           fullWidth
//           label="Contraseña/Password"
//           type="password"
//           autoComplete="new-password"
//         />
//         <TextField
//           {...register("confirmPassword", { required: true })}
//           variant="outlined"
//           error={!!errors.confirmPassword?.message}
//           helperText={errors.confirmPassword?.message as string}
//           required
//           fullWidth
//           type="password"
//           autoComplete="new-password"
//           label="Confirmación de contraseña/Confirm Password"
//         />
//       </div>
//     </div>,
//   ];
//   const handleNext = useCallback(
//     async (e) => {
//       console.info(errors);
//       if (stepIndex === 0 && (errors.firstName || errors.lastName)) {
//         return;
//       }
//       if (stepIndex === 1 && (errors.phone || errors.email)) {
//         return;
//       }
//       if (stepIndex === 2 && (errors.password || errors.passwordConfirmation)) {
//         return;
//       }
//       setStepIndex(stepIndex + 1);
//     },
//     [errors, stepIndex]
//   );

//   const transitions = useTransition([stepIndex], {
//     from: { opacity: 0, transform: "translate3d(30%,0,0)" },
//     enter: { opacity: 1, transform: "translate3d(0%,0,0)" },
//     leave: { opacity: 0, transform: "translate3d(-30%,0,0)" },
//     exitBeforeEnter: true,
//     config: { duration: 500 },
//     ref: transRef,
//   });

//   useEffect(() => {
//     transRef.start();
//   }, [transRef]);

//   useEffect(() => {
//     transRef.start();
//   }, [stepIndex, transRef]);

//   const createUser = useMutation(
//     "createUser",
//     async (data: { [key: string]: any }) => {
//       const res = await fetch("/api/signup", {
//         method: "POST",
//         body: JSON.stringify(data),
//         headers: { "Content-Type": "application/json" },
//       });
//       return await res.json();
//     },
//     {
//       onSuccess: (r) => {
//         if (r?.success) {
//           setStepIndex(stepIndex + 1);
//           setTimeout(() => {
//             window.location.href = "/";
//           }, 2000);
//         } else {
//           if (r?.errors?.fieldErrors) {
//             for (const err of r?.errors?.fieldErrors) {
//               console.info(err);
//               setError(
//                 err.field,
//                 { message: err.messages.join(" ") },
//                 { shouldFocus: true }
//               );
//             }
//           }
//           if (errors["email"]) {
//             setStepIndex(1);
//           }
//         }
//       },
//       onError: (error) => {
//         console.error(error);
//       },
//     }
//   );

//   const onSubmit = useCallback(
//     (data) => {
//       const updatedProficiencies = {
//         ...proficiencies,
//         [primaryLanguageId]: nativeSpeakerId,
//       };
//       createUser.mutate({ ...data, proficiencies: updatedProficiencies });
//     },
//     [createUser, proficiencies, nativeSpeakerId, primaryLanguageId]
//   );

//   const handleBack = () => {
//     if (stepIndex > 0 && stepIndex < steps.length) {
//       setStepIndex(stepIndex - 1);
//     }
//   };

//   return (
//     <div ref={ref} className="w-full">
//       <MUIStepper step={stepIndex} steps={steps} />
//       {stepIndex === steps.length && (
//         <div className="mx-auto text-center mt-6">
//           <Typography variant="h4">Thank you for signing up!</Typography>
//         </div>
//       )}
//       <form
//         onSubmit={handleSubmit(onSubmit)}
//         className="flex flex-col items-center w-full"
//       >
//         {transitions((style, item) => {
//           return (
//             <animated.div style={style} className={"w-full"}>
//               {data[item]}
//             </animated.div>
//           );
//         })}
//         <div ref={ref} style={{ display: "flex" }}>
//           <Button
//             onClick={handleBack}
//             style={btnStyle}
//             sx={{
//               fontWeight: 600,
//               fontSize: "1.2rem",
//               color: "white",
//               width: "160px",
//               borderRadius: "10px",
//               backgroundColor: "#F3B358",
//               border: "none",
//               "&:hover": {
//                 backgroundColor: "#02AC9F",
//               },
//               display:
//                 stepIndex !== 0 && stepIndex < steps.length ? "block" : "none",
//             }}
//           >
//             Back
//           </Button>

//           <Button
//             onClick={handleNext}
//             className="ml-2"
//             style={{ ...btnStyle, marginLeft: stepIndex == 0 ? 0 : 10 }}
//             sx={{
//               fontWeight: 600,
//               fontSize: "1.2rem",
//               color: "white",
//               width: "160px",
//               borderRadius: "10px",
//               backgroundColor: "#F3B358",
//               border: "none",
//               "&:hover": {
//                 backgroundColor: "#02AC9F",
//               },
//               display:
//                 stepIndex < steps.length - 1 && stepIndex >= 0
//                   ? "block"
//                   : "none",
//             }}
//           >
//             Next
//           </Button>

//           <Button
//             className="ml-2"
//             style={{ ...btnStyle }}
//             variant="contained"
//             color="primary"
//             type="submit"
//             sx={{
//               display: stepIndex === steps.length - 1 ? "block" : "none",
//             }}
//           >
//             Submit
//           </Button>
//         </div>
//       </form>
//     </div>
//   );
// }
// export default forwardRef(SignupForm);
