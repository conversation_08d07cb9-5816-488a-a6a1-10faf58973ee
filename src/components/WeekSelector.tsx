import React, { useState, useMemo, useEffect } from "react";
import { MenuItem, Select, Box } from "@mui/material";
import {
  format,
  addWeeks,
  startOfWeek,
  endOfWeek,
  isSameDay,
  addDays,
  startOfDay,
  parseISO,
  isEqual,
  isAfter,
  isBefore,
} from "date-fns";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";

type WeekSelectorProps = React.FC<{
  startDate: Date;
  setStartDate: React.Dispatch<React.SetStateAction<Date>>;
  endDate: Date;
  setEndDate: React.Dispatch<React.SetStateAction<Date>>;
  onChange: ({
    startDate,
    endDate,
  }: {
    startDate: Date;
    endDate: Date;
  }) => void;
  weekOptions: any[];
}>;

const WeekSelector: WeekSelectorProps = ({
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  onChange,
  weekOptions,
}) => {
  const [selectedWeekIndex, setSelectedWeekIndex] = useState(0);

  useEffect(() => {
    if (startDate && endDate && weekOptions.length > 0) {
      const findWeekIndex = (data) => {
        const { startDate, endDate, weekOptions } = data;

        // Convert the target dates to Date objects
        const targetStartDate = new Date(startDate);
        const targetEndDate = new Date(endDate);

        // Strip time portion for date-only comparison if needed
        const targetStartDay = new Date(
          targetStartDate.toISOString().split("T")[0]
        );
        const targetEndDay = new Date(
          targetEndDate.toISOString().split("T")[0]
        );

        // Find the matching week option
        for (let i = 0; i < weekOptions.length; i++) {
          const option = weekOptions[i];
          const optionStartDate = new Date(option.startDate);
          const optionEndDate = new Date(option.endDate);

          // Strip time portion for date-only comparison
          const optionStartDay = new Date(
            optionStartDate.toISOString().split("T")[0]
          );
          const optionEndDay = new Date(
            optionEndDate.toISOString().split("T")[0]
          );

          // Check if dates fall within this week option
          // Case 1: Exact match on start and end dates
          if (
            targetStartDate.getTime() === optionStartDate.getTime() &&
            targetEndDate.getTime() === optionEndDate.getTime()
          ) {
            return option.value;
          }

          // Case 2: Date-only match (ignoring time)
          if (
            targetStartDay.getTime() === optionStartDay.getTime() ||
            targetEndDay.getTime() === optionEndDay.getTime()
          ) {
            return option.value;
          }

          // Case 3: Date range falls within this option's range
          if (
            targetStartDate >= optionStartDate &&
            targetEndDate <= optionEndDate
          ) {
            return option.value;
          }
        }

        return -1;
      };
      const foundIndex = findWeekIndex({
        startDate,
        endDate,
        weekOptions,
      });
      const filtered = foundIndex === -1 ? 0 : foundIndex;
      setSelectedWeekIndex(filtered);
      const selectedWeek = weekOptions[filtered];
      if (isSameDay(startDate, endDate)) {
        setStartDate(selectedWeek.startDate);
        setEndDate(selectedWeek.endDate);
      }
    }
  }, [startDate, endDate, weekOptions]);

  const handleWeekChange = (event) => {
    const newIndex = event.target.value;
    setSelectedWeekIndex(newIndex);

    const selectedWeek = weekOptions[newIndex];
    setStartDate(selectedWeek.startDate);
    setEndDate(selectedWeek.endDate);
    onChange &&
      onChange({
        endDate: selectedWeek.endDate,
        startDate: selectedWeek.startDate,
      });
  };

  console.log({
    startDate,
    endDate,
    weekOptions,
  });

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "flex-end",
        border: "1px solid #CCCCCC",
        borderRadius: 2,
        overflow: "hidden",
        height: 45,
        mb: 2,
        width: "fit-content",
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        height="100%"
        width={50}
        sx={{ background: "#F5F5F5" }}
      >
        <CalendarMonthIcon />
      </Box>
      <Select
        labelId="week-select-label"
        id="week-select"
        value={selectedWeekIndex}
        onChange={handleWeekChange}
        sx={{
          m: 0,
          width: "fit-content",
          fontSize: 14,
          height: 45,
          border: "none",
          outline: "none",
          "&.Mui-focused": {
            outline: "none",
            boxShadow: "none",
          },
          "& .MuiSelect-select": {
            height: 45,
            padding: 0,
            lineHeight: "45px",
            px: 2,
          },
          "& .MuiInputBase-root": {
            "&.Mui-focused": {
              outline: "none",
            },
            "&::before": {
              borderBottom: "none !important",
            },
            "&::after": {
              borderBottom: "none !important",
            },
            "&:hover:not(.Mui-disabled)::before": {
              borderBottom: "none !important",
            },
          },
          "& .MuiOutlinedInput-notchedOutline": {
            border: "none",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            border: "none",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            border: "none",
            outline: "none",
          },
        }}
      >
        {weekOptions.map((week) => (
          <MenuItem key={week.value} value={week.value}>
            {week.label}
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
};

export default WeekSelector;
