import { getPriceSymbol } from "@/utils/classes";
import { Box, Typography } from "@mui/material";
import React from "react";

const totalContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "end",
};
const totalLabelStyles = {
  color: "#AEAEAE",
  fontWeight: "500",
  fontSize: "24px",
  marginRight: "15px",
  textTransform: "capitalize",
};
const totalPriceStyles = {
  fontWeight: "500",
  fontSize: "24px",
  textTransform: "capitalize",
};

const TotalContainer = ({ price, currency, onlyPrice = false }) => {
  const priceStyle = (() => {
    let style = {};
    if (onlyPrice) {
      style = {
        fontSize: "16px",
        fontWeight: "700",
      };
    }
    return {
      ...totalPriceStyles,
      ...style,
    };
  })();

  return (
    <Box sx={totalContainerStyles}>
      {!onlyPrice && <Typography sx={totalLabelStyles}>Total: </Typography>}
      <Typography sx={priceStyle}>
        {getPriceSymbol({
          currency,
        })}
        {price}
      </Typography>
    </Box>
  );
};

export default TotalContainer;
