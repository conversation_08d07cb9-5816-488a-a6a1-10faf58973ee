import { Box, Card, Typography } from "@mui/material";
import Image from "next/image";
import React, { useMemo, useState } from "react";
import CreatorDetails from "../classes/OnlineClub/CreatorDetails";
import Tag from "../Tag";
import Rating from "../classes/OnlineClub/Rating";
import EditButton from "./EditButton";
import DeleteButton from "./DeleteButton";
import { CartType, ClubType, UserType } from "@/api/mongoTypes";
import useCart from "@/hooks/useCart";
import CheckoutClubsModal from "../CheckoutModal/CheckoutClub/CheckoutClubsModal";
import { getClubImage } from "@/utils/classes";
import TotalContainer from "./TotalContainer";
import useTranslate from "@/hooks/useTranslate";

const cardContainerStyles = {
  padding: "20px 30px",
  border: "1px solid rgba(217, 217, 217, 1)",
  boxShadow: "0",
  marginBottom: "25px",
  borderRadius: "10px",
};

const titleContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
};

const cardTitleStyles = {
  textTransform: "capitalize",
  marginBottom: 1,
  fontWeight: "500",
  fontSize: "24px",
};

type ClubCartCardProps = React.FC<{
  data: CartType;
  handleDeleteSuccess: () => void;
  onUpdateCartSuccess: (data: CartType) => void;
}>;
const ClubCartCard: ClubCartCardProps = ({
  data,
  handleDeleteSuccess,
  onUpdateCartSuccess,
}) => {
  const { translate } = useTranslate();
  const clubDetails = data.clubId as ClubType;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const creatorDetails = clubDetails.teachers.map((m) => m as UserType);
  const image = getClubImage(clubDetails);

  const { handleDelete, isDeletingCart } = useCart({
    onAddingSuccess: () => {},
    onDeleteSuccess: () => {
      handleDeleteSuccess();
    },
  });

  const showPriceFooter = useMemo(() => {
    return data.memberships.length > 1;
  }, [data?.memberships]);

  const rating = clubDetails?.ratings?.value ?? 0;

  return (
    <>
      <Card sx={cardContainerStyles} aria-disabled={isDeletingCart}>
        <Box
          width="100%"
          flexDirection="row"
          sx={{
            display: "flex",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
          }}
          gap={2}
        >
          <Box
            sx={{
              width: {
                xs: "100%",
                sm: "30%",
              },
            }}
            style={{ height: 70, position: "relative" }}
          >
            <Image
              alt="beruh"
              src={image}
              quality={75}
              objectFit="cover"
              priority
              fill
              style={{
                overflow: "hidden",
                borderRadius: 5,
              }}
            />
          </Box>
          <Box
            sx={{
              width: {
                xs: "100%",
                sm: "70%",
              },
            }}
          >
            <Box sx={titleContainerStyles}>
              <Typography textAlign="start" sx={cardTitleStyles}>
                {clubDetails.title}
              </Typography>
              <Box display="flex" flexDirection="row" gap={3}>
                <EditButton
                  onClick={() => {
                    setIsModalOpen(true);
                  }}
                />
                <DeleteButton
                  onClick={() => {
                    handleDelete({ id: data._id });
                  }}
                />
              </Box>
            </Box>

            <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
              <span
                style={{
                  height: 6,
                  width: 6,
                  borderRadius: 3,
                  background: "#F9B238",
                }}
              />
              <Typography sx={{ fontSize: 12, color: "#6D6D6D" }}>
                {translate("class.online-club")}
              </Typography>
            </Box>

            <CreatorDetails creatorDetails={creatorDetails} />

            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              sx={{ overflow: "hidden" }}
              mt={5}
            >
              {clubDetails.categories?.map((m) => (
                <Tag text={m} key={m} />
              ))}
            </Box>

            <Box
              mt={5}
              display="flex"
              flexDirection="row"
              alignItems="center"
              justifyContent="space-between"
            >
              {rating > 0 && (
                <Box display="flex" flexDirection="row" alignItems="center">
                  <Rating />
                  &nbsp;
                  <Typography fontSize={12}>
                    ({rating} {translate("clubSubs.rating")})
                  </Typography>
                </Box>
              )}
              {+clubDetails?.noOfStudentsEnrolled > 0 && (
                <Typography color="rgba(129, 129, 129, 1)" fontSize={12}>
                  {`${clubDetails?.noOfStudentsEnrolled} ${translate(
                    "clubSubs.student-enrolled"
                  )}`}
                </Typography>
              )}
            </Box>
          </Box>
        </Box>
        {data.memberships.map((m, i) => (
          <Box
            key={i}
            width="100%"
            display="flex"
            flexDirection="row"
            sx={{
              borderTop:
                data.memberships.length === 1 &&
                "1px solid rgba(245, 245, 245, 1)",
              py: 2,
              mt: 2,
            }}
          >
            <Box
              width="30%"
              sx={{
                width: {
                  xs: 0,
                  sm: "30%",
                },
              }}
            ></Box>
            <Box
              sx={{
                width: {
                  xs: "100%",
                  sm: "70%",
                },
              }}
              display="flex"
              flexDirection="row"
              justifyContent="space-between"
            >
              <Membership emailId={m.emailId} />
              <TotalContainer
                currency={clubDetails.currency}
                onlyPrice={showPriceFooter}
                price={clubDetails.price}
              />
            </Box>
          </Box>
        ))}
        {showPriceFooter && (
          <Box
            width="100%"
            display="flex"
            flexDirection="row"
            justifyContent="end"
            sx={{
              borderTop: "1px solid rgba(245, 245, 245, 1)",
              py: 2,
              mt: 2,
            }}
          >
            <TotalContainer
              currency={clubDetails.currency}
              price={+clubDetails.price * data?.memberships?.length}
            />
          </Box>
        )}
      </Card>
      {isModalOpen && (
        <CheckoutClubsModal
          isUpdate={data}
          open={isModalOpen}
          setOpen={setIsModalOpen}
          data={clubDetails}
          onUpdateCartSuccess={(data) => {
            onUpdateCartSuccess(data);
            setIsModalOpen(false);
          }}
        />
      )}
    </>
  );
};

const Membership = ({ emailId }) => {
  const isSomeOneElse = emailId.length > 0;
  const { translate } = useTranslate();

  return (
    <Box display="flex" flexDirection="row" gap={2}>
      <Typography color="rgba(109, 109, 109, 1)" fontSize={14}>
        {translate("clubSubs.membership")}:
      </Typography>
      <Box display="flex" flexDirection="column" width="100%">
        <Box
          width="100%"
          display="flex"
          flexDirection="row"
          alignItems="center"
        >
          <Typography color="rgba(60, 60, 60, 1)" fontSize={15}>
            {emailId.length > 0 ? <UserIcon isMultiple /> : <UserIcon />}
            &nbsp;
            {emailId.length > 0
              ? translate("common.someone-else")
              : translate("common.myself")}
          </Typography>
        </Box>
        {isSomeOneElse && (
          <Box
            display="flex"
            width="100%"
            flexDirection="row"
            alignItems="center"
          >
            <MailIcon /> &nbsp;
            <Typography color="rgba(109, 109, 109, 1)" fontSize={14}>
              {emailId}
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

const MailIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 18 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.375 0.375H14.75C15.4462 0.375 16.1139 0.651562 16.6062 1.14384C17.0984 1.63613 17.375 2.30381 17.375 3V10.875C17.375 11.5712 17.0984 12.2389 16.6062 12.7312C16.1139 13.2234 15.4462 13.5 14.75 13.5H3.375C2.67881 13.5 2.01113 13.2234 1.51884 12.7312C1.02656 12.2389 0.75 11.5712 0.75 10.875V3C0.75 2.30381 1.02656 1.63613 1.51884 1.14384C2.01113 0.651562 2.67881 0.375 3.375 0.375ZM3.375 1.25C2.9375 1.25 2.5525 1.39875 2.255 1.66125L9.0625 6.0625L15.87 1.66125C15.5725 1.39875 15.1875 1.25 14.75 1.25H3.375ZM9.0625 7.12125L1.73875 2.37C1.66875 2.5625 1.625 2.78125 1.625 3V10.875C1.625 11.3391 1.80937 11.7842 2.13756 12.1124C2.46575 12.4406 2.91087 12.625 3.375 12.625H14.75C15.2141 12.625 15.6592 12.4406 15.9874 12.1124C16.3156 11.7842 16.5 11.3391 16.5 10.875V3C16.5 2.78125 16.4563 2.5625 16.3863 2.37L9.0625 7.12125Z"
        fill="#6D6D6D"
      />
    </svg>
  );
};

const UserIcon = ({ isMultiple = false }) => {
  if (isMultiple) {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 19 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.375 14.5C17.375 12.9758 15.9137 11.6799 13.875 11.1986M12.125 14.5C12.125 12.5662 9.77475 11 6.875 11C3.97525 11 1.625 12.5662 1.625 14.5M12.125 8.375C13.0533 8.375 13.9435 8.00625 14.5999 7.34987C15.2563 6.6935 15.625 5.80326 15.625 4.875C15.625 3.94674 15.2563 3.0565 14.5999 2.40013C13.9435 1.74375 13.0533 1.375 12.125 1.375M6.875 8.375C5.94674 8.375 5.0565 8.00625 4.40013 7.34987C3.74375 6.6935 3.375 5.80326 3.375 4.875C3.375 3.94674 3.74375 3.0565 4.40013 2.40013C5.0565 1.74375 5.94674 1.375 6.875 1.375C7.80326 1.375 8.6935 1.74375 9.34987 2.40013C10.0063 3.0565 10.375 3.94674 10.375 4.875C10.375 5.80326 10.0063 6.6935 9.34987 7.34987C8.6935 8.00625 7.80326 8.375 6.875 8.375Z"
          stroke="#3C3C3C"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    );
  }
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 13 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.75 14.5C11.75 12.5662 9.39975 11 6.5 11C3.60025 11 1.25 12.5662 1.25 14.5M6.5 8.375C5.57174 8.375 4.6815 8.00625 4.02513 7.34987C3.36875 6.6935 3 5.80326 3 4.875C3 3.94674 3.36875 3.0565 4.02513 2.40013C4.6815 1.74375 5.57174 1.375 6.5 1.375C7.42826 1.375 8.3185 1.74375 8.97487 2.40013C9.63125 3.0565 10 3.94674 10 4.875C10 5.80326 9.63125 6.6935 8.97487 7.34987C8.3185 8.00625 7.42826 8.375 6.5 8.375Z"
        stroke="#3C3C3C"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default ClubCartCard;
