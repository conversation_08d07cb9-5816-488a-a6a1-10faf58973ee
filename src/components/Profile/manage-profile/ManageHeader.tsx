import { Box, Typography } from "@mui/material";
import { useRouter } from "next/router";
import React from "react";

const ManageHeader = ({ goBack = false }) => {
  const router = useRouter();
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      width="100%"
      sx={{ position: "relative" }}
    >
      <svg
        style={{
          cursor: "pointer",
          top: "50%",
          bottom: "50%",
          left: "5%",
          position: "absolute",
        }}
        onClick={() => {
          if (goBack) {
            router.back();
          } else {
            router.push("/profile/dashboard");
          }
        }}
        width="35"
        height="35"
        viewBox="0 0 35 35"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          x="1"
          y="1"
          width="33"
          height="33"
          rx="16.5"
          stroke="#64748B"
          stroke-width="2"
        />
        <path
          d="M20.5 11.5L14.5 17.5L20.5 23.5"
          stroke="#64748B"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <Typography
        variant="h4"
        align="center"
        gutterBottom
        sx={{ marginTop: "30px", fontWeight: 500 }}
      >
        Profile
      </Typography>
    </Box>
  );
};

export default ManageHeader;
