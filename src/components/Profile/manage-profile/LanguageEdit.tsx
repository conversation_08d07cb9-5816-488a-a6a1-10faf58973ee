import React from "react";
import InformationEditGroup from "./InformationEditGroup";
import {
  Box,
  FormControl,
  Grid,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import LanguageProficiencyManager from "@/components/LanguageProficiencyManager";
import { LEVEL_TYPES } from "@/constant/Enums";
import { getProficiencyEn } from "@/utils/common";
import useTranslate from "@/hooks/useTranslate";
import { getProficiencyId } from "@/utils/profile";

const InputContStyle = {
  display: "inline-flex",
  alignItems: "center",
  gap: 2,
  backgroundColor: "#fff",
  borderRadius: "16px",
  boxShadow: "0px 2px 14px rgba(0, 0, 0, 0.08)",
  padding: "4px 16px",
  width: "100%",
};

const SelectContStyle = {
  width: "100%",
  "& .MuiOutlinedInput-root": {
    backgroundColor: "transparent",
    borderRadius: "16px",
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none",
  },
};

const PlaceholderStyle = {
  color: "rgba(0, 0, 0, 0.87)",
  fontSize: "16px",
  fontWeight: 400,
  minWidth: "fit-content",
};

const LanguageEdit = ({
  studyLanguage,
  studyLanguageProficiency,
  setStudyLanguage,
  setStudyLanguageProficiency,
  selectionData,
  dbUser,
  languages,
  setLanguages,
  isStudentDashboard = false,
  level,
  setLevel,
}) => {
  const { translate } = useTranslate();

  const studyvalue = (() => {
    if (studyLanguage) {
      if (typeof studyLanguage === "string") {
        return studyLanguage;
      }
      if (studyLanguage && "_id" in studyLanguage) {
        return studyLanguage?._id;
      }
    }
    return null;
  })();

  return (
    <>
      <InformationEditGroup
        index="3"
        title={translate("kyc.study-language")}
        isPending={!studyLanguage || !studyLanguageProficiency}
      >
        <>
          <Grid container spacing={8}>
            <Grid item xs={12} sm={6}>
              <Box sx={InputContStyle}>
                <Typography variant="body1" sx={PlaceholderStyle}>
                  {translate("kyc.Language")}:
                </Typography>
                <FormControl sx={SelectContStyle}>
                  <Select
                    value={studyvalue}
                    onChange={(e) => setStudyLanguage(e.target.value)}
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      <Typography sx={{ color: `rgba(0, 0, 0, .38)` }}>
                        {translate("kyc.select-language")}
                      </Typography>
                    </MenuItem>
                    {selectionData.languages
                      .filter(({ code }) => RegExp("^(es|en)$").test(code))
                      .map((language) => {
                        return (
                          <MenuItem key={language._id} value={language._id}>
                            {language.name}
                          </MenuItem>
                        );
                      })}
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Box sx={InputContStyle}>
                <Typography variant="body1" sx={PlaceholderStyle}>
                  {translate(
                    isStudentDashboard
                      ? "mp.student-stated-level-only"
                      : "mp.prof-only"
                  )}
                  :
                </Typography>
                <FormControl sx={SelectContStyle}>
                  <Select
                    value={studyLanguageProficiency}
                    onChange={(e) =>
                      setStudyLanguageProficiency(e.target.value)
                    }
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      <Typography sx={{ color: `rgba(0, 0, 0, .38)` }}>
                        {translate(
                          isStudentDashboard
                            ? "mp.student-stated-level"
                            : "kyc.select-proficiency"
                        )}
                      </Typography>
                    </MenuItem>
                    {selectionData.proficiencies.map((prof) => {
                      return (
                        <MenuItem key={prof._id} value={prof._id}>
                          {translate(
                            getProficiencyId(
                              getProficiencyEn({
                                data: prof,
                              })
                            )
                          )}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </Box>
            </Grid>

            {isStudentDashboard && (
              <Grid item xs={12} sm={6}>
                <Box sx={InputContStyle}>
                  <Typography variant="body1" sx={PlaceholderStyle}>
                    {translate("mp.current-proficiency")}:
                  </Typography>
                  <FormControl sx={SelectContStyle}>
                    <Select
                      value={level}
                      onChange={(e) => setLevel(e.target.value)}
                      displayEmpty
                    >
                      <MenuItem value="" disabled>
                        <Typography sx={{ color: `rgba(0, 0, 0, .38)` }}>
                          {translate("mp.select-current-proficiency")}
                        </Typography>
                      </MenuItem>
                      <MenuItem value={LEVEL_TYPES.BEGINNER}>
                        {translate("mp.Beginner")}
                      </MenuItem>
                      <MenuItem value={LEVEL_TYPES.ADVANCED}>
                        {translate("mp.Advanced")}
                      </MenuItem>
                      <MenuItem value={LEVEL_TYPES.INTERMEDIATE}>
                        {translate("mp.Intermediate")}
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Grid>
            )}
          </Grid>
        </>
      </InformationEditGroup>

      <InformationEditGroup
        index="4"
        title={translate("mp.proficiency-level")}
        isPending={Array.isArray(dbUser?.langauges) && dbUser.langauges.length}
      >
        <LanguageProficiencyManager
          userId={dbUser?._id}
          value={languages}
          onChange={setLanguages}
        />
      </InformationEditGroup>
    </>
  );
};

export default LanguageEdit;
