import { Box, Typography, styled } from "@mui/material";
import React from "react";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import useTranslate from "@/hooks/useTranslate";

const PendingIcon = styled(Box)(({ theme }) => ({
  display: "inline-flex",
  alignItems: "center",
  gap: theme.spacing(1),
  color: "#CE9000",
  fontWeight: 500,
  padding: `5px 6px`,
  background: `#FFF2DA`,
  borderRadius: 4,
  "& .MuiSvgIcon-root": {
    fontSize: "20px",
  },
}));

const InformationEditGroup = ({
  children,
  index,
  title,
  isPending,
}: {
  children: React.ReactNode;
  index: String | Number;
  title: String;
  isPending?: Boolean;
}) => {
  const { translate } = useTranslate();
  return (
    <Box sx={{ mb: 9 }}>
      <Box sx={{ display: `flex`, alignItems: `center`, gap: 4, mb: 7 }}>
        <Typography
          variant="subtitle1"
          sx={{
            display: `inline-flex`,
            alignItems: `center`,
            justifyItems: `center`,
            border: `1px solid #000`,
            height: `28px`,
            width: `28px`,
            padding: `8px`,
            fontSize: `16px`,
            borderRadius: `50%`,
            fontWeight: 700,
          }}
        >
          {String(index)}
        </Typography>
        <Typography variant="h6" sx={{ fontWeight: 700 }}>
          {title}
        </Typography>
        {isPending ? (
          <PendingIcon>
            <WarningAmberIcon />{" "}
            <Typography variant="body2">
              {translate("mp.selection-pending")}
            </Typography>
          </PendingIcon>
        ) : null}
      </Box>
      {children}
    </Box>
  );
};

export default InformationEditGroup;
