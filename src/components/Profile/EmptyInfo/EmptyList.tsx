import React from "react";
import CalenderIcon from "./CalenderIcon";
import { Box, Button, Typography } from "@mui/material";
import { useRouter } from "next/router";
import CartIcon from "./CartIcon";
import useTranslate from "@/hooks/useTranslate";

type EmptyListProps = React.FC<{
  isTransactions?: boolean;
  isSuggestions?: boolean;

  isSmall?: boolean;
  isPurchase?: boolean;
  showButton?: boolean;
  title: string;
  description: string;
}>;
const EmptyList: EmptyListProps = ({
  isSmall = false,
  title,
  description,
  showButton = false,
  isPurchase = false,
}) => {
  const router = useRouter();
  const { translate } = useTranslate();

  return (
    <Box
      height="100%"
      width="100%"
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      sx={{ minHeight: isSmall ? 200 : 500 }}
    >
      {isPurchase ? (
        <CartIcon size={isSmall ? 70 : 150} />
      ) : (
        <CalenderIcon size={isSmall ? 70 : 150} />
      )}

      <Typography fontSize={isSmall ? 16 : 18} fontWeight={600} mt={6}>
        {title}
      </Typography>
      <Typography
        mt={2}
        color="rgba(109, 109, 109, 1)"
        fontSize={isSmall ? 14 : 16}
        textAlign="center"
      >
        {description}
      </Typography>

      {showButton && (
        <Button
          onClick={() => {
            router.push("/classes");
          }}
          sx={{
            mt: 5,
            background: "rgba(20, 167, 156, 1)",
            width: "60%",
            maxWidth: 250,
            fontSize: 14,
          }}
        >
          {translate("dash.viewplans")}
        </Button>
      )}
    </Box>
  );
};

export default EmptyList;
