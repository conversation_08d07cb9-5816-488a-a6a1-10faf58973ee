import React from 'react';
import {
  Box,
  Container,
  Typography,
  Skeleton,
  Paper,
  styled,
} from '@mui/material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  marginTop: theme.spacing(4),
  borderRadius: '16px',
  boxShadow: 'none',
  border: '1px solid #E0E0E0',
}));

const StepIndicator = styled(Box)(({ theme }) => ({
  width: '24px',
  height: '24px',
  borderRadius: '50%',
  backgroundColor: theme.palette.grey[200],
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));

const ProfileSkeleton = () => {
  return (
    <Container maxWidth="md">
      <Box sx={{ mt: 10 }}>
        <StyledPaper>
          {/* Title */}
          <Typography variant="h4" align="center" gutterBottom>
            <Skeleton width={300} height={40} sx={{ mx: 'auto' }} />
          </Typography>

          {/* Content */}
          <Box sx={{ my: 4 }}>
            <Skeleton width="80%" height={24} sx={{ mx: 'auto', mb: 2 }} />
            <Skeleton width="60%" height={24} sx={{ mx: 'auto', mb: 4 }} />
            
            {/* Main Content Area */}
            <Box sx={{ minHeight: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Skeleton variant="rectangular" width="80%" height={200} />
            </Box>
          </Box>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Skeleton width={120} height={40} />
            <Skeleton width={120} height={40} />
          </Box>
        </StyledPaper>

        {/* Step Indicators */}
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 5, mt: 4 }}>
          {[1, 2, 3, 4, 5, 6].map((label) => (
            <StepIndicator key={label} />
          ))}
        </Box>
      </Box>
    </Container>
  );
};

export default ProfileSkeleton; 