import { formatDate } from "@/utils/dateTime";
import { Box, Typography } from "@mui/material";
import React from "react";

type PurchasedOnProps = React.FC<{
  date: Date;
  text?: string;
}>;
const PurchasedOn: PurchasedOnProps = ({ date, text = "Purchased on" }) => {
  return (
    <Box display="flex" flexDirection="row">
      <Typography fontWeight={700} fontSize={12}>
        {text}{" "}
      </Typography>
      &nbsp;
      <Typography fontSize={12} color="rgba(100, 116, 139, 1)">
        {formatDate({
          date: new Date(date),
          returnWeekday: true,
        })}
        ,
        {formatDate({
          date: new Date(date),
        })}
      </Typography>
    </Box>
  );
};

export default PurchasedOn;
