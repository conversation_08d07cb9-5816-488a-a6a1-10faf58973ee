import React, { useEffect, useMemo, useState } from "react";
import {
  Box,
  Button,
  Typography,
  Modal,
  Select,
  MenuItem,
} from "@mui/material";
import PurchasedOn from "./PurchasesOn";
import ClassTag from "../ClassCard/ClassTag";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import axiosInstance from "@/utils/interceptor";
import { CartType, LanguageType } from "@/api/mongoTypes";
import { useSnackbar } from "@/hooks/useSnackbar";
import { useUserContext } from "@/contexts/UserContext";
import {
  langDataType,
  langugaeStateTypes,
  purchaseDetailsType,
  SingleLanguageType,
} from "@/types";
import { getSubTypeName, getTypeName } from "@/utils/format";
import { DURATION_TYPE } from "@/constant/Enums";
import StartEndDateSelector from "@/components/CheckoutModal/CheckoutClass/StartEndDateSelector";
import {
  formatDate,
  getDateAsPerUTC,
  getEndOfDayUTC,
  getWeekOptions,
} from "@/utils/dateTime";
import { addDays, format } from "date-fns";
import AddMoreButton from "../CompleteProfile/LanguageSelection/AddMoreButton";
import SelectionRow from "../CompleteProfile/LanguageSelection/SelectionRow";
import { SelectStyle } from "../CompleteProfile/LanguageSelection/style";
import {
  getMondayAndThursday,
  getTypedClassPricingInfo,
} from "@/utils/classes";

const commonSelectStyle = {
  fontSize: {
    xs: "12px",
    md: "14px",
  },
  height: {
    xs: "35px",
    md: "40px",
  },
  fontWeight: 500,
  boxShadow: "2px 2px 4px 4px rgba(0, 0, 0, 0.1)",
  borderRadius: {
    xs: 2,
  },
};

type ActivateModalProps = React.FC<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  data: purchaseDetailsType;
  purchasesData: purchaseDetailsType;
  index: number;
  setPurchasesData: React.Dispatch<React.SetStateAction<purchaseDetailsType[]>>;
}>;

const ActivateModal: ActivateModalProps = ({
  open,
  setOpen,
  data,
  setPurchasesData,
  index,
  purchasesData,
}) => {
  const handleClose = () => setOpen(false);
  const getFirstWeek = getWeekOptions({
    showStartEndDate: false,
  });
  const [startDate, setStartDate] = useState(dayjs(getFirstWeek[0].startDate));
  const [endDate, setEndDate] = useState(dayjs(getFirstWeek[0].endDate));
  const [isUpdating, setIsUpdating] = useState(false);
  const { showSnackbar } = useSnackbar();
  const { dbUser, setUserInfo } = useUserContext();

  const [isLoading, setIsLoading] = useState(false);
  const [langData, setLangData] = useState<langDataType>(null);
  const [isError, setIsError] = useState(false);
  const [languages, setLanguages] = useState<langugaeStateTypes>([]);
  const [interestedLang, setInterestedLang] = useState("");

  const classInfo = useMemo(() => {
    return getTypedClassPricingInfo(data);
  }, [data]);

  const showAdditionalOptions =
    dbUser.languages.length === 0 || !dbUser?.languageOfInterest;

  console.log("ActivateModal-data", data);

  console.log({
    purchasesData,
    data,
  });

  const transactionDate = useMemo(() => {
    if (!data) {
      return null;
    }
    if (data && "transactionDate" in data) {
      return new Date(data.transactionDate);
    }
    return null;
  }, [data]);

  const handleUpdate = async () => {
    if (langData) {
      if (!interestedLang) {
        showSnackbar("Please select interested language", {
          type: "warning",
        });
        return;
      }
      if (!languages) {
        showSnackbar("Please select your proficiency level", {
          type: "warning",
        });
        return;
      }
    }

    try {
      setIsUpdating(true);
      const payload = {
        // _id: data._id,
        transactionId: data?._id,
        currentPlan: data.classesDetails.plans,
        updatedPlan: {
          ...data.classesDetails.plans,
          // startDate,
          // endDate,
          startDate: getDateAsPerUTC(dayjs(startDate).toDate(), true),
          endDate: getEndOfDayUTC(dayjs(endDate).toDate(), true),
          isDateEnabled: true,
        },
        // plans: data.plans.map((m) => {
        //   if (m.planId === data.currentPlan.planId) {
        //     return {
        //       ...m,
        //       startDate,
        //       endDate,
        //       isDateEnabled: true,
        //     };
        //   } else {
        //     return m;
        //   }
        // }),
      };

      if (langData) {
        const updatedUser = await axiosInstance.post(`user/update`, {
          languageOfInterest: interestedLang,
          id: dbUser?._id,
          languages: languages.map((m) => [
            {
              languageId: m.language._id,
              languageProficiencyId: m.proficiency._id,
            },
          ]),
        });
        const isSucceeded = updatedUser.status === 200;
        if (updatedUser?.data?.data) {
          setUserInfo(updatedUser?.data?.data);
        }
        if (!isSucceeded) {
          showSnackbar("Failed to activate the plan.Please try again", {
            type: "error",
          });
          setIsUpdating(false);
          return;
        }
      }

      const { data: respData } = await axiosInstance.post(
        "payment/activate-plan",
        payload
      );
      // const { data: respData } = await axiosInstance.put(
      //   "cart/update",
      //   payload
      // );
      if (respData.success) {
        showSnackbar("Activated the plan successfully", {
          type: "success",
        });
        setPurchasesData((prev) => {
          const newList = prev.map((m, k) => {
            if (m._id === data._id && k === index) {
              return {
                ...m,
                classesDetails: {
                  ...data.classesDetails,
                  plans: payload.updatedPlan,
                },
              };
            } else {
              return m;
            }
          });
          return newList as purchaseDetailsType[];
        });
        handleClose();
      } else {
        showSnackbar("Failed to activate the plan", {
          type: "error",
        });
      }
      setIsUpdating(false);
    } catch (error) {
      console.error(error);
      setIsUpdating(false);
    }
  };

  const fetchLanguagesAndProdiciencies = async () => {
    try {
      if (dbUser) {
        setIsLoading(true);
        setIsError(false);
        const { data } = await axiosInstance.get(`/getLanguages`);
        if (data.success) {
          setLangData(data);
          setLanguages([
            {
              language: data.languages[0],
              proficiency: data.languageProficiencies[0],
            },
          ]);
        } else {
          setIsError(true);
        }
        setIsLoading(false);
      }
    } catch (error) {
      setIsLoading(false);
      console.error(
        "Something went wrong in fetchLanguagesAndProdiciencies in error",
        error
      );
    }
  };

  useEffect(() => {
    if (showAdditionalOptions) {
      fetchLanguagesAndProdiciencies();
    }
  }, [showAdditionalOptions]);

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            xsm: 400,
            sm: 600,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 3,
          p: 4,
        }}
        aria-disabled={isLoading}
      >
        <Box
          display="flex"
          flexDirection="column"
          width="100%"
          gap={2}
          p={1}
          aria-disabled={isUpdating}
        >
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
          >
            <Typography fontWeight={800} fontSize={18}>
              {classInfo.title}
            </Typography>
            <Box onClick={handleClose} style={{ cursor: "pointer" }}>
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M7.5 17.385L12 12.885L16.5 17.385L17.385 16.5L12.885 12L17.385 7.5L16.5 6.615L12 11.115L7.5 6.615L6.615 7.5L11.115 12L6.615 16.5L7.5 17.385ZM12.0037 23.25C10.4488 23.25 8.98625 22.955 7.61625 22.365C6.24708 21.7742 5.05583 20.9725 4.0425 19.96C3.02917 18.9475 2.22708 17.7575 1.63625 16.39C1.04542 15.0225 0.75 13.5604 0.75 12.0037C0.75 10.4471 1.04542 8.98458 1.63625 7.61625C2.22625 6.24708 3.02667 5.05583 4.0375 4.0425C5.04833 3.02917 6.23875 2.22708 7.60875 1.63625C8.97875 1.04542 10.4412 0.75 11.9963 0.75C13.5512 0.75 15.0138 1.04542 16.3837 1.63625C17.7529 2.22625 18.9442 3.02708 19.9575 4.03875C20.9708 5.05042 21.7729 6.24083 22.3638 7.61C22.9546 8.97917 23.25 10.4412 23.25 11.9963C23.25 13.5512 22.955 15.0138 22.365 16.3837C21.775 17.7537 20.9733 18.945 19.96 19.9575C18.9467 20.97 17.7567 21.7721 16.39 22.3638C15.0233 22.9554 13.5613 23.2508 12.0037 23.25ZM12 22C14.7917 22 17.1562 21.0312 19.0938 19.0938C21.0312 17.1562 22 14.7917 22 12C22 9.20833 21.0312 6.84375 19.0938 4.90625C17.1562 2.96875 14.7917 2 12 2C9.20833 2 6.84375 2.96875 4.90625 4.90625C2.96875 6.84375 2 9.20833 2 12C2 14.7917 2.96875 17.1562 4.90625 19.0938C6.84375 21.0312 9.20833 22 12 22Z"
                  fill="black"
                />
              </svg>
            </Box>
          </Box>
          <ClassTag
            textOne={`${getTypeName(classInfo.type)} :`}
            textTwo={getSubTypeName(classInfo.subType)}
          />

          <PurchasedOn date={transactionDate} />

          <Box>
            <Typography fontSize={14} fontWeight={700}>
              Select a start date <span style={{ color: "red" }}>*</span>
            </Typography>

            {classInfo.durationType === DURATION_TYPE.WEEK ? (
              <StartEndDateSelector
                startDate={startDate}
                endDate={endDate}
                onSelect={(val) => {
                  const startDate = val.startDate;
                  const endDate = val.endDate;
                  // const convertedStartDate = dayjs(startDate);
                  // const convertedEndDate = dayjs(endDate);
                  const convertedStartDateISO = startDate.toISOString();
                  const convertedEndDateISO = endDate.toISOString();
                  setStartDate(convertedStartDateISO);
                  setEndDate(convertedEndDateISO);
                }}
              />
            ) : (
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DatePicker
                  sx={{
                    width: "100%",
                    fontSize: "0.75rem", // Overall component font size
                    p: 0, // Sets padding to 0
                    "& .MuiInputBase-root": {
                      fontSize: "13px", // Input field font size
                    },
                    "& .MuiSvgIcon-root": {
                      fontSize: "16px", // Reduces the calendar icon size
                    },
                  }}
                  value={dayjs(startDate)}
                  disablePast={true}
                  onChange={(newValue) => {
                    const convertedDate = dayjs(newValue);
                    const end = addDays(convertedDate.toDate(), 3);
                    setStartDate(convertedDate);
                    setEndDate(dayjs(end) as any);
                  }}
                />
              </LocalizationProvider>
            )}

            {endDate && (
              <Typography color="#6D6D6D" textAlign="start" fontSize={12}>
                Your plan will end on &nbsp;
                <span style={{ fontWeight: 700 }}>
                  {/* {formatDate({
                    date: new Date(String(endDate)),
                    returnWeekday: true,
                  })}
                  ,
                  {formatDate({
                    date: new Date(String(endDate)),
                  })} */}

                  {format(dayjs(endDate).toDate(), "MMM dd, yyyy")}
                </span>
              </Typography>
            )}
          </Box>

          {showAdditionalOptions && (
            <>
              {dbUser.languages.length === 0 && (
                <Box>
                  <Typography fontSize={14} fontWeight={700}>
                    Select your Proficiency level
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                  {languages.map((m, i) => {
                    return (
                      <SelectionRow
                        key={i}
                        index={i}
                        languages={languages}
                        setLanguages={setLanguages}
                        languagesList={langData?.languages ?? []}
                        proficiencyList={langData?.languageProficiencies ?? []}
                        isTransparentBorder
                        deleteStyle={{
                          right: {
                            xs: 0,
                            md: 10,
                          },
                        }}
                        languageStyle={commonSelectStyle}
                        proficiencyStyle={commonSelectStyle}
                        selectContainerStyle={{
                          width: {
                            xs: "75%",
                            sm: "41.666667%",
                          },
                        }}
                      />
                    );
                  })}
                  <AddMoreButton
                    onClick={() => {
                      setLanguages((prev) => [
                        ...prev,
                        {
                          language: (() => {
                            return langData?.languages?.find(
                              (l: LanguageType) => {
                                if (
                                  languages?.every(
                                    (s: SingleLanguageType) =>
                                      s?.language._id !== l?._id
                                  )
                                ) {
                                  return l;
                                }
                              }
                            );
                          })(),
                          proficiency: langData.languageProficiencies[0],
                        },
                      ]);
                    }}
                  />
                </Box>
              )}

              {!dbUser?.languageOfInterest && (
                <Box>
                  <Typography fontSize={14} fontWeight={700}>
                    Which language do you want to study?
                    <span style={{ color: "red" }}>*</span>
                  </Typography>
                  <Select
                    value={interestedLang}
                    onChange={(event) => {
                      setInterestedLang(event.target.value);
                    }}
                    displayEmpty
                    sx={{ ...SelectStyle, marginTop: "0.2rem" }}
                  >
                    <MenuItem value="" disabled>
                      <Typography sx={{ color: `rgba(0, 0, 0, 0.38)` }}>
                        Select Language
                      </Typography>
                    </MenuItem>
                    {langData?.languages
                      .filter(({ code }) => RegExp("^(es|en)$").test(code))
                      .map((lang) => (
                        <MenuItem key={lang._id} value={lang._id}>
                          {lang.name}
                        </MenuItem>
                      ))}
                  </Select>
                </Box>
              )}
            </>
          )}

          <Box
            width="100%"
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            mt={4}
          >
            <Button
              onClick={handleUpdate}
              sx={{
                width: "80%",
                fontSize: "0.7rem",
                height: 40,
                background: "rgba(20, 167, 156, 1)",
              }}
            >
              Activate Plan
            </Button>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default ActivateModal;
