import {
  CartType,
  ClassesPricingType,
  ClubType,
  EventSchemaType,
} from "@/api/mongoTypes";
import { getPricingTitle } from "@/utils/format";
import { Box, Typography } from "@mui/material";
import React, { useMemo } from "react";
import Status from "./Status";
import { purchaseDetailsType } from "@/types";
import { getTypedClassPricingInfo } from "@/utils/classes";

type PlanDetailsProps = React.FC<{
  purchaseDetails: purchaseDetailsType;
}>;
const PlanDetails: PlanDetailsProps = ({ purchaseDetails }) => {
  const planTitle = useMemo(() => {
    if (purchaseDetails) {
      const classesDetails = getTypedClassPricingInfo(purchaseDetails);
      if (classesDetails && classesDetails?.plans?.length > 0) {
        return getPricingTitle({
          data: classesDetails,
        });
      }
    }
    return "";
  }, [purchaseDetails]);

  // const { isBooked, isActive } = useMemo(() => {
  //   const defaultValues = {
  //     isBooked: false,
  //     isActive: false,
  //   };
  //   if (purchaseDetails) {
  //     const clubDetails = purchaseDetails.clubId as ClubType;
  //     const eventDetails = purchaseDetails.eventId as EventSchemaType;
  //     const classesDetails = purchaseDetails.classesId as ClassesPricingType;

  //     if (eventDetails?.title) {
  //       defaultValues.isActive = true;
  //       return defaultValues;
  //     }
  //     if (clubDetails?.title) {
  //       defaultValues.isBooked = true;
  //       return defaultValues;
  //     }
  //     return defaultValues;
  //   } else {
  //     return defaultValues;
  //   }
  // }, [purchaseDetails]);

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      mt={2}
      mb={2}
    >
      <Typography fontWeight={700} fontSize={14} mb={0}>
        {planTitle ?? <>&nbsp;</>}
      </Typography>
      {/* <Status isBooked={isBooked} isActive={isActive} /> */}
    </Box>
  );
};

export default PlanDetails;
