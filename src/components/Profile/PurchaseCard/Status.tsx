import { Box } from "@mui/material";
import React, { useMemo } from "react";

type StatusProps = React.FC<{
  isBooked?: boolean;
  isActive?: boolean;
}>;
const Status: StatusProps = ({ isBooked, isActive }) => {
  const { color, bgColor, text } = useMemo(() => {
    if (isBooked) {
      return {
        color: "rgba(229, 160, 0, 1)",
        bgColor: "rgba(255, 232, 163, 1)",
        text: "Booked",
      };
    }
    if (isActive) {
      return {
        color: "rgba(20, 174, 92, 1)",
        bgColor: "rgba(207, 247, 211, 1)",
        text: "Activated",
      };
    }
    return {
      color: "rgba(109, 109, 109, 1)",
      bgColor: "rgba(217, 217, 217, 1)",
      text: "Not Active",
    };
  }, [isBooked, isActive]);

  return (
    <Box
      sx={{
        color: color,
        background: bgColor,
        width: "max-content",
        borderRadius: 5,
        padding: "2px 5px",
        fontSize: 10,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
      }}
    >
      &nbsp;
      <span
        style={{
          height: 8,
          width: 8,
          borderRadius: 4,
          background: color,
          display: "flex",
        }}
      />
      &nbsp;
      <b>{text}</b>
    </Box>
  );
};

export default Status;
