import {
  CartType,
  ClassesPricingType,
  ClubType,
  EventSchemaType,
} from "@/api/mongoTypes";
import { CLASSES_TYPE } from "@/constant/Enums";
import { purchaseDetailsType } from "@/types";
import { getClassesImage } from "@/utils/classes";
import { Box } from "@mui/material";
import Image from "next/image";
import React, { useMemo } from "react";

type CardImageProps = React.FC<{
  purchaseDetails: purchaseDetailsType;
}>;
const CardImage: CardImageProps = ({ purchaseDetails }) => {
  const imgSrc = useMemo(() => {
    return getClassesImage({ purchaseDetails });
  }, [purchaseDetails]);

  if (imgSrc) {
    return (
      <Image
        height={40}
        width={40}
        style={{
          overflow: "hidden",
          borderRadius: 5,
        }}
        alt="class"
        src={imgSrc}
      />
    );
  } else {
    return (
      <Box
        sx={{
          height: 40,
          width: 40,
          overflow: "hidden",
          borderRadius: "5px",
          background: "gray",
        }}
      />
    );
  }
};

export default CardImage;
