import { CartType } from "@/api/mongoTypes";
import {
  dateAsPerTimeZone,
  formatDate,
  getDateAsPerUTC,
} from "@/utils/dateTime";
import { Box, Button, Typography } from "@mui/material";
import React, { useMemo, useState } from "react";
import ActivateModal from "./ActivateModal";
import {
  getStartAndEndDateOfClasses,
  getTypedEventInfo,
} from "@/utils/classes";
import { purchaseDetailsType } from "@/types";
import { format } from "date-fns";
import dayjs from "dayjs";

type StartEndProps = React.FC<{
  purchaseDetails: purchaseDetailsType;
  isForSomeone: boolean;
  showActivateClassButton: boolean;
  setPurchasesData: React.Dispatch<React.SetStateAction<purchaseDetailsType[]>>;
  index: number;
}>;
const StartEnd: StartEndProps = ({
  purchaseDetails,
  isForSomeone,
  showActivateClassButton,
  setPurchasesData,
  index,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const isEvent = !!getTypedEventInfo(purchaseDetails);

  const { startDate, endDate, timezone } = useMemo(() => {
    return getStartAndEndDateOfClasses({ cartData: purchaseDetails });
  }, [purchaseDetails]);

  return (
    <>
      <Box>
        {showActivateClassButton && !isForSomeone && (
          <Button
            onClick={() => {
              setIsOpen(true);
            }}
            sx={{
              width: "100%",
              fontSize: "0.8rem",
              background: "rgba(20, 167, 156, 1)",
            }}
          >
            Activate Class
          </Button>
        )}
        {startDate && endDate && (
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{
              borderRadius: 2,
              background: "rgba(248, 248, 248, 1)",
              p: 2,
            }}
          >
            {startDate && (
              <SingleStartEnd
                timezone={timezone}
                title="Start :"
                isEvent={isEvent}
                date={startDate}
              />
            )}
            {endDate && (
              <SingleStartEnd
                timezone={timezone}
                title="End :"
                isEvent={isEvent}
                date={endDate}
              />
            )}
          </Box>
        )}
      </Box>
      {isOpen && (
        <ActivateModal
          data={purchaseDetails}
          purchasesData={purchaseDetails}
          open={isOpen}
          index={index}
          setOpen={setIsOpen}
          setPurchasesData={setPurchasesData}
        />
      )}
    </>
  );
};

type SingleStartEndProps = React.FC<{
  title: string;
  timezone: string;
  date: Date;
  isEvent: Boolean;
}>;
const SingleStartEnd: SingleStartEndProps = ({
  title,
  date,
  timezone = null,
  isEvent,
}) => {
  const { weekDay, dayDate } = useMemo(() => {
    if (isEvent && timezone) {
      const dateAsPerEventTimezone = getDateAsPerUTC(
        dateAsPerTimeZone({
          dateToChange: date,
          timeZone: timezone,
        })
      );
      const dateAsPerUserTZ = getDateAsPerUTC(
        dateAsPerTimeZone({
          dateToChange: dateAsPerEventTimezone,
        })
      );

      const res = {
        dayDate: format(dayjs(dateAsPerUserTZ).toDate(), "MMM dd yyyy"),
        weekDay: dayjs(dateAsPerUserTZ).format("ddd"),
      };
      return res;
    } else {
      return {
        dayDate: format(dayjs(date).toDate(), "MMM dd yyyy"),
        weekDay: dayjs(date).format("ddd"),
      };
    }
  }, [isEvent, timezone, date]);

  return (
    <Box>
      <Typography fontWeight={700} fontSize={13} color="rgba(60, 60, 60, 1)">
        {title}
      </Typography>
      <Typography fontSize={12} color="rgba(109, 109, 109, 1)">
        {weekDay}, {dayDate}
      </Typography>
    </Box>
  );
};

export default StartEnd;
