import { formatDate } from "@/utils/dateTime";
import { Box, Typography } from "@mui/material";
import React from "react";

type PurchasedForProps = React.FC<{
  email: string;
}>;
const PurchasedFor: PurchasedForProps = ({ email }) => {
  return (
    <Box display="flex" flexDirection="row">
      <Typography fontWeight={700} fontSize={12}>
        Purchased for{" "}
      </Typography>
      &nbsp;
      <Typography fontSize={12} color="rgba(100, 116, 139, 1)">
        {email}
      </Typography>
    </Box>
  );
};

export default PurchasedFor;
