import { Box, Typography } from "@mui/material";
import Image from "next/image";
import React, { useMemo, useState } from "react";
import Locked from "./Locked";
import ClassTag from "./ClassTag";
import DateAndLevel from "./DateAndLevel";
import ClassCardLocation from "./ClassCardLocation";
import ClassCardModal from "./ClassCardModal";
import { CartType } from "@/api/mongoTypes";
import {
  getClassesImage,
  getClassesLocation,
  getClassStatus,
  getClassTitleAndSubtitle,
  getClassTypeAndSubType,
  getCreatorDetails,
  getProficiencyLevel,
} from "@/utils/classes";
import CreatorDetails from "@/components/classes/OnlineClub/CreatorDetails";
import ClassStatus from "../ClassStatus";
import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import { MaybeCartSchedule } from "@/types";
import { getProficiencyId } from "@/utils/profile";
import useTranslate from "@/hooks/useTranslate";

type ClassCardProps = React.FC<{
  isSuggestion?: boolean;
  isYourClasses?: boolean;
  isDashboard?: boolean;
  data: MaybeCartSchedule;
  active: CLASSESS_FETCH_DURATION;
  cardDate?: Date;
}>;
const ClassCard: ClassCardProps = ({
  isSuggestion,
  data,
  active,
  isYourClasses,
  isDashboard,
  cardDate,
}) => {
  const [open, setOpen] = useState(false);
  const { translate } = useTranslate();

  const imgSrc = useMemo(() => {
    return getClassesImage({ purchaseDetails: data });
  }, [data]);

  const { title } = useMemo(() => {
    return getClassTitleAndSubtitle({ cartData: data });
  }, [data]);

  const { type, subType } = useMemo(() => {
    return getClassTypeAndSubType({ cartData: data });
  }, [data]);

  const location = useMemo(() => {
    return getClassesLocation({ cartData: data });
  }, [data]);

  const creatorDetails = useMemo(() => {
    return getCreatorDetails({ cartData: data });
  }, [data]);

  const status = useMemo(() => {
    return getClassStatus({ data, selectedDate: null, active, cardDate });
  }, [data, active, cardDate]);

  const proficiency = useMemo(() => {
    return getProficiencyLevel({ data });
  }, [data]);

  return (
    <>
      <Box
        onClick={() => {
          setOpen(true);
        }}
        sx={{
          width: "100%",
          boxShadow: "0px 1px 10px 0px rgba(0, 0, 0, 0.1)",
          borderRadius: 2,
          mb: 2,
          p: 3,
          cursor: "pointer",
          height: "100%",
        }}
      >
        <Box
          sx={{
            height: 200,
            width: "100%",
            position: "relative",
            overflow: "hidden",
            borderRadius: 2,
          }}
        >
          <Image alt="class" src={imgSrc} fill />

          {proficiency && (
            <ProficiencyLevel
              level={translate(getProficiencyId(proficiency))}
            />
          )}
          {status && <ClassStatus status={status} />}
        </Box>
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          my={1}
        >
          <ClassTag textOne={`${type} ${subType && ":"}`} textTwo={subType} />
        </Box>
        <Typography fontSize={16} fontWeight={700}>
          {title}
        </Typography>
        {(isSuggestion || creatorDetails?.length > 0) && (
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            mb={1}
            justifyContent={
              creatorDetails?.length === 0 ? "end" : "space-between"
            }
          >
            {creatorDetails?.length > 0 && (
              <CreatorDetails mt={0} creatorDetails={creatorDetails} isSmall />
            )}
            {isSuggestion && (
              <Box
                sx={{
                  background: "#E2E2E2",
                  color: "#000",
                  fontSize: 11,
                  fontWeight: 600,
                  width: "fit-content",
                  px: 2,
                  py: 1,
                  borderRadius: 1,
                }}
              >
                Suggested
              </Box>
            )}
          </Box>
        )}

        {location && <ClassCardLocation location={location} fontSize={12} />}
        <DateAndLevel
          cardDate={cardDate}
          data={data}
          isDashboard={isDashboard}
          active={active}
        />
      </Box>
      {open && (
        <ClassCardModal
          isRestricted={isSuggestion}
          data={data}
          open={open}
          setOpen={setOpen}
          cardDate={cardDate}
        />
      )}
    </>
  );
};

const ProficiencyLevel = ({ level }) => {
  return (
    <Box
      sx={{
        color: "#fff",
        fontSize: 12,
        zIndex: 100,
        display: "flex",
        position: "absolute",
        top: 10,
        left: 10,
        px: 2,
        borderRadius: 10,
      }}
    >
      <Typography fontSize={12}>{level}</Typography>
    </Box>
  );
};

export default ClassCard;
