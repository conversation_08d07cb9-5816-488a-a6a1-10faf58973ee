import { Box, Skeleton } from "@mui/material";
import React from "react";

const ClassCardListLoading = () => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      flexWrap="wrap"
      mt={10}
    >
      {new Array(9).fill("").map((m, i) => (
        <ClassCardLoading key={i} />
      ))}
    </Box>
  );
};

export default ClassCardListLoading;

const ClassCardLoading = () => {
  return (
    <Box
      sx={{
        mb: 2,
        overflow: "hidden",
        width: "100%",
        p: 1,
      }}
    >
      <Box
        p={3}
        sx={{
          boxShadow: "0px 1px 10px 0px rgba(0, 0, 0, 0.1)",
          height: 160,
          borderRadius: 2,
        }}
      >
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          gap={2}
        >
          <Skeleton variant="rectangular" width="20%" height={80} />
          <Skeleton variant="rectangular" width="80%" height={80} />
        </Box>
        <Box display="flex" flexDirection="row" justifyContent="space-between">
          <Skeleton
            variant="rectangular"
            sx={{ marginTop: "0.5rem" }}
            width="40%"
            height={20}
          />
          <Skeleton
            variant="rectangular"
            sx={{ marginTop: "0.5rem" }}
            width="40%"
            height={20}
          />
        </Box>
        <Box
          display="flex"
          flexDirection="row"
          sx={{ marginTop: "0.5rem" }}
          alignItems="center"
          justifyContent="space-between"
        >
          <Skeleton variant="rectangular" width="100%" height={20} />
        </Box>
      </Box>
    </Box>
  );
};
