import { Box, Typography } from "@mui/material";
import React from "react";

const ClassCardLocation = ({
  color = "#14A79C",
  location = "",
  fontSize = 16,
}) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      sx={{ overflow: "hidden" }}
    >
      <svg
        width={fontSize + 2}
        height={fontSize + 2}
        viewBox="0 0 17 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.2812 6.90625C13.2812 5.63818 12.7775 4.42205 11.8809 3.5254C10.9842 2.62874 9.76807 2.125 8.5 2.125C7.23193 2.125 6.0158 2.62874 5.11915 3.5254C4.22249 4.42205 3.71875 5.63818 3.71875 6.90625C3.71875 8.86762 5.28806 11.424 8.5 14.4861C11.7119 11.424 13.2812 8.86762 13.2812 6.90625ZM8.5 15.9375C4.60381 12.3962 2.65625 9.38506 2.65625 6.90625C2.65625 5.35639 3.27193 3.87001 4.36784 2.77409C5.46376 1.67818 6.95014 1.0625 8.5 1.0625C10.0499 1.0625 11.5362 1.67818 12.6322 2.77409C13.7281 3.87001 14.3438 5.35639 14.3438 6.90625C14.3438 9.38506 12.3962 12.3962 8.5 15.9375Z"
          fill={color}
        />
        <path
          d="M8.5 8.5C8.92269 8.5 9.32807 8.33209 9.62695 8.0332C9.92584 7.73432 10.0938 7.32894 10.0938 6.90625C10.0938 6.48356 9.92584 6.07818 9.62695 5.7793C9.32807 5.48041 8.92269 5.3125 8.5 5.3125C8.07731 5.3125 7.67193 5.48041 7.37305 5.7793C7.07416 6.07818 6.90625 6.48356 6.90625 6.90625C6.90625 7.32894 7.07416 7.73432 7.37305 8.0332C7.67193 8.33209 8.07731 8.5 8.5 8.5ZM8.5 9.5625C7.79552 9.5625 7.11989 9.28265 6.62175 8.7845C6.1236 8.28636 5.84375 7.61073 5.84375 6.90625C5.84375 6.20177 6.1236 5.52614 6.62175 5.028C7.11989 4.52985 7.79552 4.25 8.5 4.25C9.20448 4.25 9.88011 4.52985 10.3783 5.028C10.8764 5.52614 11.1562 6.20177 11.1562 6.90625C11.1562 7.61073 10.8764 8.28636 10.3783 8.7845C9.88011 9.28265 9.20448 9.5625 8.5 9.5625Z"
          fill={color}
        />
      </svg>
      <Typography
        fontSize={fontSize}
        color={color}
        sx={{
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
          maxWidth: "100%",
        }}
      >
        {location ? location : "Online"}
      </Typography>
    </Box>
  );
};

export default ClassCardLocation;
