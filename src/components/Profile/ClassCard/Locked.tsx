import { Box } from "@mui/material";
import React from "react";

const Locked = () => {
  return (
    <Box
      sx={{
        height: 30,
        width: 30,
        borderRadius: 15,
        background: "rgba(20, 167, 156, 1)",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4 8V5C4 2.79 5.79 1 8 1C10.21 1 12 2.79 12 5V8M8 13C8.26522 13 8.51957 12.8946 8.70711 12.7071C8.89464 12.5196 9 12.2652 9 12C9 11.7348 8.89464 11.4804 8.70711 11.2929C8.51957 11.1054 8.26522 11 8 11C7.73478 11 7.48043 11.1054 7.29289 11.2929C7.10536 11.4804 7 11.7348 7 12C7 12.2652 7.10536 12.5196 7.29289 12.7071C7.48043 12.8946 7.73478 13 8 13ZM8 13V16M2.6 8H13.4C14.28 8 15 8.72 15 9.6V16.6C15 17.92 13.92 19 12.6 19H3.4C2.08 19 1 17.92 1 16.6V9.6C1 8.72 1.72 8 2.6 8Z"
          stroke="white"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </Box>
  );
};

export default Locked;
