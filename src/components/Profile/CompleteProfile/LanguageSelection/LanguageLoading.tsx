import React from "react";
import {
  BottonButtonContainer,
  ContainerStyle,
  SelectContStyle,
  SelectRowContainer,
  ShowAddMoreContainerStyle,
} from "./style";
import { Container, Skeleton } from "@mui/material";

const LanguageLoading = () => {
  return (
    <Container sx={ContainerStyle}>
      <Container
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          width: "100%",
          marginTop: {
            xs: "0.5rem",
            md: "4rem",
          },
          textAlign: "center",
          fontWeight: "600",
          fontSize: {
            xs: "1.5rem",
            md: "1.875rem",
          },
        }}
      >
        <Skeleton variant="rectangular" width="50%" height={50} />
      </Container>

      <Container
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          width: "100%",
          marginTop: "0.5rem",
        }}
      >
        <Skeleton variant="rectangular" width="70%" height={15} />
      </Container>
      <Container
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          width: "100%",
          marginTop: "0.25rem",
        }}
      >
        <Skeleton variant="rectangular" width="50%" height={15} />
      </Container>

      <Container sx={SelectRowContainer}>
        <Container sx={SelectContStyle}>
          <Skeleton variant="rectangular" width="100%" height={50} />
        </Container>
        <Container sx={SelectContStyle}>
          <Skeleton variant="rectangular" width="100%" height={50} />
        </Container>
      </Container>

      <Container sx={ShowAddMoreContainerStyle}>
        <Skeleton variant="rectangular" width="10%" height={40} />
      </Container>

      <Container sx={BottonButtonContainer}>
        <Container sx={SelectContStyle}>
          <Skeleton variant="rectangular" width="100%" height={50} />
        </Container>
        <Container sx={SelectContStyle}>
          <Skeleton variant="rectangular" width="100%" height={50} />
        </Container>
      </Container>
    </Container>
  );
};

export default LanguageLoading;
