import { LanguageProficiencyType, LanguageType } from "@/api/mongoTypes";
import React from "react";
import FormControl from "@mui/material/FormControl";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import { commonStyle } from "./style";
import MenuItem from "@mui/material/MenuItem";
import { getProficiencyEn } from "@/utils/common";
import useTranslate from "@/hooks/useTranslate";
import { getProficiencyId } from "@/utils/profile";

const SelectProficiency = ({
  proficiencyList,
  selectedLanguageProf,
  isTransparentBorder = false,
  selectStyle = {},
  onChange,
}) => {
  const { translate } = useTranslate();
  const value = selectedLanguageProf
    ? `${translate("kyc.Level")} :${translate(
        getProficiencyId(selectedLanguageProf)
      )}`
    : "";
  const border = isTransparentBorder
    ? "none"
    : "1px solid rgba(128, 175, 112, 1)";

  return (
    <>
      <FormControl
        variant="outlined"
        sx={{
          width: "100%",
        }}
      >
        <Select
          value={value}
          onChange={onChange}
          inputProps={{ "aria-label": "Without label" }}
          displayEmpty
          sx={{
            ...commonStyle,
            "&:hover .MuiOutlinedInput-notchedOutline": {
              border: selectedLanguageProf ? border : "none",
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
              border: selectedLanguageProf ? border : "none",
            },
            "& .MuiOutlinedInput-notchedOutline": {
              border: selectedLanguageProf ? border : "none",
            },
            ...selectStyle,
          }}
          renderValue={(selected) => {
            if (!selected) {
              return translate("kyc.select-proficiency");
            }
            return selected;
          }}
        >
          {proficiencyList.map((m: LanguageProficiencyType) => {
            const proficiency = getProficiencyEn({
              data: m,
            });
            if (proficiency) {
              return (
                <MenuItem key={m._id} value={m._id}>
                  {translate(getProficiencyId(proficiency))}
                </MenuItem>
              );
            }
          })}
        </Select>
      </FormControl>
    </>
  );
};

export default SelectProficiency;
