import { LanguageProficiencyType, LanguageType } from "@/api/mongoTypes";
import { useSnackbar } from "@/hooks/useSnackbar";
import {
  langDataType,
  langugaeStateTypes,
  SingleLanguageType,
  StudyingType,
} from "@/types";
import { Container, Divider, SxProps, Theme, Typography } from "@mui/material";
import axios from "axios";
import React, { useEffect, useMemo, useState } from "react";
import Box from "@mui/material/Box";
import CustomButton from "../../../CustomButton";
import AddMoreButton from "./AddMoreButton";
import {
  BottonButtonContainer,
  ContainerStyle,
  CustomButtonStyle,
  SelectContStyle,
  SelectRowContainer,
  ShowAddMoreContainerStyle,
} from "./style";
import SelectLanguage from "./SelectLanguage";
import LanguageLoading from "./LanguageLoading";
import SelectionRow from "./SelectionRow";
import SelectProficiency from "./SelectProficiency";
import { getProficiencyEn } from "@/utils/common";
import useTranslate from "@/hooks/useTranslate";

const headerStyle: SxProps<Theme> = {
  fontWeight: "600",
  textAlign: "center",
  marginTop: "6rem",
  fontSize: {
    xs: "1.5rem",
    sm: "1.75rem",
    md: "1.95rem",
  },
};

type LanguageSelectionProps = {
  languages: langugaeStateTypes;
  setLanguages: React.Dispatch<React.SetStateAction<langugaeStateTypes>>;
  stageIx: number;
  completeProfile: boolean;
  userId: string;
  setStageIx: React.Dispatch<React.SetStateAction<number>>;
  studying: StudyingType;
  setStudying: React.Dispatch<React.SetStateAction<StudyingType>>;
};

const LanguageSelection: React.FC<LanguageSelectionProps> = ({
  languages,
  setLanguages,
  stageIx,
  setStageIx,
  userId,
  completeProfile,
  studying,
  setStudying,
}) => {
  const { showSnackbar } = useSnackbar();
  const { translate } = useTranslate();
  const [isLoading, setIsloading] = useState(true);
  const [langData, setLangData] = useState<langDataType>(null);

  const showAddMoreButton = useMemo(() => {
    if (langData?.languages?.length === languages?.length) {
      return false;
    }
    return true;
  }, [langData, languages]);

  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        setIsloading(true);
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
        );
        if (data.isError) {
        } else {
          setLangData(data);

          const setThisLangs = () => {
            setLanguages([
              {
                language: data.languages[0],
                proficiency: data.languageProficiencies[0],
              },
            ]);
          };
          const { data: languagesData } = await axios.get(
            `${process.env.NEXT_PUBLIC_BASE_URL}api/user/language/${userId}`
          );
          if (languagesData.data) {
            const newList = languagesData.data
              .filter((f) => f.language && f.proficiency && f._id)
              .map((m) => ({
                language: m.language,
                proficiency: m.proficiency,
              }));
            if (newList.length > 0) {
              setLanguages(newList);
            } else {
              setThisLangs();
            }
          } else {
            setThisLangs();
          }
        }
        setIsloading(false);
      } catch (error) {
        setIsloading(false);
        console.error("Something went wrong due to", error);
        showSnackbar(translate("kyc.failed-fetch"), {
          type: "error",
        });
      }
    };
    fetchLanguages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, completeProfile]);

  const handleContinue = () => {
    if (!studying?.language || studying.language.length === 0) {
      showSnackbar(translate("kyc.select-study-lang"), {
        type: "warning",
      });
    } else if (!studying?.proficiency || studying?.proficiency?.length === 0) {
      showSnackbar(translate("kyc.select-study-lang-propf"), {
        type: "warning",
      });
    } else if (languages.length === 0) {
      showSnackbar(translate("kyc.select-one-lang"), {
        type: "warning",
      });
    } else {
      setStageIx(+stageIx + 1);
    }
  };

  const handleDoItLater = () => {
    setLanguages([]);
    setStageIx(+stageIx + 1);
  };

  if (isLoading) {
    return <LanguageLoading />;
  }

  return (
    <Container sx={ContainerStyle}>
      <Typography variant="h2" gutterBottom sx={headerStyle}>
        {translate("kyc.study-language")}
      </Typography>

      <Box sx={SelectRowContainer}>
        <Box sx={SelectContStyle}>
          <SelectLanguage
            languagesList={langData?.languages?.filter(({ code }) =>
              RegExp("^(es|en)$").test(code)
            )}
            // languagesList={langData?.languages}
            selectedLanguage={
              langData?.languages?.find(
                (f: LanguageType) => f._id === studying.language
              )?.name
            }
            // selectedLanguage={studying.language}
            onChange={(e) => {
              const selectedId = e.target.value;
              setStudying((prev) => ({
                ...prev,
                language: selectedId,
              }));
            }}
          />
        </Box>
        <Box sx={SelectContStyle}>
          <SelectProficiency
            proficiencyList={langData?.languageProficiencies}
            selectedLanguageProf={getProficiencyEn({
              data: langData?.languageProficiencies?.find(
                (f: LanguageProficiencyType) => f._id === studying?.proficiency
              ),
            })}
            onChange={(e) => {
              const selectedId = e.target.value;
              const findProficiency = langData?.languageProficiencies?.find(
                (f: LanguageProficiencyType) => f._id === selectedId
              );
              setStudying((prev) => ({
                ...prev,
                proficiency: findProficiency._id,
              }));
            }}
          />
        </Box>
      </Box>

      <Divider sx={{ borderTop: "1px solid #D4D4D4", width: "90%", my: 15 }} />

      <Typography variant="h2" gutterBottom sx={headerStyle}>
        {translate("kyc.additional-languages")}
      </Typography>
      <Container
        sx={{
          marginBottom: "1rem",
          marginTop: "0px",
          width: {
            xs: "66.666667%",
            sm: "83.333333%",
            md: "66.666667%",
          },
        }}
      >
        <Typography
          variant="h5"
          component="div"
          sx={{
            textAlign: "center",
            fontWeight: "600",
            fontSize: {
              xs: "11px",
              sm: "14px",
              md: "16px",
            },
          }}
        >
          {translate("kyc.language-journey")}
        </Typography>
      </Container>

      {languages.map((m, i) => {
        return (
          <SelectionRow
            key={i} // Add a unique key prop
            index={i}
            languages={languages}
            setLanguages={setLanguages}
            languagesList={langData?.languages ?? []}
            proficiencyList={langData?.languageProficiencies ?? []}
          />
        );
      })}

      {showAddMoreButton && (
        <Container sx={ShowAddMoreContainerStyle}>
          <AddMoreButton
            onClick={() => {
              setLanguages((prev) => [
                ...prev,
                {
                  language: (() => {
                    return langData?.languages?.find((l: LanguageType) => {
                      if (
                        languages?.every(
                          (s: SingleLanguageType) => s?.language._id !== l?._id
                        )
                      ) {
                        return l;
                      }
                    });
                  })(),
                  proficiency: langData.languageProficiencies[0],
                },
              ]);
            }}
          />
        </Container>
      )}

      <Container sx={BottonButtonContainer}>
        <CustomButton
          text={translate("common.do-later")}
          onClick={handleDoItLater}
          colortype="secondary"
          sx={CustomButtonStyle}
        />
        <CustomButton
          text={translate("common.continue")}
          sx={CustomButtonStyle}
          onClick={handleContinue}
        />
      </Container>
    </Container>
  );
};

export default LanguageSelection;
