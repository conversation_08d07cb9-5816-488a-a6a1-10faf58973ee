import useTranslate from "@/hooks/useTranslate";
import { Box, SxProps, Theme, Typography } from "@mui/material";
import React from "react";

type tabType = {
  id: number;
  name: string;
};

type PastCurrentUpcomingProps = React.FC<{
  active: number;
  setActive: React.Dispatch<React.SetStateAction<number>>;
  showAll?: boolean;
  isSuggestions?: boolean;
  style?: SxProps<Theme>;
  tabs: tabType[];
}>;
const PastCurrentUpcoming: PastCurrentUpcomingProps = ({
  active,
  setActive,
  showAll = false,
  isSuggestions = false,
  style = {},
  tabs,
}) => {
  const num = showAll ? 4 : 3;

  const list = isSuggestions ? tabs.filter((f) => f.id !== 1) : tabs;

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyItems: "center",
        gap: 2,
        background: "rgba(244, 244, 245, 1)",
        p: 1,
        ...style,
      }}
    >
      {list.slice(0, num).map((m, i) => (
        <Tab
          key={i}
          data={m}
          active={active === m.id}
          onClick={() => {
            setActive(m.id);
          }}
        />
      ))}
    </Box>
  );
};

type TabProps = React.FC<{
  data: tabType;
  active: boolean;
  onClick: () => void;
}>;
const Tab: TabProps = ({ data, active, onClick }) => {
  const { translate } = useTranslate();
  return (
    <Box
      key={data.id}
      sx={{
        px: 2,
        py: 1,
        background: active ? "rgba(255, 255, 255, 1)" : "",
        borderRadius: 1,
        color: "rgba(109, 109, 109, 1)",
        cursor: "pointer",
      }}
      onClick={onClick}
    >
      <Typography fontSize={13} fontWeight={600}>
        {translate(data.name)}
      </Typography>
    </Box>
  );
};

export default PastCurrentUpcoming;
