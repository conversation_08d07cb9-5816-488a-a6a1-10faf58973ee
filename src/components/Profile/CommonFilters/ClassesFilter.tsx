import React, { useState } from "react";
import {
  Button,
  Menu,
  MenuItem,
  Checkbox,
  Typography,
  Box,
} from "@mui/material";

const ClassesFilter = ({
  classesSelected,
  setClassesSelected,
  filtersTabs,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleCheckboxChange = (name) => {
    if (classesSelected.includes(name)) {
      setClassesSelected(classesSelected.filter((item) => item !== name));
    } else {
      setClassesSelected([...classesSelected, name]);
    }
  };

  return (
    <div>
      <Button
        onClick={handleClick}
        sx={{
          padding: "8px 12px",
          textTransform: "none",
          border: "1px solid rgba(0, 0, 0, 0.23)",
          background: "transparent",
          width: "max-content",
          color: "#000",
          "&:hover": {
            background: "transparent",
          },
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <FilterIcon />
          <Typography sx={{ fontSize: 12 }}>
            {classesSelected.length > 1
              ? `${classesSelected.length} selected`
              : classesSelected.length > 0
              ? classesSelected.join(", ")
              : "Filter"}
          </Typography>
        </Box>
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        {filtersTabs.map((className) => (
          <MenuItem
            key={className.id}
            sx={{ padding: "0 8px" }}
            onClick={() => handleCheckboxChange(className.value)}
          >
            <Checkbox
              size="small"
              checked={classesSelected.includes(className.value)}
            />
            <Typography sx={{ fontSize: 12 }}>{className.name}</Typography>
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
};

export const FilterIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 21 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.95833 6.72449H16.2917C16.4795 6.72449 16.6597 6.79912 16.7925 6.93195C16.9254 7.06479 17 7.24496 17 7.43282V8.55624C17 8.74408 16.9253 8.92422 16.7925 9.05703L12.2492 13.6003C12.1164 13.7331 12.0417 13.9132 12.0417 14.1011V18.5671C12.0417 18.6748 12.0171 18.781 11.9699 18.8778C11.9227 18.9746 11.854 19.0593 11.7691 19.1255C11.6842 19.1918 11.5854 19.2378 11.48 19.2601C11.3747 19.2824 11.2657 19.2804 11.1612 19.2542L9.74454 18.9C9.59137 18.8617 9.45541 18.7732 9.35826 18.6487C9.26111 18.5242 9.20834 18.3709 9.20833 18.2129V14.1011C9.20829 13.9132 9.13364 13.7331 9.00079 13.6003L4.45754 9.05703C4.32469 8.92422 4.25004 8.74408 4.25 8.55624V7.43282C4.25 7.24496 4.32463 7.06479 4.45747 6.93195C4.5903 6.79912 4.77047 6.72449 4.95833 6.72449Z"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default ClassesFilter;
