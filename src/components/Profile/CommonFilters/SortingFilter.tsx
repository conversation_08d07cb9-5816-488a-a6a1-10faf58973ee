import { SORT } from "@/constant/Enums";
import { Box, MenuItem, Select, SelectChangeEvent } from "@mui/material";
import React from "react";

const list = [
  {
    id: 1,
    name: "Latest to Oldest",
    value: SORT.ASCENDING,
  },
  {
    id: 2,
    name: "Oldest to Latest",
    value: SORT.DESCENDING,
  },
];

const SortingFilter = ({ sort, setSort }) => {
  const handleChange = (event: SelectChangeEvent) => {
    setSort(event.target.value as string);
  };

  return (
    <Select
      id="demo-simple-select"
      value={sort}
      onChange={handleChange}
      sx={{
        "& .MuiSelect-select": {
          padding: "8px 12px",
          fontSize: "12px",
        },
        borderRadius: 3,
      }}
      renderValue={(selected) => {
        const selectedItem = list.find((item) => item.value === selected);
        return (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <SortIcon />
            <span>{selectedItem?.name}</span>
          </Box>
        );
      }}
    >
      {list.map((m, i) => (
        <MenuItem sx={{ fontSize: 12 }} key={i} value={m.value}>
          {m.name}
        </MenuItem>
      ))}
    </Select>
  );
};

const SortIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 23 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.25 6.625V19.375"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M5 10.8394L9.25 6.58936"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.7917 19.4106V6.66064"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.7917 19.4106L17.0417 15.1606"
        stroke="black"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default SortingFilter;
