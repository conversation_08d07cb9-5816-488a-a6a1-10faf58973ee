import { Box, IconButton } from "@mui/material";
import React, { useEffect, useMemo, useRef, useState } from "react";

type ScrollerProps = React.FC<{
  children: React.ReactNode;
}>;

const Scroller: ScrollerProps = ({ children }) => {
  const scrollContainerRef = useRef(null);

  return (
    <Box position="relative">
      <Box
        ref={scrollContainerRef}
        display="flex"
        flexDirection="row"
        alignItems="stretch"
        gap={2}
        sx={{
          overflowX: "scroll",
          p: 2,
          "&::-webkit-scrollbar": { display: "none" },
          scrollbarWidth: "none",
          msOverflowStyle: "none",
        }}
      >
        {children}
      </Box>
      <LeftRightButton scrollContainerRef={scrollContainerRef} isRight />
      <LeftRightButton scrollContainerRef={scrollContainerRef} />
    </Box>
  );
};

export default Scroller;

const LeftRightButton = ({ isRight = false, scrollContainerRef }) => {
  const [isAtExtreme, setIsAtExtreme] = useState(false);

  const style = useMemo(
    () => ({
      position: "absolute",
      top: "50%",
      transform: "translateY(-50%)",
      [isRight ? "right" : "left"]: -25,
    }),
    [isRight]
  );

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const checkScrollPosition = () => {
      const { scrollLeft, scrollWidth, clientWidth } = container;
      const maxScroll = scrollWidth - clientWidth;

      if (isRight) {
        setIsAtExtreme(scrollLeft >= maxScroll - 1);
      } else {
        setIsAtExtreme(scrollLeft <= 0);
      }
    };

    checkScrollPosition();
    container.addEventListener("scroll", checkScrollPosition);

    return () => {
      container.removeEventListener("scroll", checkScrollPosition);
    };
  }, [isRight, scrollContainerRef]);

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = container.clientWidth * 0.75;
      const direction = isRight ? scrollAmount : -scrollAmount;
      container.scrollBy({ left: direction, behavior: "smooth" });
    }
  };

  if (isAtExtreme) return null;

  return (
    <IconButton
      onClick={handleScroll}
      sx={{
        bgcolor: "background.paper",
        "&:hover": { bgcolor: "grey.100" },
        height: 40,
        width: 40,
        borderRadius: 20,
        boxShadow: "0px 1px 10px 0px rgba(0, 0, 0, 0.25)",
        ...style,
      }}
      aria-label={isRight ? "Scroll right" : "Scroll left"}
      size="large"
    >
      {isRight ? (
        <svg
          width="10"
          height="20"
          viewBox="0 0 10 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1 1.5L9 10L1 18.5"
            stroke="black"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ) : (
        <svg
          width="10"
          height="20"
          viewBox="0 0 10 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9 1.5L1 10L9 18.5"
            stroke="black"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}
    </IconButton>
  );
};
