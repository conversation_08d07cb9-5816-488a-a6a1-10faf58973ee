import { CountryType } from "@/api/mongoTypes";
import { useSnackbar } from "@/hooks/useSnackbar";
import { Button, Container, Typography } from "@mui/material";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import CustomButton from "../CustomButton";
import Skeleton from "@mui/material/Skeleton";
import SelectCountry from "./SelectCountry";
import useTranslate from "@/hooks/useTranslate";

type LocationSelectionProps = {
  location: string;
  setLocation: React.Dispatch<React.SetStateAction<string>>;
  stageIx: number;
  data: CountryType[];
  setStageIx: React.Dispatch<React.SetStateAction<number>>;
};

type countriesType = CountryType[] | [];

const ButtonContainerStyle = {
  width: "100%",
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  gap: "0.75rem",
  marginBottom: "2rem",
  flexWrap: {
    xs: "wrap",
    sm: "nowrap",
  },
};

const ImageContainerStyle = {
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  height: {
    xs: "150px",
    sm: "200px",
    md: "275px",
  },
  width: { xs: "150px", sm: "200px", md: "275px" },
};

const ContainerStyle = {
  display: "flex",
  flexDirection: "column",
  gap: "2rem",
  alignItems: "center",
};

const BottomButtonStyle = {
  width: {
    xs: "100%",
    sm: "50%",
    lg: "33%",
  },
};

const LocationSelection: React.FC<LocationSelectionProps> = ({
  location,
  setLocation,
  stageIx,
  data,
  setStageIx,
}) => {
  const { showSnackbar } = useSnackbar();
  const { translate } = useTranslate();
  const [countries, setCountries] = useState<countriesType>([]);

  const handleContinue = () => {
    if (!location) {
      showSnackbar(translate("kyc.select-country-please"), {
        type: "warning",
      });
    } else {
      setStageIx(+stageIx + 1);
    }
  };

  const handleDoItLater = () => {
    setLocation("");
    setStageIx(+stageIx + 1);
  };
  const ranOnce = useRef(false);

  useEffect(() => {
    if (ranOnce.current) return;
    if (countries.length > 0 && !location) {
      const defaultLocation = (() => {
        if (location) {
          return location;
        }
        const usa = data.find((f) => f.code === "US");
        if (usa?._id) {
          return usa?._id;
        }
        const flallbackFirstCountry = countries[0]?._id;
        if (flallbackFirstCountry) {
          return flallbackFirstCountry;
        }
        return null;
      })();
      setLocation(defaultLocation);
      ranOnce.current = true;
    }
  }, [countries, location]);

  useEffect(() => {
    setCountries(data);
  }, [data]);

  const selectedCountryName = countries.find(
    (f: CountryType) => f._id === location
  )?.name;

  return (
    <Container sx={ContainerStyle}>
      <Typography
        variant="h2"
        gutterBottom
        sx={{
          fontWeight: "600",
          textAlign: "center",
          fontSize: {
            xs: "1.5rem",
            md: "1.9rem",
            lg: "2rem",
          },
        }}
      >
        {translate("kyc.location")}
      </Typography>
      <Container sx={ImageContainerStyle}>
        <Image fill alt="eaerth image" src="/images/profile/earth.webp" />
      </Container>

      <Container
        sx={{
          width: {
            xs: "100%",
            sm: "66.666667%",
            md: "50%",
          },
          display: "flex",
          flexDirection: "row",
          fontSize: "1.125rem",
          lineHeight: "1.75rem",
        }}
      >
        <SelectCountry
          country={location}
          isManageProfile
          countriesList={countries}
          setCountry={setLocation}
          countryButtonStyle={{
            background: "#B3B3B3",
          }}
        />
      </Container>

      <Container sx={ButtonContainerStyle}>
        <CustomButton
          text={translate("common.do-later")}
          onClick={handleDoItLater}
          colortype="secondary"
          sx={BottomButtonStyle}
        />
        <CustomButton
          text={translate("common.continue")}
          sx={BottomButtonStyle}
          onClick={handleContinue}
        />
      </Container>
    </Container>
  );
};

const LocationLoading = () => {
  return (
    <Container sx={ContainerStyle}>
      <Container sx={ImageContainerStyle}>
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          sx={{
            borderRadius: "9999px",
          }}
        />
      </Container>

      <Container
        sx={{
          width: {
            xs: "100%",
            sm: "66.666667%",
            md: "50%",
          },
          display: "flex",
          flexDirection: "row",
          fontSize: "1.125rem",
          lineHeight: "1.75rem",
        }}
      >
        <Skeleton variant="rectangular" width="100%" height={60} />
      </Container>

      <Container sx={ButtonContainerStyle}>
        <Skeleton variant="rectangular" sx={BottomButtonStyle} height={60} />
        <Skeleton variant="rectangular" sx={BottomButtonStyle} height={60} />
      </Container>
    </Container>
  );
};

export default LocationSelection;
