import { Box, Typography } from "@mui/material";
import React from "react";
import CardSectionTitle from "./CardSectionTitle";
import { GoalIcon } from "./Icons";
import Tag from "@/components/Tag";
import useTranslate from "@/hooks/useTranslate";
import { getGoalId } from "@/utils/profile";

const Goals = ({ goals, sx = {} }) => {
  const { translate } = useTranslate();
  return (
    <Box display="flex" flexDirection="column" sx={sx}>
      <CardSectionTitle icon={<GoalIcon />} name={translate("dash.goals")} />
      <Box
        display="flex"
        flexWrap="wrap"
        flexDirection="row"
        alignItems="center"
        mt={1}
        gap={1}
      >
        {goals.length === 0 ? (
          <Typography fontSize={12} color="rgba(109, 109, 109, 1)">
            {translate("dash.not-selected")}
          </Typography>
        ) : (
          goals.map((m, i) => (
            <Tag
              key={i}
              text={translate(getGoalId(m.name))}
              sx={{ maxWidth: "max-content" }}
            />
          ))
        )}
      </Box>
    </Box>
  );
};

export default Goals;
