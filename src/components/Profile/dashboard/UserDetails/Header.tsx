import { useUser } from "@clerk/nextjs";
import { Avatar, Box, Typography } from "@mui/material";
import Link from "next/link";
import React from "react";

const Header = ({ userDetails }) => {
  const { user } = useUser();
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      mb={2}
      pb={3}
      sx={{
        borderBottom: "1px solid rgba(236, 236, 236, 1)",
      }}
    >
      <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
        <Avatar src={user?.imageUrl} />
        <Box>
          <Typography fontWeight="800" fontSize={16}>
            {userDetails.firstName}&nbsp;{userDetails.lastName}
          </Typography>
          <Typography color="rgba(181, 181, 181, 1)" fontSize={14}>
            {userDetails.email}
          </Typography>
        </Box>
      </Box>
      <Box sx={{ cursor: "pointer" }}>
        <Link href="/manage-profile">
          <svg
            width="16"
            height="16"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 2.99994L15 5.99994M10 16.9999H18M2 12.9999L1 16.9999L5 15.9999L16.586 4.41394C16.9609 4.03889 17.1716 3.53027 17.1716 2.99994C17.1716 2.46961 16.9609 1.961 16.586 1.58594L16.414 1.41394C16.0389 1.039 15.5303 0.828369 15 0.828369C14.4697 0.828369 13.9611 1.039 13.586 1.41394L2 12.9999Z"
              stroke="black"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </Link>
      </Box>
    </Box>
  );
};

export default Header;
