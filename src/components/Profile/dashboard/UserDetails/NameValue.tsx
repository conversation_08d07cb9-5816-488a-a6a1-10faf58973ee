import { Box, Typography } from "@mui/material";
import React from "react";

const NameValue = ({ name, value }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
    >
      <Typography color="rgba(0, 0, 0, 1)" fontSize={14}>
        {name}
      </Typography>
      <Typography color="rgba(109, 109, 109, 1)" fontSize={14}>
        {value}
      </Typography>
    </Box>
  );
};

export default NameValue;
