import { Box, Chip, SxProps, Theme, Typography } from "@mui/material";
import React, { useMemo, useState } from "react";
import CardSectionTitle from "./CardSectionTitle";
import { InterestsSectionIcon } from "./Icons";
import { INTEREST_TYPES } from "@/constant/Enums";
import InterestsIcon from "../../InterestsIcon";
import Tag from "@/components/Tag";
import { InterestType, UserSelectedInterestsType } from "@/api/mongoTypes";
import useTranslate from "@/hooks/useTranslate";
import { getInterestsId } from "@/utils/profile";

const tabs = [
  {
    id: 1,
    // name: "Love It",
    name: "kyc.love",
    value: INTEREST_TYPES.LOVE,
  },
  {
    id: 2,
    // name: "Neutral",
    name: "kyc.neutral",
    value: INTEREST_TYPES.NEUTRAL,
  },
  {
    id: 3,
    // name: "Hate It",
    name: "kyc.hate",
    value: INTEREST_TYPES.HATE,
  },
];

type InterestsProps = React.FC<{
  selectedInterests: UserSelectedInterestsType;
  sx?: SxProps<Theme>;
}>;
const Interests: InterestsProps = ({ selectedInterests, sx = {} }) => {
  const [active, setActive] = useState(INTEREST_TYPES.LOVE);
  const { translate } = useTranslate();

  const interests = useMemo(() => {
    return selectedInterests
      .filter((m) => m.rating === active)
      .map((m) => {
        const interestsDetail = m.id as InterestType;
        if (
          m &&
          interestsDetail &&
          typeof interestsDetail === "object" &&
          "name" in interestsDetail
        ) {
          return interestsDetail.name;
        }
        return "";
      })
      .filter((j) => j);
  }, [active, selectedInterests]);

  return (
    <Box mb={5} sx={sx}>
      <CardSectionTitle
        icon={<InterestsSectionIcon />}
        name={translate("dash.interests")}
      />
      <Box
        sx={{
          border: sx ? null : "1px solid #E6E6E6",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          width="100%"
          p={0}
          sx={{
            overflow: "hidden",
          }}
        >
          {tabs.map((m, i) => (
            <Box
              sx={{
                color: active === m.value ? "#fff" : "rgba(60, 60, 60, 1)",
                background:
                  active === m.value ? "rgba(20, 167, 156, 1)" : "#fff",
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                width: "100%",
                height: 30,
                borderTopRightRadius: 8,
                borderTopLeftRadius: 8,
              }}
              key={i}
              onClick={() => {
                setActive(m.value);
              }}
            >
              <InterestsIcon
                type={m.value}
                color={active === m.value ? "#fff" : "rgba(60, 60, 60, 1)"}
              />
              &nbsp;
              <Typography fontSize="0.75rem">{translate(m.name)}</Typography>
            </Box>
          ))}
        </Box>
        <Box gap={1} p={1}>
          {interests.length === 0 ? (
            <Typography fontSize="0.75rem" color="rgba(109, 109, 109, 1)">
              {translate("dash.not-selected")}
            </Typography>
          ) : (
            <>
              {interests.map((m, i) => (
                <Tag
                  key={i}
                  text={translate(getInterestsId(m))}
                  sx={{
                    backgroundColor: "rgba(255, 245, 226, 1)",
                    color: "rgba(229, 160, 0, 1)",
                    fontSize: "0.7rem",
                    marginTop: 1,
                  }}
                />
              ))}
            </>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Interests;
