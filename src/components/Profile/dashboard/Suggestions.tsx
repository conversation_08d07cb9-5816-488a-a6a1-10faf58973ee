import { Box, Skeleton } from "@mui/material";
import React, { useEffect, useState } from "react";
import SectionHeader from "./SectionHeader";
import ClassCardContainer from "./ClassCardContainer";
import { getClassessFetchTabs } from "@/utils/classes";
import axiosInstance from "@/utils/interceptor";
import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import { useSnackbar } from "@/hooks/useSnackbar";
import { getLocalUser } from "@/utils/common";
import axios from "axios";
import { getDateAsPerUTC } from "@/utils/dateTime";
import useTranslate from "@/hooks/useTranslate";

type SuggestionsProps = React.FC<{}>;
const Suggestions: SuggestionsProps = () => {
  const [active, setActive] = useState<CLASSESS_FETCH_DURATION>(2);
  const [isLoading, setIsLoading] = useState(true);
  const [classess, setClassess] = useState([]);
  const { showSnackbar } = useSnackbar();
  const { translate } = useTranslate();

  const fetchSuggestedClassess = async () => {
    try {
      setIsLoading(true);
      setClassess([]);
      const { data: respdata } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/user/get-dashboard-suggested`,
        {
          params: {
            isUpcoming: active === CLASSESS_FETCH_DURATION.UPCOMING,
            currentDate: getDateAsPerUTC(new Date()),
          },
          headers: {
            userid: getLocalUser()._id,
          },
        }
      );
      if (respdata.success) {
        setClassess(respdata.data);
      } else {
        showSnackbar("Failed to fetch the suggested class", {
          type: "error",
        });
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.error(
        "Something went wrong in fetchSuggestedClassess due to ",
        error
      );
    }
  };

  useEffect(() => {
    fetchSuggestedClassess();
  }, [active]);

  return (
    <Box>
      <SectionHeader
        title={translate("dash.suggestions")}
        linkText={translate("dash.see-all")}
        link="/profile/suggestions"
        active={active}
        setActive={setActive}
        isSuggestions
        tabs={getClassessFetchTabs({
          needPast: false,
          needAll: false,
          needCurrent: true,
          needUpcoming: true,
        })}
      />
      <ClassCardContainer
        isLoading={isLoading}
        data={classess}
        active={active}
        isDashboard
      />
    </Box>
  );
};

export default Suggestions;
