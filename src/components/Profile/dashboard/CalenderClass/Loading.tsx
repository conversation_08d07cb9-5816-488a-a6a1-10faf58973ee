import { Box, Card, Skeleton } from "@mui/material";
import React from "react";

const Loading = () => {
  return (
    <Card
      sx={{
        borderRadius: 2,
        p: 3,
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        width="100%"
        mb={2}
        gap={2}
      >
        {new Array(5).fill("").map((m, i) => (
          <Skeleton
            key={i}
            variant="rectangular"
            sx={{ borderRadius: 3 }}
            width="20%"
            height={40}
          />
        ))}
      </Box>

      <Skeleton
        variant="rectangular"
        sx={{ borderRadius: 3, mb: 2 }}
        width="80%"
        height={20}
      />
      {new Array(2).fill("").map((m, i) => (
        <Skeleton
          key={i}
          variant="rectangular"
          sx={{ borderRadius: 3, mb: 2 }}
          width="100%"
          height={125}
        />
      ))}
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
      >
        <Skeleton
          variant="rectangular"
          sx={{ borderRadius: 3, mt: 2 }}
          width="30%"
          height={20}
        />
      </Box>
    </Card>
  );
};

export default Loading;
