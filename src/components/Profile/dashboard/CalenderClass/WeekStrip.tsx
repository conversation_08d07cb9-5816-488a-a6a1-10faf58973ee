import React, { useState, useEffect } from "react";
import dayjs from "dayjs";
import { Box, Typography } from "@mui/material";
import isoWeek from "dayjs/plugin/isoWeek";
import Icon from "./PrevNext";
import PrevNextIcon from "./PrevNext";
import useTranslate from "@/hooks/useTranslate";
import "dayjs/locale/es";
import "dayjs/locale/en";

dayjs.extend(isoWeek);

const WeekStrip = ({ selectedDate, setSelectedDate }) => {
  const { preferredLanguage } = useTranslate();
  const [currentWeekStart, setCurrentWeekStart] = useState(
    selectedDate
      ? dayjs(selectedDate).startOf("isoWeek")
      : dayjs().startOf("isoWeek")
  );

  useEffect(() => {
    if (selectedDate) {
      setCurrentWeekStart(dayjs(selectedDate).startOf("isoWeek"));
    }
  }, [selectedDate]);

  const days = Array.from({ length: 7 }, (_, i) =>
    currentWeekStart.add(i, "day")
  ).filter((day) => day.isoWeekday() >= 1 && day.isoWeekday() <= 5);

  const handlePreviousWeek = () => {
    setCurrentWeekStart(currentWeekStart.subtract(1, "week"));
  };

  const handleNextWeek = () => {
    setCurrentWeekStart(currentWeekStart.add(1, "week"));
  };

  const monthYear =
    currentWeekStart.month() === currentWeekStart.endOf("isoWeek").month()
      ? currentWeekStart.locale(preferredLanguage).format("MMMM YYYY")
      : `${currentWeekStart
          .locale(preferredLanguage)
          .format("MMMM")} - ${currentWeekStart
          .endOf("isoWeek")
          .locale(preferredLanguage)
          .format("MMMM")} ${currentWeekStart
          .locale(preferredLanguage)
          .format("YYYY")}`;

  console.log("preferredLanguage", preferredLanguage);

  return (
    <Box sx={{ textAlign: "center", mb: 2 }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 5,
        }}
      >
        <PrevNextIcon isPrev onClick={handlePreviousWeek} />
        <Typography
          fontSize={16}
          fontWeight={600}
          variant="h6"
          color="rgba(51, 51, 51, 1)"
        >
          {monthYear}
        </Typography>
        <PrevNextIcon isPrev={false} onClick={handleNextWeek} />
      </Box>

      <Box sx={{ display: "flex", gap: 1, justifyContent: "center" }}>
        {days.map((day) => {
          const isSelected = selectedDate && day.isSame(selectedDate, "day");
          return (
            <Box
              key={day.format("YYYY-MM-DD")}
              onClick={() => setSelectedDate(day)}
              sx={{
                border: "1px solid rgba(217, 217, 217, 1)",
                width: "100%",
                borderRadius: 2,
                color: isSelected ? "#fff" : "rgba(100, 116, 139, 1)",
                background: isSelected ? "rgba(20, 167, 156, 1)" : "#fff",
                p: 2,
                fontSize: 14,
                cursor: "pointer",
              }}
            >
              {day.format("D")}
              <br />
              {day.locale(preferredLanguage).format("ddd")}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

export default WeekStrip;
