import React from "react";

const PrevNextIcon = ({ isPrev, ...props }) => {
  return (
    <svg
      {...props}
      style={{
        cursor: "pointer",
        transform: isPrev ? "rotate(180deg)" : "rotate(0deg)",
      }}
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="24.5"
        y="24.5"
        width="24"
        height="24"
        rx="12"
        transform="rotate(-180 24.5 24.5)"
        stroke="black"
      />
      <g clip-path="url(#clip0_10534_31670)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M15.5893 11.9109C15.7455 12.0671 15.8333 12.2791 15.8333 12.5C15.8333 12.721 15.7455 12.9329 15.5893 13.0892L10.8751 17.8034C10.7983 17.883 10.7063 17.9464 10.6046 17.9901C10.503 18.0338 10.3936 18.0568 10.283 18.0577C10.1723 18.0587 10.0626 18.0376 9.96016 17.9957C9.85775 17.9538 9.7647 17.8919 9.68646 17.8137C9.60822 17.7354 9.54634 17.6424 9.50444 17.54C9.46254 17.4376 9.44145 17.3278 9.44241 17.2172C9.44338 17.1065 9.46636 16.9972 9.51004 16.8955C9.55371 16.7939 9.6172 16.7019 9.69679 16.625L13.8218 12.5L9.69679 8.37503C9.54499 8.21786 9.461 8.00736 9.46289 7.78886C9.46479 7.57036 9.55243 7.36135 9.70694 7.20685C9.86145 7.05234 10.0705 6.9647 10.289 6.9628C10.5075 6.9609 10.718 7.0449 10.8751 7.1967L15.5893 11.9109Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_10534_31670">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(2.5 2.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default PrevNextIcon;
