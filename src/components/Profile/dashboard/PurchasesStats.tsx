import useTranslate from "@/hooks/useTranslate";
import { ValueOf } from "@/types";
import { Box, Typography } from "@mui/material";
import React from "react";

const ListType = {
  ONLINE_CLUB: 1,
  IN_PERSON: 2,
  COMMUNITY: 3,
  ONLINE_CLASSES: 4,
};

type ListSingleType = {
  id: ValueOf<typeof ListType>;
  name1: String;
  name2: String;
  text: String;
  backgroundColor: String;
};
const list: ListSingleType[] = [
  {
    id: ListType.ONLINE_CLUB,
    name1: "dash-online",
    name2: "dash-Clubs",
    text: "dash.clubs-joined",
    backgroundColor: "rgba(197, 242, 182, 1)",
  },
  {
    id: ListType.IN_PERSON,
    name1: "dash-inperson",
    name2: "dash-classes",
    text: "dash.purchased-plan",
    backgroundColor: "rgba(255, 232, 193, 1)",
  },
  {
    id: ListType.COMMUNITY,
    name1: "dash-community",
    name2: "dash-experiences",
    text: "dash.purchased-event",
    backgroundColor: "rgba(188, 237, 234, 1)",
  },
  {
    id: ListType.ONLINE_CLASSES,
    name1: "dash-online",
    name2: "dash-classes",
    text: "dash.purchased-plan",
    backgroundColor: "rgba(250, 216, 201, 1)",
  },
];

type PurchasesStatsProps = React.FC<{
  stats: {
    eventCount: number;
    inPersonClassesCount: number;
    onlineClassesCount: number;
    onlineClubsCount: number;
  };
}>;
const PurchasesStats: PurchasesStatsProps = ({ stats }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
      flexWrap="wrap"
      gap={1}
      mt={4}
    >
      {list.map((m, i) => (
        <SingleCard
          count={(() => {
            if (m.id === ListType.ONLINE_CLUB) {
              return stats?.onlineClubsCount ?? 0;
            }
            if (m.id === ListType.ONLINE_CLASSES) {
              return stats?.onlineClassesCount ?? 0;
            }
            if (m.id === ListType.IN_PERSON) {
              return stats?.inPersonClassesCount ?? 0;
            }
            return stats?.eventCount ?? 0;
          })()}
          data={m}
          key={i}
        />
      ))}
    </Box>
  );
};

type SingleCardProps = React.FC<{
  data: ListSingleType;
  count: number;
}>;
const SingleCard: SingleCardProps = ({ data, count }) => {
  const { translate } = useTranslate();
  return (
    <Box
      sx={{
        backgroundColor: String(data.backgroundColor),
        width: {
          xs: "49%",
          smd: "24%",
        },
        height: 120,
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        borderRadius: 4,
        p: 3,
      }}
    >
      <Typography fontWeight={700} fontSize={15}>
        {translate(String(data.name1))}
        <br />
        {translate(String(data.name2))}
      </Typography>
      <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
        <Box
          sx={{
            height: 30,
            width: 30,
            borderRadius: 30,
            background: "#fff",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Icon type={data.id} />
        </Box>
        <Box display="flex" flexDirection="column">
          <Typography fontWeight={700} fontSize={13}>
            {count}
          </Typography>
          <Typography
            fontWeight={500}
            fontSize={10}
            color="rgba(60, 60, 60, 1)"
          >
            {translate(String(data.text))}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

const Icon = ({ type }) => {
  if (type === ListType.ONLINE_CLUB) {
    return (
      <svg
        width="18"
        height="16"
        viewBox="0 0 18 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.42045 12V11.2222C5.42045 10.1908 5.79758 9.20163 6.46888 8.47232C7.14017 7.74301 8.05065 7.33329 9 7.33329M9 7.33329C9.94935 7.33329 10.8598 7.74301 11.5311 8.47232C12.2024 9.20163 12.5795 10.1908 12.5795 11.2222V12M9 7.33329C9.56961 7.33329 10.1159 7.08746 10.5187 6.64988C10.9214 6.21229 11.1477 5.6188 11.1477 4.99996C11.1477 4.38112 10.9214 3.78763 10.5187 3.35004C10.1159 2.91246 9.56961 2.66663 9 2.66663C8.43039 2.66663 7.8841 2.91246 7.48133 3.35004C7.07855 3.78763 6.85227 4.38112 6.85227 4.99996C6.85227 5.6188 7.07855 6.21229 7.48133 6.64988C7.8841 7.08746 8.43039 7.33329 9 7.33329ZM1.125 12V11.2222C1.125 10.6033 1.35128 10.0098 1.75405 9.57227C2.15683 9.13468 2.70311 8.88885 3.27273 8.88885M3.27273 8.88885C3.65247 8.88885 4.01666 8.72496 4.28518 8.43324C4.55369 8.14151 4.70455 7.74585 4.70455 7.33329C4.70455 6.92073 4.55369 6.52507 4.28518 6.23335C4.01666 5.94163 3.65247 5.77774 3.27273 5.77774C2.89299 5.77774 2.5288 5.94163 2.26028 6.23335C1.99176 6.52507 1.84091 6.92073 1.84091 7.33329C1.84091 7.74585 1.99176 8.14151 2.26028 8.43324C2.5288 8.72496 2.89299 8.88885 3.27273 8.88885ZM16.875 12V11.2222C16.875 10.6033 16.6487 10.0098 16.2459 9.57227C15.8432 9.13468 15.2969 8.88885 14.7273 8.88885M14.7273 8.88885C15.107 8.88885 15.4712 8.72496 15.7397 8.43324C16.0082 8.14151 16.1591 7.74585 16.1591 7.33329C16.1591 6.92073 16.0082 6.52507 15.7397 6.23335C15.4712 5.94163 15.107 5.77774 14.7273 5.77774C14.3475 5.77774 13.9833 5.94163 13.7148 6.23335C13.4463 6.52507 13.2955 6.92073 13.2955 7.33329C13.2955 7.74585 13.4463 8.14151 13.7148 8.43324C13.9833 8.72496 14.3475 8.88885 14.7273 8.88885Z"
          stroke="#009951"
          stroke-width="1.2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    );
  }
  if (type === ListType.IN_PERSON) {
    return (
      <svg
        width="21"
        height="26"
        viewBox="0 0 21 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4 5.91663H13.9167C15.2526 5.91663 15.9198 5.91663 16.3349 6.33171C16.75 6.74679 16.75 7.41404 16.75 8.74996V13C16.75 14.3359 16.75 15.0031 16.3349 15.4182C15.9198 15.8333 15.2526 15.8333 13.9167 15.8333H8.95833M9.66667 9.10413H13.9167M4 16.5416V13.7083C4 13.0403 4 12.7067 4.20754 12.4992C4.41508 12.2916 4.74871 12.2916 5.41667 12.2916H6.83333M4 16.5416H6.83333M4 16.5416V20.0833M6.83333 12.2916V16.5416M6.83333 12.2916H11.0833M6.83333 16.5416V20.0833"
          stroke="#C86418"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M6.83333 9.10417C6.83333 9.47989 6.68408 9.84022 6.4184 10.1059C6.15272 10.3716 5.79239 10.5208 5.41667 10.5208C5.04094 10.5208 4.68061 10.3716 4.41493 10.1059C4.14926 9.84022 4 9.47989 4 9.10417C4 8.72844 4.14926 8.36811 4.41493 8.10243C4.68061 7.83676 5.04094 7.6875 5.41667 7.6875C5.79239 7.6875 6.15272 7.83676 6.4184 8.10243C6.68408 8.36811 6.83333 8.72844 6.83333 9.10417Z"
          stroke="#C86418"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    );
  }
  if (type === ListType.COMMUNITY) {
    return (
      <svg
        width="22"
        height="24"
        viewBox="0 0 22 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_10163_6500)">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M11.5482 8.89051C11.4983 8.78748 11.4203 8.70059 11.3234 8.63979C11.2264 8.579 11.1142 8.54675 10.9997 8.54675C10.8852 8.54675 10.7731 8.579 10.6761 8.63979C10.5791 8.70059 10.5012 8.78748 10.4513 8.89051L9.52503 10.8072L7.45641 11.1159C7.34476 11.1327 7.23997 11.1802 7.15371 11.253C7.06746 11.3258 7.00313 11.4212 6.96791 11.5285C6.93269 11.6357 6.92796 11.7507 6.95425 11.8605C6.98053 11.9702 7.0368 12.0706 7.11678 12.1503L8.61991 13.6453L8.26484 15.7578C8.24583 15.8702 8.25874 15.9857 8.3021 16.0911C8.34546 16.1966 8.41753 16.2877 8.51013 16.3543C8.60272 16.4208 8.71212 16.46 8.82589 16.4675C8.93967 16.4749 9.05324 16.4503 9.15372 16.3964L10.9997 15.4043L12.8457 16.3956C12.9461 16.4494 13.0596 16.474 13.1733 16.4666C13.287 16.4591 13.3963 16.42 13.4889 16.3536C13.5814 16.2872 13.6535 16.1961 13.6969 16.0908C13.7404 15.9855 13.7534 15.8701 13.7346 15.7578L13.3795 13.6436L14.8827 12.1494C14.9626 12.0698 15.0189 11.9694 15.0452 11.8596C15.0715 11.7498 15.0667 11.6349 15.0315 11.5276C14.9963 11.4204 14.932 11.325 14.8457 11.2522C14.7595 11.1793 14.6547 11.1319 14.543 11.1151L12.4752 10.8064L11.549 8.88969L11.5482 8.89051ZM10.4813 11.6278L10.9997 10.5553L11.5181 11.6278C11.5614 11.7172 11.6258 11.7946 11.7058 11.8534C11.7858 11.9121 11.879 11.9504 11.9772 11.965L13.1626 12.1421L12.2965 13.0026C12.2275 13.0713 12.176 13.1556 12.1463 13.2483C12.1167 13.3411 12.1096 13.4396 12.1258 13.5356L12.3265 14.7348L11.2882 14.1774C11.1995 14.1298 11.1004 14.1049 10.9997 14.1049C10.8991 14.1049 10.8 14.1298 10.7113 14.1774L9.67291 14.7348L9.87441 13.5356C9.89051 13.4395 9.88335 13.341 9.85353 13.2482C9.8237 13.1555 9.77207 13.0712 9.70297 13.0026L8.83684 12.1413L10.0231 11.965C10.1211 11.9503 10.2141 11.9119 10.294 11.8532C10.3738 11.7944 10.4382 11.7171 10.4813 11.6278Z"
            fill="#005F56"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M14.25 6.10938C14.25 5.94776 14.1858 5.79276 14.0715 5.67848C13.9572 5.5642 13.8022 5.5 13.6406 5.5C13.479 5.5 13.324 5.5642 13.2097 5.67848C13.0955 5.79276 13.0312 5.94776 13.0312 6.10938V6.3125H8.96875V6.10938C8.96875 5.94776 8.90455 5.79276 8.79027 5.67848C8.67599 5.5642 8.52099 5.5 8.35938 5.5C8.19776 5.5 8.04276 5.5642 7.92848 5.67848C7.8142 5.79276 7.75 5.94776 7.75 6.10938V6.3125H6.32812C5.84328 6.3125 5.37829 6.50511 5.03545 6.84795C4.69261 7.19079 4.5 7.65578 4.5 8.14062V16.6719C4.5 17.1567 4.69261 17.6217 5.03545 17.9646C5.37829 18.3074 5.84328 18.5 6.32812 18.5H15.6719C16.1567 18.5 16.6217 18.3074 16.9646 17.9646C17.3074 17.6217 17.5 17.1567 17.5 16.6719V8.14062C17.5 7.65578 17.3074 7.19079 16.9646 6.84795C16.6217 6.50511 16.1567 6.3125 15.6719 6.3125H14.25V6.10938ZM7.75 8.14062V7.53125H6.32812C6.16651 7.53125 6.01151 7.59545 5.89723 7.70973C5.78295 7.82401 5.71875 7.97901 5.71875 8.14062V16.6719C5.71875 17.0083 5.99175 17.2812 6.32812 17.2812H15.6719C15.8335 17.2812 15.9885 17.217 16.1028 17.1028C16.217 16.9885 16.2812 16.8335 16.2812 16.6719V8.14062C16.2812 7.97901 16.217 7.82401 16.1028 7.70973C15.9885 7.59545 15.8335 7.53125 15.6719 7.53125H14.25V8.14062C14.25 8.30224 14.1858 8.45724 14.0715 8.57152C13.9572 8.6858 13.8022 8.75 13.6406 8.75C13.479 8.75 13.324 8.6858 13.2097 8.57152C13.0955 8.45724 13.0312 8.30224 13.0312 8.14062V7.53125H8.96875V8.14062C8.96875 8.30224 8.90455 8.45724 8.79027 8.57152C8.67599 8.6858 8.52099 8.75 8.35938 8.75C8.19776 8.75 8.04276 8.6858 7.92848 8.57152C7.8142 8.45724 7.75 8.30224 7.75 8.14062Z"
            fill="#005F56"
          />
        </g>
        <defs>
          <clipPath id="clip0_10163_6500">
            <rect
              width="13"
              height="13"
              fill="white"
              transform="translate(4.5 5.5)"
            />
          </clipPath>
        </defs>
      </svg>
    );
  }
  return (
    <svg
      width="22"
      height="24"
      viewBox="0 0 22 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.2753 17.8584C10.2297 18.0316 10.1955 18.207 10.1727 18.3848C10.1499 18.5625 10.134 18.7448 10.1249 18.9316C9.54154 18.8633 8.98557 18.7266 8.45693 18.5215C7.9283 18.3164 7.4384 18.0544 6.98724 17.7354C6.53608 17.4163 6.12593 17.0472 5.7568 16.6279C5.38766 16.2087 5.07322 15.7529 4.81346 15.2607C4.5537 14.7686 4.35318 14.249 4.21191 13.7021C4.07064 13.1553 4 12.5879 4 12C4 11.3574 4.08203 10.7376 4.24609 10.1406C4.41015 9.54362 4.64484 8.98535 4.95017 8.46582C5.25551 7.94629 5.62008 7.47461 6.0439 7.05078C6.46772 6.62695 6.94167 6.26237 7.46574 5.95703C7.98982 5.65169 8.5458 5.41699 9.13368 5.25293C9.72155 5.08887 10.3436 5.00456 10.9998 5C11.6424 5 12.2599 5.08203 12.8523 5.24609C13.4448 5.41016 14.003 5.64714 14.5271 5.95703C15.0512 6.26693 15.5229 6.63151 15.9421 7.05078C16.3614 7.47005 16.7282 7.94401 17.0427 8.47266C17.3571 9.0013 17.5918 9.55729 17.7468 10.1406C17.9017 10.724 17.986 11.3438 17.9997 12H17.5143C17.4505 11.8952 17.3845 11.7926 17.3161 11.6924C17.2478 11.5921 17.168 11.4964 17.0769 11.4053C17.0495 11.0088 16.9789 10.6237 16.8649 10.25H8.49111C8.45465 10.5417 8.42731 10.8311 8.40908 11.1182C8.39085 11.4053 8.37946 11.6992 8.3749 12C8.3749 12.2962 8.38402 12.5879 8.40225 12.875C8.42048 13.1621 8.4501 13.4538 8.49111 13.75H10.9998C10.9998 13.8958 11.0112 14.0417 11.034 14.1875C11.0568 14.3333 11.0864 14.4792 11.1229 14.625H8.6415C8.69618 14.8848 8.7691 15.1696 8.86024 15.4795C8.95139 15.7894 9.06532 16.0924 9.20203 16.3887C9.33875 16.6849 9.49597 16.9629 9.6737 17.2227C9.85143 17.4824 10.0519 17.6943 10.2753 17.8584ZM16.53 9.375C16.3523 9.00586 16.1426 8.65951 15.9011 8.33594C15.6596 8.01237 15.3861 7.71615 15.0808 7.44727C14.7755 7.17839 14.4519 6.93913 14.1101 6.72949C13.7683 6.51986 13.3992 6.34896 13.0027 6.2168C13.1668 6.44466 13.3126 6.6862 13.4402 6.94141C13.5678 7.19661 13.684 7.45866 13.7888 7.72754C13.8937 7.99642 13.9825 8.26986 14.0554 8.54785C14.1284 8.82585 14.1922 9.10156 14.2468 9.375H16.53ZM10.9998 5.875C10.772 5.875 10.5624 5.93652 10.371 6.05957C10.1796 6.18262 10.0018 6.3444 9.83776 6.54492C9.6737 6.74544 9.52787 6.97103 9.40027 7.22168C9.27267 7.47233 9.15646 7.72982 9.05165 7.99414C8.94683 8.25846 8.86252 8.50911 8.79872 8.74609C8.73492 8.98307 8.68251 9.19271 8.6415 9.375H13.3582C13.3217 9.19727 13.2693 8.98763 13.201 8.74609C13.1326 8.50456 13.0483 8.25391 12.948 7.99414C12.8478 7.73438 12.7339 7.47917 12.6063 7.22852C12.4787 6.97786 12.3328 6.75 12.1688 6.54492C12.0047 6.33984 11.8247 6.17806 11.6287 6.05957C11.4328 5.94108 11.2231 5.87956 10.9998 5.875ZM9.0038 6.20996C8.61643 6.34212 8.2473 6.51074 7.8964 6.71582C7.54549 6.9209 7.21966 7.16016 6.91888 7.43359C6.61811 7.70703 6.34467 8.00553 6.09859 8.3291C5.8525 8.65267 5.64287 9.0013 5.46969 9.375H7.75285C7.80298 9.12435 7.8645 8.85775 7.93741 8.5752C8.01033 8.29264 8.10147 8.01009 8.21084 7.72754C8.32022 7.44499 8.4387 7.17383 8.5663 6.91406C8.69391 6.6543 8.83974 6.4196 9.0038 6.20996ZM4.87498 12C4.87498 12.6061 4.95929 13.1895 5.1279 13.75H7.60929C7.57284 13.4583 7.54549 13.1689 7.52727 12.8818C7.50904 12.5947 7.49992 12.3008 7.49992 12C7.49992 11.7038 7.50904 11.4121 7.52727 11.125C7.54549 10.8379 7.57284 10.5462 7.60929 10.25H5.1279C4.95929 10.8105 4.87498 11.3939 4.87498 12ZM5.45602 14.625C5.63375 14.9941 5.84338 15.3405 6.08491 15.6641C6.32645 15.9876 6.59988 16.2861 6.90521 16.5596C7.21054 16.833 7.53866 17.07 7.88956 17.2705C8.24047 17.471 8.6096 17.6419 8.99696 17.7832C8.8329 17.5553 8.68707 17.3138 8.55947 17.0586C8.43187 16.8034 8.31566 16.5413 8.21084 16.2725C8.10603 16.0036 8.01716 15.7301 7.94425 15.4521C7.87133 15.1742 7.80525 14.8984 7.74601 14.625H5.45602ZM16.0447 15.8623C16.3409 16.0127 16.6097 16.1973 16.8513 16.416C17.0928 16.6348 17.2979 16.8809 17.4665 17.1543C17.6351 17.4277 17.7673 17.7194 17.863 18.0293C17.9587 18.3392 18.0042 18.6628 17.9997 19H17.1247C17.1247 18.64 17.0564 18.3005 16.9196 17.9814C16.7829 17.6624 16.5961 17.3844 16.3591 17.1475C16.1221 16.9105 15.8419 16.7214 15.5183 16.5801C15.1947 16.4388 14.8552 16.3704 14.4998 16.375C14.1352 16.375 13.7957 16.4434 13.4812 16.5801C13.1668 16.7168 12.8888 16.9036 12.6473 17.1406C12.4057 17.3776 12.2166 17.6579 12.0799 17.9814C11.9432 18.305 11.8748 18.6445 11.8748 19H10.9998C10.9998 18.6673 11.0454 18.346 11.1366 18.0361C11.2277 17.7262 11.3576 17.4323 11.5262 17.1543C11.6948 16.8763 11.8999 16.6302 12.1414 16.416C12.383 16.2018 12.6518 16.0173 12.948 15.8623C12.6063 15.6162 12.3419 15.3086 12.1551 14.9395C11.9682 14.5703 11.8748 14.1738 11.8748 13.75C11.8748 13.39 11.9432 13.0505 12.0799 12.7314C12.2166 12.4124 12.4035 12.1344 12.6404 11.8975C12.8774 11.6605 13.1554 11.4714 13.4744 11.3301C13.7934 11.1888 14.1352 11.1204 14.4998 11.125C14.8598 11.125 15.1993 11.1934 15.5183 11.3301C15.8373 11.4668 16.1153 11.6536 16.3523 11.8906C16.5892 12.1276 16.7784 12.4079 16.9196 12.7314C17.0609 13.055 17.1293 13.3945 17.1247 13.75C17.1247 14.1693 17.0313 14.5635 16.8444 14.9326C16.6576 15.3018 16.391 15.6117 16.0447 15.8623ZM14.4998 15.5C14.7413 15.5 14.9669 15.4544 15.1765 15.3633C15.3861 15.2721 15.573 15.1468 15.737 14.9873C15.9011 14.8278 16.0264 14.6432 16.113 14.4336C16.1996 14.224 16.2452 13.9961 16.2497 13.75C16.2497 13.5085 16.2042 13.2829 16.113 13.0732C16.0219 12.8636 15.8965 12.6768 15.737 12.5127C15.5775 12.3486 15.393 12.2233 15.1833 12.1367C14.9737 12.0501 14.7459 12.0046 14.4998 12C14.2582 12 14.0327 12.0456 13.823 12.1367C13.6134 12.2279 13.4265 12.3532 13.2625 12.5127C13.0984 12.6722 12.9731 12.8568 12.8865 13.0664C12.7999 13.276 12.7544 13.5039 12.7498 13.75C12.7498 13.9915 12.7954 14.2171 12.8865 14.4268C12.9777 14.6364 13.103 14.8232 13.2625 14.9873C13.422 15.1514 13.6066 15.2767 13.8162 15.3633C14.0258 15.4499 14.2537 15.4954 14.4998 15.5Z"
        fill="#F16425"
      />
    </svg>
  );
};

export default PurchasesStats;
