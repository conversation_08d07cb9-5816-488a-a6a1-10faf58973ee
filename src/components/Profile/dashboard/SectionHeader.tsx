import { Box, Typography } from "@mui/material";
import Link from "next/link";
import React from "react";
import PastCurrentUpcoming from "../PastCurrentUpcoming";

type SectionHeaderProps = React.FC<{
  title: String;
  link?: String;
  linkText?: String;
  active?: number;
  setActive?: React.Dispatch<React.SetStateAction<number>>;
  isListEmpty?: boolean;
  isSuggestions?: boolean;
  tabs?: {
    id: number;
    name: string;
  }[];
}>;
const SectionHeader: SectionHeaderProps = ({
  title,
  link,
  linkText,
  active,
  setActive,
  isListEmpty = false,
  isSuggestions = false,
  tabs = [],
}) => {
  return (
    <Box>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
        my={4}
      >
        <Typography fontWeight={800} fontSize={16}>
          {title}
        </Typography>
        <Box display="flex" flexDirection="row" gap={5} alignItems="center">
          {setActive && (
            <Box
              sx={{
                display: {
                  xs: "none",
                  sm: "flex",
                },
              }}
            >
              <PastCurrentUpcoming
                isSuggestions={isSuggestions}
                active={active}
                tabs={tabs}
                setActive={setActive}
              />
            </Box>
          )}
          {!isListEmpty && link && (
            <Link
              style={{
                color: "rgba(20, 167, 156, 1)",
                fontSize: 15,
                fontWeight: 600,
              }}
              href={String(link)}
            >
              {linkText}
            </Link>
          )}
        </Box>
      </Box>
      {setActive && (
        <PastCurrentUpcoming
          isSuggestions={isSuggestions}
          style={{
            display: {
              xs: "flex",
              sm: "none",
            },
            justifyContent: "space-between",
            p: 2,
            my: 5,
          }}
          active={active}
          tabs={tabs}
          setActive={setActive}
        />
      )}
    </Box>
  );
};

export default SectionHeader;
