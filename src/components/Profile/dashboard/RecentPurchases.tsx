import { Box } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import SectionHeader from "./SectionHeader";
import PurchaseCard from "../PurchaseCard";
import { CartType } from "@/api/mongoTypes";
import EmptyList from "../EmptyInfo/EmptyList";
import useWindowDimensions from "@/hooks/useWindowDimension";
import { getEmptyPurchaseTitleAndDes } from "@/utils/dashboard";
import { purchaseDetailsType } from "@/types";
import useTranslate from "@/hooks/useTranslate";

const RecentPurchases = ({ purchases, setUserStats }) => {
  const [purchasesData, setPurchasesData] =
    useState<purchaseDetailsType[]>(purchases);
  const { width } = useWindowDimensions();
  const { translate } = useTranslate();

  const [renderArray, setRenderArray] = useState(4);

  useEffect(() => {
    setRenderArray(width > 900 ? 3 : 4);
  }, [width]);

  const { title, description } = getEmptyPurchaseTitleAndDes();

  return (
    <Box>
      <SectionHeader
        title={translate("dash.recent-purchases")}
        linkText={translate("dash.see-all")}
        isListEmpty={purchasesData.length === 0}
        link="/profile/mypurchases"
      />
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          width: "100%",
          p: 1,
          gap: 5,
          alignItems: "center",
          // justifyContent: "space-between",
          overflowX: "scroll",
          "&::-webkit-scrollbar": { display: "none" },
          scrollbarWidth: "none",
          msOverflowStyle: "none",
        }}
      >
        {purchasesData.length > 0 ? (
          <>
            {purchasesData.slice(0, renderArray)?.map((m, i) => (
              <Box
                sx={{
                  width: {
                    xs: 275,
                    // xs: "100%",
                    sm: "48%",
                    md: "32%",
                  },
                  minWidth: {
                    xs: 275,
                    // xs: "100%",
                    sm: "48%",
                    md: "32%",
                  },
                  mr: {
                    xs: 2,
                    sm: 0,
                  },
                }}
                key={i}
              >
                <PurchaseCard
                  index={i}
                  setPurchasesData={setPurchasesData}
                  purchaseDetails={m}
                  isDashboard
                  onUnSubscribe={() => {
                    setUserStats((prev) => {
                      const onlineClubsCount = prev.onlineClubsCount ?? 0;
                      return {
                        ...prev,
                        onlineClubsCount: onlineClubsCount - 1,
                      };
                    });
                  }}
                />
              </Box>
            ))}
          </>
        ) : (
          <EmptyList
            title={translate(title)}
            description={translate(description)}
            isSmall
          />
        )}
      </Box>
    </Box>
  );
};

export default RecentPurchases;
