import React from "react";
import { Typography } from "@mui/material";
import Image from "next/image";
import CustomButton from "../CustomButton";
import { useRouter } from "next/router";
import Link from "next/link";
import useTranslate from "@/hooks/useTranslate";

const links = [
  {
    id: 1,
    name: "instagram",
    link: "/images/icons/instagram.svg",
    href: process.env.NEXT_PUBLIC_INSTA_URL,
  },
  {
    id: 2,
    name: "spotify",
    link: "/images/icons/spotify.svg",
    href: process.env.NEXT_PUBLIC_SPOTIFY_URL,
  },
  {
    id: 3,
    name: "facebook",
    link: "/images/icons/facebook.svg",
    href: process.env.NEXT_PUBLIC_FB_URL,
  },
].filter((f) => f.href);

type CongratulationsProps = {};

const Congratulations: React.FC<CongratulationsProps> = ({}) => {
  const router = useRouter();
  const { translate } = useTranslate();
  const handleContinue = () => {
    router.push("/");
  };

  return (
    <div>
      <Typography
        variant="h2"
        component="div"
        sx={{
          textAlign: "center",
          fontWeight: {
            xs: "600",
            sm: "500",
          },
          marginTop: "5rem",
          fontSize: { xs: "1.75rem", sm: "3rem", md: "3.5rem" },
        }}
      >
        {translate("kyc.congratulations")}
      </Typography>
      <Typography
        variant="h2"
        component="div"
        sx={{
          textAlign: "center",
          fontWeight: "400",
          marginTop: "1.5rem",
          color: "rgba(67, 67, 67, 1)",
          fontSize: { xs: "0.85rem", sm: "1rem", md: "1.3rem" },
        }}
      >
        {translate("kyc.welcome-community")}
      </Typography>
      <Typography
        variant="h2"
        component="div"
        sx={{
          color: "rgba(67, 67, 67, 1)",
          marginTop: "1.5rem",
          textAlign: "center",
          fontWeight: "600",
          fontSize: { xs: "0.95rem", sm: "1rem", md: "1.2rem" },
        }}
      >
        {translate("kyc.get-started")}
      </Typography>
      {links.length > 0 && (
        <>
          <Typography
            variant="h2"
            component="div"
            sx={{
              color: "rgba(67, 67, 67, 1)",
              marginTop: "1.5rem",
              marginBottom: "1rem",
              textAlign: "center",
              fontWeight: "600",
              fontSize: {
                xs: "0.95rem",
                sm: "1rem",
                md: "1.2rem",
              },
            }}
          >
            {translate("kyc.follow-us")}
          </Typography>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              gap: "1.5rem",
              alignItems: "center",
              justifyContent: "center",
              marginTop: "1rem",
            }}
          >
            {links.map((m, i) => (
              <span key={i} style={{ cursor: "pointer" }}>
                <Link
                  href={m.href}
                  passHref
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image
                    height={i === 1 ? 34 : 40}
                    width={i === 1 ? 34 : 40}
                    src={m.link}
                    alt={m.name}
                  />
                </Link>
              </span>
            ))}
          </div>
        </>
      )}
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          marginTop: "1.25rem",
          marginBottom: "1.25rem",
        }}
      >
        <CustomButton
          text={translate("common.continue")}
          sx={{
            width: {
              xs: "100%",
              sm: "50%",
              md: "33%",
            },
          }}
          onClick={handleContinue}
        />
      </div>
    </div>
  );
};

export default Congratulations;
