import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Card,
  CardA<PERSON>,
  CardContent,
  Container,
  Typography,
} from "@mui/material";
import Image from "next/image";
import axios from "axios";
import Head from "next/head";
import { INTEREST_TYPES } from "@/constant/Enums";
import { interestStateTypes, InterestTypeWithpreference } from "@/types";
import { useSnackbar } from "@/hooks/useSnackbar";
import CustomButton from "../CustomButton";
import Skeleton from "@mui/material/Skeleton";
import Stack from "@mui/material/Stack";
import InterestsIcon from "./InterestsIcon";
import useTranslate from "@/hooks/useTranslate";
import { getInterestsId } from "@/utils/profile";

const buttonStyle = {
  width: {
    xs: "100%",
    sm: "50%",
    lg: "33%",
  },
};

const buttonContainerStyle = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  gap: "0.75rem",
  width: "100%",
  marginTop: "2rem",
  marginBottom: "2rem",
  flexWrap: {
    xs: "wrap",
    sm: "nowrap",
  },
};

export const InterestCardStyle = {
  boxShadow: "2px 2px 7px 4px rgba(204, 204, 204, 0.54)",
  minHeight: {
    xs: "300px",
    sm: "380px",
    lg: "500px",
  },
  width: {
    xs: "240px",
    sm: "300px",
    lg: "375px",
  },
  height: "100%",
  textAlign: "center",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  borderRadius: "1rem",
  margin: "auto",
  marginTop: "1rem",
  padding: "1.5rem",
};

export const InterestImgCont = {
  display: "flex",
  position: "relative",
  alignItems: "center",
  justifyContent: "center",
  overflow: "hidden",
  height: {
    xs: "150px",
    sm: "200px",
    lg: "300px",
  },
  paddingLeft: "1rem",
  paddingRight: "1rem",
  width: "75%",
  borderRadius: {
    xs: "0.5rem",
    sm: "1rem",
    lg: "20px",
  },
};

type InterestSelectionProps = React.FC<{
  interests: interestStateTypes;
  selectedInterests: interestStateTypes;
  setInterests: React.Dispatch<React.SetStateAction<interestStateTypes>>;
  setSelectedInterests: React.Dispatch<
    React.SetStateAction<InterestTypeWithpreference>
  >;
  stageIx: number;
  setStageIx: React.Dispatch<React.SetStateAction<number>>;
  handleSubmit: (
    doLater?: boolean,
    interests?: InterestTypeWithpreference
  ) => void;
  isUpdating: boolean;
}>;

const InterestSelection: InterestSelectionProps = ({
  interests,
  setInterests,
  stageIx,
  setStageIx,
  setSelectedInterests,
  selectedInterests,
  handleSubmit,
  isUpdating,
}) => {
  const { showSnackbar } = useSnackbar();
  const { translate } = useTranslate();
  const [isLoading, setisLoading] = useState(true);
  const [currentSubjectIndex, setCurrentSubjectIndex] = useState(0);

  const handleSelection = (preference: string) => {
    const findInterest = interests.find((f, i) => i === currentSubjectIndex);

    if (findInterest) {
      const newInterest: InterestTypeWithpreference = {
        name: findInterest?.name,
        image: findInterest.image,
        _id: String(findInterest._id),
        preference,
      };
      if (currentSubjectIndex === interests.length - 1) {
        handleSubmit(false, newInterest);
      } else {
        setSelectedInterests(newInterest);
        setCurrentSubjectIndex(currentSubjectIndex + 1);
      }
    }
  };

  const handleContinue = () => {
    handleSubmit();
    // setStageIx(+stageIx + 1);
  };

  const handleDoItLater = () => {
    handleSubmit(true);
    // setInterests([]);
    // setSelectedInterests([]);
    // setStageIx(+stageIx + 1);
  };

  useEffect(() => {
    const fetchInterests = async () => {
      try {
        setisLoading(true);
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/utils/get-interest`
        );
        if (data.data) {
          const changedSubjects = data.data.map((m) => {
            return {
              ...m,
              preference: null,
            };
          });
          setInterests(changedSubjects);
        }
        setisLoading(false);
      } catch (error) {
        setisLoading(false);
        setInterests([]);
        showSnackbar(translate("kyc.fetchinterests-failed"), {
          type: "error",
        });
      }
    };
    fetchInterests();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) {
    return <InterestLoading />;
  }

  return (
    <>
      <Head>
        {interests?.map((subject) => (
          <link
            key={subject._id}
            rel="preload"
            href={subject.image}
            as="image"
          />
        ))}
      </Head>
      <div
        aria-disabled={isUpdating}
        style={{
          display: "flex",
          flexDirection: "column",
          width: "100%",
          height: "100%",
        }}
      >
        {currentSubjectIndex < interests.length ? (
          <Card sx={InterestCardStyle}>
            <Box
              style={{
                marginBottom: "1rem",
              }}
            >
              <Typography
                variant="h5"
                component="div"
                sx={{
                  textAlign: "end",
                  fontWeight: "600",
                  fontSize: {
                    xs: "0.55rem",
                    sm: "0.7rem",
                    md: "0.8rem",
                  },
                }}
              >
                {currentSubjectIndex + 1}/{interests.length}
              </Typography>
              <Typography
                variant="h2"
                component="h2"
                sx={{
                  textAlign: "center",
                  fontWeight: "600",
                  fontSize: {
                    xs: "1.6rem",
                    sm: "2rem",
                    lg: "3rem",
                  },
                  marginTop: {
                    sm: "0.5rem",
                    lg: "1rem",
                  },
                }}
              >
                {translate("kyc.interests")}
              </Typography>
              <Typography
                variant="h5"
                component="p"
                sx={{
                  textAlign: "center",
                  fontWeight: "600",
                  fontSize: {
                    xs: "0.60rem",
                    sm: "0.75rem",
                    lg: "0.95rem",
                  },
                  marginTop: "0.5rem",
                }}
              >
                {translate("kyc.experience-learning")}
              </Typography>
            </Box>
            <Container sx={InterestImgCont}>
              <Image
                src={interests[currentSubjectIndex].image}
                alt={`Subject ${currentSubjectIndex + 1}`}
                key={currentSubjectIndex}
                quality={75}
                fill
                objectFit="cover"
                priority
              />
            </Container>
            <Typography
              variant="h2"
              component="h2"
              sx={{
                textAlign: "center",
                fontWeight: "600",
                marginTop: {
                  xs: "1rem",
                  lg: "1.25rem",
                },
                marginBottom: "2rem",
                fontSize: {
                  xs: "1.1rem",
                  sm: "0.85rem",
                  md: "1rem",
                  lg: "1.75rem",
                },
              }}
            >
              {translate(getInterestsId(interests[currentSubjectIndex].name))}
            </Typography>
            <CardActions sx={{ justifyContent: "center" }}>
              <ReactionButton
                type={INTEREST_TYPES.HATE}
                onClick={() => handleSelection(INTEREST_TYPES.HATE)}
              />
              <ReactionButton
                type={INTEREST_TYPES.NEUTRAL}
                onClick={() => handleSelection(INTEREST_TYPES.NEUTRAL)}
              />
              <ReactionButton
                type={INTEREST_TYPES.LOVE}
                onClick={() => handleSelection(INTEREST_TYPES.LOVE)}
              />
            </CardActions>
          </Card>
        ) : (
          <Typography textAlign="center" variant="h5" component="div">
            {translate("kyc.interests-select")}
          </Typography>
        )}

        <Container sx={buttonContainerStyle}>
          <CustomButton
            text={translate("common.do-later")}
            onClick={handleDoItLater}
            colortype="secondary"
            sx={buttonStyle}
            disabled={isUpdating}
          />
          <CustomButton
            text={translate("common.continue")}
            disabled={isUpdating}
            sx={buttonStyle}
            onClick={() => {
              if (
                selectedInterests.length === interests.length - 1 &&
                currentSubjectIndex === interests.length - 1
              ) {
                showSnackbar(translate("kyc.interests-select-please"), {
                  type: "warning",
                });
              } else if (selectedInterests.length < interests.length) {
                showSnackbar(translate("kyc.interests-select-remaining"), {
                  type: "warning",
                });
              } else {
                handleContinue();
              }
            }}
          />
        </Container>
      </div>
    </>
  );
};

const SkeletonStyle = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  width: "100%",
};

const InterestLoading = () => {
  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        height: "100%",
      }}
    >
      <Container sx={InterestCardStyle}>
        <Container
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "flex-end",
            justifyContent: "flex-end",
            width: "100%",
          }}
        >
          <Skeleton variant="rectangular" width={50} height={20} />
        </Container>

        <Container
          sx={{
            ...SkeletonStyle,
            marginTop: "0.5rem",
          }}
        >
          <Skeleton variant="rectangular" width="50%" height={60} />
        </Container>

        <Container
          sx={{
            ...SkeletonStyle,
            marginTop: "0.5rem",
          }}
        >
          <Skeleton variant="rectangular" width="70%" height={10} />
        </Container>

        <Container
          sx={{
            ...SkeletonStyle,
            marginTop: "0.25rem",
          }}
        >
          <Skeleton variant="rectangular" width="50%" height={10} />
        </Container>

        <Skeleton
          variant="rectangular"
          width="100%"
          height={150}
          style={{
            marginTop: "1rem",
          }}
        />
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            width: "100%",
            marginTop: "0.5rem",
          }}
        >
          <Skeleton variant="rectangular" width="70%" height={30} />
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            gap: "0.5rem",
            width: "100%",
            marginTop: "1rem",
          }}
        >
          <Skeleton variant="rectangular" width="25%" height={30} />
          <Skeleton variant="rectangular" width="25%" height={30} />
          <Skeleton variant="rectangular" width="25%" height={30} />
        </div>
      </Container>
      <Container sx={buttonContainerStyle}>
        <Skeleton
          sx={buttonStyle}
          variant="rectangular"
          width={210}
          height={60}
        />
        <Skeleton
          sx={buttonStyle}
          variant="rectangular"
          width={210}
          height={60}
        />
      </Container>
    </Container>
  );
};

const getProperties = (type = INTEREST_TYPES.LOVE) => {
  if (type === INTEREST_TYPES.HATE) {
    return {
      name: "kyc.hate",
      bgColor: "#C00F0CB2",
    };
  } else if (type === INTEREST_TYPES.NEUTRAL) {
    return {
      name: "kyc.neutral",
      bgColor: "#B3B3B3",
    };
  }
  return {
    name: "kyc.love",
    bgColor: "#F9B238B2",
  };
};

const ReactionButton = ({ type = INTEREST_TYPES.LOVE, ...props }) => {
  const { name, bgColor } = getProperties(type);
  const { translate } = useTranslate();
  return (
    <Button
      {...props}
      sx={{
        background: bgColor,
        borderRadius: "1.5rem",
        margin: "0px",
        textTransform: "none",
        fontWeight: "500",
        width: {
          xs: "75px",
          lg: "100px",
        },
        fontSize: {
          xs: "10px",
          lg: "15px",
        },
        paddingTop: {
          xs: "3px",
          lg: "5px",
        },
        paddingRight: {
          xs: "4px",
          lg: "8px",
        },
        "&:hover": {
          backgroundColor: bgColor,
        },
      }}
    >
      {translate(name)}
      &nbsp;
      <InterestsIcon type={type} />
    </Button>
  );
};

export default InterestSelection;
