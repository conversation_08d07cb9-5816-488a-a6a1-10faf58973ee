import { Box, Skeleton } from "@mui/material";
import React from "react";

const MyPurchasesLoading = () => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      flexWrap="wrap"
      mt={10}
    >
      {new Array(9).fill("").map((m, i) => (
        <PurchaseLoadingCard key={i} />
      ))}
    </Box>
  );
};

export default MyPurchasesLoading;

const PurchaseLoadingCard = () => {
  return (
    <Box
      sx={{
        mb: 2,
        overflow: "hidden",
        width: {
          xs: "100%",
          sm: "48%",
          md: "33%",
        },
        p: 1,
      }}
    >
      <Box
        p={3}
        sx={{
          boxShadow: "0px 1px 10px 0px rgba(0, 0, 0, 0.1)",
          height: 160,
          borderRadius: 2,
        }}
      >
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <Skeleton variant="rectangular" width="100%" height={80} />
        </Box>
        <Box display="flex" flexDirection="row" justifyContent="space-between">
          <Skeleton
            variant="rectangular"
            sx={{ marginTop: "0.5rem" }}
            width="40%"
            height={20}
          />
          <Skeleton
            variant="rectangular"
            sx={{ marginTop: "0.5rem" }}
            width="40%"
            height={20}
          />
        </Box>
        <Box
          display="flex"
          flexDirection="row"
          sx={{ marginTop: "0.5rem" }}
          alignItems="center"
          justifyContent="space-between"
        >
          <Skeleton variant="rectangular" width="100%" height={20} />
        </Box>
      </Box>
    </Box>
  );
};
