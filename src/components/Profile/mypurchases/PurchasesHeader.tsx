import SearchInput from "@/components/SearchInput";
import { Box, Typography } from "@mui/material";
import React, { useMemo, useState } from "react";
import SortingFilter from "../CommonFilters/SortingFilter";
import ClassesFilter from "../CommonFilters/ClassesFilter";
import { getClassessFilters } from "@/utils/classes";

const categories = [
  {
    id: 1,
    name: "Activated",
  },
  {
    id: 2,
    name: "Not Active",
  },
  {
    id: 3,
    name: "Expired",
  },
  {
    id: 4,
    name: "Booked",
  },
  {
    id: 5,
    name: "Attended",
  },
];
type PurchasesHeaderProps = React.FC<{
  search: string;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
  selectedCategory: string;
  setSelectedCategory: React.Dispatch<React.SetStateAction<string>>;
  sort: string;
  setSort: React.Dispatch<React.SetStateAction<string>>;
  classesSelected: string[];
  setClassesSelected: React.Dispatch<React.SetStateAction<string[]>>;
  handleSearch: () => void;
  isLoading: boolean;
}>;

const PurchasesHeader: PurchasesHeaderProps = ({
  selectedCategory,
  setSelectedCategory,
  sort,
  setSort,
  classesSelected,
  setClassesSelected,
  search,
  setSearch,
  handleSearch,
  isLoading,
}) => {
  return (
    <Box aria-disabled={isLoading}>
      <Box
        my={5}
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          flexDirection: {
            xs: "column",
            sm: "row",
          },
          // flexWrap: "wrap",
        }}
      >
        <SearchInput
          searchValue={search}
          setSearchValue={setSearch}
          handleSearch={handleSearch}
          handleClear={() => {
            setSearch("");
          }}
          disabled={isLoading}
          placeholder="Find the perfect course or instructor"
          style={{
            width: {
              xs: "100%",
            },
          }}
        />
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          gap={2}
          sx={{
            width: {
              xs: "100%",
            },
            justifyContent: {
              xs: "center",
              sm: "end",
            },
            mt: {
              xs: 3,
            },
          }}
        >
          <ClassesFilter
            classesSelected={classesSelected}
            setClassesSelected={setClassesSelected}
            filtersTabs={getClassessFilters({
              needClub: true,
              needInPerson: true,
              needCommunityExperience: true,
              needOnline: true,
            })}
          />
          <SortingFilter sort={sort} setSort={setSort} />
        </Box>
      </Box>
      {/* <Box display="flex" flexDirection="row" gap={3} mb={5}>
        {categories.map((m) => (
          <CategoryPill
            name={m.name}
            key={m.id}
            isActive={m.name === selectedCategory}
            onClick={() => {
              setSelectedCategory(m.name);
            }}
          />
        ))}
      </Box> */}
    </Box>
  );
};

type CategoryPillProps = React.FC<{
  isActive: boolean;
  name: string;
  onClick: () => void;
}>;
const CategoryPill: CategoryPillProps = ({ isActive, name, onClick }) => {
  const { color, backgroundColor, borderColor } = useMemo(() => {
    if (isActive) {
      return {
        color: "#fff",
        backgroundColor: "rgba(20, 167, 156, 1)",
        borderColor: "rgba(20, 167, 156, 1)",
      };
    }
    return {
      borderColor: "rgba(179, 179, 179, 1)",
      backgroundColor: "#fff",
      color: "",
    };
  }, [isActive]);

  return (
    <Box
      onClick={onClick}
      sx={{
        backgroundColor,
        border: "1px solid",
        borderColor: borderColor,
        borderRadius: 5,
        width: "max-content",
        padding: "3px 12px",
        cursor: "pointer",
      }}
    >
      <Typography color={color} fontSize={13}>
        {name}
      </Typography>
    </Box>
  );
};

export default PurchasesHeader;
