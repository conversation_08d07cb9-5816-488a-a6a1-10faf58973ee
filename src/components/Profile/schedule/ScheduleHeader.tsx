import { Box } from "@mui/material";
import React from "react";
import ClassesFilter from "../CommonFilters/ClassesFilter";
import SortingFilter from "../CommonFilters/SortingFilter";
import OnlyMyClassessToggle from "../OnlyMyClassessToggle";
import PastCurrentUpcoming from "../PastCurrentUpcoming";
import { CLASSES_SORT } from "@/constant/Enums";

type ScheduleHeaderProps = React.FC<{
  sort: string;
  setSort: React.Dispatch<React.SetStateAction<string>>;
  classesSelected: any[];
  setClassesSelected: React.Dispatch<React.SetStateAction<any[]>>;
  setActive: React.Dispatch<React.SetStateAction<number>>;
  active: number;
  isLoading: boolean;
  showTabs: boolean;
  isMyClassesOnly?: boolean;
  setIsMyClassessOnly?: React.Dispatch<React.SetStateAction<boolean>>;
  tabs?: {
    id: number;
    name: string;
  }[];
  filtersTabs?: {
    id: number;
    value: CLASSES_SORT;
    name: string;
  }[];
}>;

const ScheduleHeader: ScheduleHeaderProps = ({
  sort,
  setSort,
  classesSelected,
  setClassesSelected,
  isLoading,
  isMyClassesOnly,
  setIsMyClassessOnly,
  setActive,
  active,
  tabs,
  filtersTabs,
  showTabs = false,
}) => {
  return (
    <Box
      aria-disabled={isLoading}
      display="flex"
      flexDirection="column"
      width="100%"
    >
      <Box
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        alignItems="center"
        width="100%"
        flexWrap="wrap"
        gap={2}
      >
        {setIsMyClassessOnly ? (
          <OnlyMyClassessToggle
            isMyClassesOnly={isMyClassesOnly}
            setIsMyClassessOnly={setIsMyClassessOnly}
          />
        ) : (
          <>
            {showTabs && (
              <PastCurrentUpcoming
                style={{ width: "fit-content", mt: 3 }}
                showAll
                setActive={setActive}
                active={active}
                tabs={tabs}
              />
            )}
          </>
        )}
        <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
          <ClassesFilter
            filtersTabs={filtersTabs}
            classesSelected={classesSelected}
            setClassesSelected={setClassesSelected}
          />
          {/* <SortingFilter sort={sort} setSort={setSort} /> */}
        </Box>
      </Box>
      {setIsMyClassessOnly && showTabs && (
        <PastCurrentUpcoming
          style={{ width: "fit-content", mt: 3 }}
          showAll
          setActive={setActive}
          active={active}
          tabs={tabs}
        />
      )}
    </Box>
  );
};

export default ScheduleHeader;
