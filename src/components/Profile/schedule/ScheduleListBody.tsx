import { CartType } from "@/api/mongoTypes";
import PageHeaderAndContainer from "@/components/PageHeaderAndContainer";
import ClassCardListLoading from "@/components/Profile/ClassCard/ClassCardListLoading";
import EmptyList from "@/components/Profile/EmptyInfo/EmptyList";
import Calender from "@/components/Profile/schedule/Calender";
import ScheduleCard from "@/components/Profile/schedule/ScheduleCard";
import ScheduleHeader from "@/components/Profile/schedule/ScheduleHeader";
import { CLASSESS_FETCH_DURATION, SORT } from "@/constant/Enums";
import { useUserContext } from "@/contexts/UserContext";
import useTranslate from "@/hooks/useTranslate";
import {
  getClassessFetchTabs,
  getClassessFilters,
  getStatusBasedOnSelectedDate,
} from "@/utils/classes";
import { getLocalUser } from "@/utils/common";
import { getEmptyClassesTitleAndDesc } from "@/utils/dashboard";
import { formatDate, getDateAsPerUTC } from "@/utils/dateTime";
import axiosInstance from "@/utils/interceptor";
import { groupDataByDate } from "@/utils/listLogic";
import { Box, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

const LIMIT = 10;

type ScheduleListBodyProps = React.FC<{
  isSuggestion?: boolean;
  showMyClassessToggle: boolean;
  headerTitle: string;
}>;

const ScheduleListBody: ScheduleListBodyProps = ({
  showMyClassessToggle,
  headerTitle,
  isSuggestion = false,
}) => {
  const { translate } = useTranslate();
  const [active, setActive] = useState<CLASSESS_FETCH_DURATION>(2);
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<CartType[] | []>([]);
  const [sort, setSort] = useState<SORT>(SORT.ASCENDING);
  const [skip, setSkip] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [classesSelected, setClassesSelected] = useState([]);
  const [isMyClassesOnly, setIsMyClassessOnly] = useState(true);
  const [selectedDate, setSelectedDate] = useState(null);
  const { dbUser } = useUserContext();
  const userId = dbUser?._id;

  console.log("data", data);

  const fetchSchedules = async ({
    skip,
    sort,
    classesSelected,
    isMyClassesOnly,
    date,
    userId,
    isSuggestion,
  }) => {
    try {
      if (skip === 0) {
        setIsLoading(true);
        setData([]);
      }
      const isAscending = sort === "asc";
      const selectedDate = getDateAsPerUTC(date);
      const params = {
        skip: skip,
        limit: LIMIT,
        isAscending,
        filters: classesSelected.join(","),
        isMyClassesOnly: isSuggestion ? false : isMyClassesOnly,
        // timeFilter: active,
        isSuggestions: isSuggestion,
        timeFilter: selectedDate ? CLASSESS_FETCH_DURATION.CURRENT : active,
        date: selectedDate,
        currentDate: new Date(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        // currentDate: getDateAsPerUTC(new Date()),
      };
      if (!date) {
        delete params.date;
      } else {
        setActive(CLASSESS_FETCH_DURATION.CURRENT);
      }
      const { data: respData } = await axiosInstance.get(
        "schedule/my-schedules",
        {
          params,
          headers: {
            userid: userId,
          },
        }
      );
      if (respData.data && respData.success) {
        const groupedData = groupDataByDate({
          data: respData?.data,
          active,
          isAscending,
          baseDate: getDateAsPerUTC(date),
        }) as any[];
        setData((prev) => [...prev, ...groupedData]);
        setSkip(+skip + LIMIT);
        setHasMore(+respData.nextSkipCount >= LIMIT);
      } else {
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      setData([]);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchSchedules({
        skip: 0,
        sort,
        classesSelected,
        isMyClassesOnly,
        date: selectedDate,
        userId,
        isSuggestion,
      });
    }
  }, [
    sort,
    classesSelected,
    isMyClassesOnly,
    isSuggestion,
    active,
    selectedDate,
    userId,
  ]);

  const { title, description } = useMemo(() => {
    return getEmptyClassesTitleAndDesc({ type: active });
  }, [active]);

  return (
    <PageHeaderAndContainer title={headerTitle}>
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: {
            xs: "column-reverse",
            md: "row",
          },
          gap: 4,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            width: {
              xs: "100%",
              md: "70%",
            },
          }}
        >
          <ScheduleHeader
            sort={sort}
            setSort={setSort}
            setIsMyClassessOnly={
              showMyClassessToggle ? setIsMyClassessOnly : null
            }
            isMyClassesOnly={showMyClassessToggle ? isMyClassesOnly : null}
            classesSelected={classesSelected}
            setClassesSelected={setClassesSelected}
            isLoading={isLoading}
            setActive={setActive}
            active={active}
            showTabs={!selectedDate}
            filtersTabs={getClassessFilters({
              needClub: false,
              needInPerson: true,
              needCommunityExperience: true,
              needOnline: false,
            })}
            tabs={getClassessFetchTabs({
              needPast: isSuggestion ? false : true,
              needAll: isSuggestion ? false : true,
              needCurrent: true,
              needUpcoming: true,
            })}
          />
          {data.length === 0 && !isLoading ? (
            <EmptyList
              title={translate(title)}
              description={translate(description)}
            />
          ) : isLoading ? (
            <ClassCardListLoading />
          ) : (
            <InfiniteScroll
              style={{
                width: "100%",
              }}
              dataLength={data.length}
              next={() =>
                fetchSchedules({
                  skip,
                  sort,
                  classesSelected,
                  isMyClassesOnly,
                  date: selectedDate,
                  userId,
                  isSuggestion,
                })
              }
              hasMore={hasMore}
              loader={<p style={{ textAlign: "center" }}>Loading</p>}
              endMessage={
                <p style={{ textAlign: "center" }}>
                  <b>{translate("common.seen-all")}</b>
                </p>
              }
            >
              <Box p={2}>
                {data.map((m, i) => {
                  if (m.date) {
                    return (
                      <>
                        <Typography
                          mt={4}
                          fontSize={12}
                          fontWeight={700}
                          color="#3C3C3C"
                        >
                          {formatDate({
                            returnWeekday: true,
                            date: new Date(m.date),
                          })}
                          ,
                          {formatDate({
                            inText: true,
                            date: new Date(m.date),
                          })}
                        </Typography>
                        {m.data.map((j, i) => (
                          <ScheduleCard
                            active={active}
                            selectedDate={selectedDate}
                            info={j}
                            key={i}
                            cardDate={m.date}
                          />
                        ))}
                      </>
                    );
                  } else {
                    return (
                      <>
                        <ScheduleCard
                          active={active}
                          selectedDate={selectedDate}
                          info={m}
                          key={i}
                          cardDate={m.date}
                        />
                      </>
                    );
                  }
                })}
              </Box>
            </InfiniteScroll>
          )}
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            width: {
              xs: "100%",
              md: "30%",
            },
          }}
        >
          <Calender
            onChange={(date) => {
              if (date) {
                setActive(CLASSESS_FETCH_DURATION.CURRENT);
              }
            }}
            setSelectedDate={setSelectedDate}
            selectedDate={selectedDate}
            userId={userId}
            isSuggestion={isSuggestion}
          />
        </Box>
      </Box>
    </PageHeaderAndContainer>
  );
};

export default ScheduleListBody;
