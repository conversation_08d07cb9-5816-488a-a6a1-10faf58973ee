import React, { useEffect, useState } from "react";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import { Box, Typography } from "@mui/material";
import axiosInstance from "@/utils/interceptor";
import dayjs from "dayjs";
import { USER_TIMEZONE } from "@/utils/dateTime";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { getEventDates } from "@/utils/listLogic";
import { ClassesTypesPills } from "@/constant/classes";
import useTranslate from "@/hooks/useTranslate";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault(USER_TIMEZONE);

type CalendarProps = React.FC<{
  selectedDate: any;
  setSelectedDate: React.Dispatch<any>;
  userId: string;
  isSuggestion?: boolean;
  onChange: (date: Date) => void;
}>;
const Calendar: CalendarProps = ({
  selectedDate,
  setSelectedDate,
  userId,
  isSuggestion,
  onChange,
}) => {
  const [currentMonth, setCurrentMonth] = useState(null);
  const [isLoading, setLoading] = useState(false);
  const [eventData, setEventData] = useState({});
  const [mounted, setMounted] = useState(false);
  // const disablePast = false;
  const disablePast = isSuggestion;

  const { preferredLanguage } = useTranslate();
  console.log("selectedDate", selectedDate);

  useEffect(() => {
    setMounted(true);
    setCurrentMonth(dayjs().month() + 1);
  }, []);

  useEffect(() => {
    if (!currentMonth) return;

    const fetchCalenderInfo = async () => {
      try {
        setLoading(true);
        const { data: respData } = await axiosInstance.get(
          "user/get-month-data",
          {
            params: {
              monthNumber: currentMonth,
              isSuggestion,
            },
            headers: {
              userid: userId,
            },
          }
        );
        if (respData.data) {
          const list = getEventDates(respData.data);
          setEventData(list);
        }
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
    };

    if (userId) {
      fetchCalenderInfo();
    }
  }, [currentMonth, userId, isSuggestion]);

  const handleMonthChange = (newMonth) => {
    const monthNumber = newMonth.month() + 1;
    setCurrentMonth(monthNumber);
  };

  const handleDateChange = (newDate) => {
    const isSelectedSame = selectedDate && newDate.isSame(selectedDate, "day");
    const isCurrentDate = newDate.isSame(new Date(), "day");

    if (isSelectedSame || isCurrentDate) {
      setSelectedDate(null);
      onChange(null);
      console.log("Deselected Date");
    } else {
      onChange(newDate);
      setSelectedDate(newDate);
      console.log("Selected Date:", newDate.format("YYYY-MM-DD"));
    }
  };

  const CustomDay = (props) => {
    const { day, selected, onDaySelect, ...other } = props;
    const dateStr = day.format("YYYY-MM-DD");
    const markers = eventData[dateStr] || [];
    const isToday = day.isSame(dayjs(), "day");
    const isSelected = selectedDate?.isSame(day, "day");

    const LIMIT = 2;
    const extra = markers.length - LIMIT;

    return (
      <Box
        {...other}
        onClick={() => {
          onDaySelect?.(day);
        }}
        aria-disabled={disablePast ? day.isBefore(dayjs(), "day") : false}
        sx={{
          position: "relative",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flexDirection: "column",
          width: 40,
          height: 45,
          borderRadius: "50%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "column",
            cursor: "pointer",
            borderRadius: "50%",
            width: 30,
            height: 30,
            backgroundColor: isSelected
              ? "#1976d2"
              : isToday
              ? "#02B199"
              : "transparent",
          }}
        >
          <Typography
            sx={{
              fontSize: 14,
              color: isToday || isSelected ? "#fff" : "inherit",
              fontWeight: isSelected ? "bold" : "normal",
            }}
          >
            {day.date()}
          </Typography>
        </Box>
        {markers.length > 0 && (
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              gap: 0.5,
            }}
          >
            {markers.slice(0, LIMIT).map((color, index) => (
              <Box
                key={index}
                sx={{
                  height: 6,
                  width: 6,
                  borderRadius: "50%",
                  backgroundColor: color,
                }}
              />
            ))}
            {extra > 0 && (
              <Typography color="#6D6D6D" fontSize={10}>
                + {extra}
              </Typography>
            )}
          </Box>
        )}
      </Box>
    );
  };

  if (!mounted) return null;

  return (
    <Box
      aria-disabled={isLoading}
      sx={{
        boxShadow: "0px 1px 10px 0px rgba(0, 0, 0, 0.1)",
        p: 2,
        borderRadius: 2,
      }}
    >
      <LocalizationProvider
        dateAdapter={AdapterDayjs}
        adapterLocale={preferredLanguage}
      >
        <DateCalendar
          onMonthChange={handleMonthChange}
          onChange={handleDateChange}
          value={selectedDate || null}
          disablePast={disablePast}
          slots={{
            day: CustomDay,
          }}
        />
      </LocalizationProvider>
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="start"
        flexWrap="wrap"
      >
        {ClassesTypesPills.map((m) => (
          <Box
            key={m.id}
            display="flex"
            flexDirection="row"
            alignItems="center"
            gap={1}
            mr={2}
            sx={{
              border: `1px solid #EAEAEA`,
              borderRadius: 10,
              px: 2,
              mb: 1,
            }}
          >
            <span
              style={{
                height: 6,
                width: 6,
                borderRadius: 5,
                background: m.color,
                display: "flex",
              }}
            />
            <Typography fontSize={12}>{m.name}</Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default Calendar;
