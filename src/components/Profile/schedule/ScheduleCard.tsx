import React, { useMemo, useState } from "react";
import { Box, Typography } from "@mui/material";
import CreatorDetails from "@/components/classes/OnlineClub/CreatorDetails";
import Image from "next/image";
import ClassTag from "../ClassCard/ClassTag";
import ClassCardLocation from "../ClassCard/ClassCardLocation";
import DateAndLevel from "../ClassCard/DateAndLevel";
import {
  getClassesImage,
  getClassesLocation,
  getClassStatus,
  getClassTitleAndSubtitle,
  getClassTypeAndSubType,
  getCreatorDetails,
} from "@/utils/classes";
import ClassCardModal from "../ClassCard/ClassCardModal";
import useWindowDimensions from "@/hooks/useWindowDimension";
import ClassStatus from "../ClassStatus";
import { MySingleScheduleResponse } from "@/types";
import { CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import useTranslate from "@/hooks/useTranslate";

type ScheduleCardProps = React.FC<{
  info: MySingleScheduleResponse;
  isSmaller?: boolean;
  selectedDate?: Date;
  cardDate?: Date;
  active: CLASSESS_FETCH_DURATION;
}>;
const ScheduleCard: ScheduleCardProps = ({
  info,
  isSmaller = false,
  selectedDate,
  active,
  cardDate,
}) => {
  const data = info;
  const { translate } = useTranslate();
  const [open, setOpen] = useState(false);
  const { width } = useWindowDimensions();

  const imgSrc = useMemo(() => {
    return getClassesImage({ purchaseDetails: data });
  }, [data]);

  const { title } = useMemo(() => {
    return getClassTitleAndSubtitle({ cartData: data });
  }, [data]);

  const { type, subType } = useMemo(
    () => getClassTypeAndSubType({ cartData: data }),
    [data]
  );

  const location = useMemo(() => {
    return getClassesLocation({
      cartData: data,
    });
  }, [data]);

  const creatorDetails = useMemo(() => {
    return getCreatorDetails({ cartData: data });
  }, [data]);

  const status = useMemo(() => {
    return getClassStatus({ data, selectedDate, active, cardDate });
  }, [data, selectedDate, active, cardDate]);

  console.log({
    status,
    cardDate,
  });

  const makeUseSmaller = isSmaller || width < 600;

  return (
    <>
      <Box
        sx={{
          boxShadow: "0px 1px 10px 0px rgba(0, 0, 0, 0.1)",
          p: 3,
          mt: 2,
          borderRadius: 2,
          cursor: "pointer",
          position: "relative",
        }}
        onClick={() => {
          setOpen(true);
        }}
      >
        {status && <ClassStatus status={status} />}
        {makeUseSmaller && (
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="start"
            gap={2}
          >
            {imgSrc ? (
              <Image
                style={{ borderRadius: 5 }}
                height={50}
                width={50}
                src={imgSrc}
                alt="class"
              />
            ) : (
              <Box
                sx={{
                  height: 50,
                  width: 50,
                  borderRadius: 5,
                  background: "gray",
                }}
              />
            )}
            <Box>
              <Typography fontWeight={800} fontSize={20}>
                {title}
              </Typography>
              {creatorDetails?.length > 0 && (
                <CreatorDetails
                  creatorDetails={creatorDetails}
                  isSmall
                  mt={1}
                />
              )}
            </Box>
          </Box>
        )}

        <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
          <Box
            sx={{
              width: makeUseSmaller
                ? 0
                : {
                    xs: "30%",
                    sm: "20%",
                  },
              minHeight: makeUseSmaller ? 0 : 120,
              position: "relative",
            }}
          >
            <Image
              style={{
                overflow: "hidden",
                borderRadius: 5,
              }}
              alt="class"
              src={imgSrc}
              fill
            />
          </Box>
          <Box
            sx={{
              width: makeUseSmaller
                ? "100%"
                : {
                    xs: "70%",
                    sm: "80%",
                  },
            }}
          >
            {!makeUseSmaller && (
              <Box>
                <Typography fontWeight={800} fontSize={20}>
                  {title}
                </Typography>
                {creatorDetails?.length > 0 && (
                  <CreatorDetails
                    creatorDetails={creatorDetails}
                    isSmall
                    mt={1}
                  />
                )}
              </Box>
            )}
            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              justifyContent="space-between"
            >
              <ClassTag
                textOne={`${type} ${subType && ":"}`}
                textTwo={subType}
              />
              {!data?.isBought && (
                <Box
                  sx={{
                    color: "#000",
                    background: "rgba(226, 226, 226, 1)",
                    borderRadius: 1,
                    width: "fit-content",
                    padding: "2px 8px",
                  }}
                >
                  <Typography fontSize="0.7rem" fontWeight={700}>
                    {translate("dash.suggested")}
                  </Typography>
                </Box>
              )}
            </Box>
            {location && (
              <ClassCardLocation
                fontSize={12}
                color="rgba(109, 109, 109, 1)"
                location={location}
              />
            )}
            <DateAndLevel
              active={active}
              isDashboard={false}
              cardDate={cardDate}
              data={data}
            />
          </Box>
        </Box>
      </Box>
      <ClassCardModal
        isRestricted={Boolean(!data?.isBought)}
        data={data}
        open={open}
        cardDate={cardDate}
        setOpen={setOpen}
      />
    </>
  );
};

export default ScheduleCard;
