import useTranslate from "@/hooks/useTranslate";
import { MenuItem, Select, Box } from "@mui/material";
import Image from "next/image";

const LanguageSwitcher = () => {
  const { preferredLanguage, setPreferredLanguage } = useTranslate();

  const handleChange = (event) => {
    setPreferredLanguage(event.target.value);
  };

  return (
    <Box sx={{ minWidth: 120 }}>
      <Select
        value={preferredLanguage}
        onChange={handleChange}
        variant="standard"
        disableUnderline
        size="small"
        sx={{
          bgcolor: "transparent",
          border: "none",
          borderRadius: 2,
          "& .MuiSelect-select": {
            display: "flex",
            alignItems: "center",
          },
        }}
      >
        <MenuItem value="en">
          <Image
            src="/flags/us.svg"
            alt="English"
            width={20}
            height={15}
            style={{ marginRight: 8 }}
          />
          English
        </MenuItem>
        <MenuItem value="es">
          <Image
            src="/flags/es.svg"
            alt="Español"
            width={20}
            height={15}
            style={{ marginRight: 8 }}
          />
          Español
        </MenuItem>
      </Select>
    </Box>
  );
};

export default LanguageSwitcher;
