import update from "immutability-helper";
import { useCallback, useEffect, useState } from "react";
import { useDrag, useDrop } from "react-dnd";
import { Button } from "@mui/material";
import { BaseModal } from "@/components/Modals/BaseModal";
import { DraggableBox } from "./DraggableBox.js";
import { ItemTypes } from "./ItemTypes.js";
import { snapToGrid as doSnapToGrid } from "./snapToGrid.js";
const styles = {
  width: 300,
  height: 300,
  border: "1px solid black",
  position: "relative",
};

const Sentences = [
  "This is a sentence.",
  "The sun rises in the east and sets in the west.",
  // "The cat chased the mouse around the garden.",
  // "I enjoy reading books on a rainy day.",
  // "She smiled warmly when she saw her old friend.",
  // "The tall oak tree swayed gently in the breeze.",
  // "The bright stars twinkled in the clear night sky.",
  // "Cooking is one of my favorite hobbies.",
  // "The children laughed as they played in the playground.",
  // "The smell of fresh flowers filled the air.",
  // "He wore a blue hat and a red scarf to stay warm.",
];
const AlertModal = ({ isOpen, isWinMode, handleClose }) => {
  const title = isWinMode ? "You Win!" : "You Lose!";
  return (
    <BaseModal title={title} isOpen={isOpen} handleClose={handleClose}>
      <p className="text-sm text-gray-500 dark:text-gray-300">
        {isWinMode ? "You have won the game!" : "You have lost the game!"}
      </p>
    </BaseModal>
  );
};
export const PetCard = ({ id, title }) => {
  const [{ isDragging }, dragRef] = useDrag({
    type: ItemTypes.BOX,
    item: { id, title },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  return (
    <div
      className="pet-card p-4 m-4 bg-yellow-300 rounded-md border-solid border-1 h-fit"
      ref={dragRef}
    >
      {title}
      {isDragging && "😱"}
    </div>
  );
};
export const Container = ({ snapToGrid }) => {
  const [basket, setBasket] = useState([]);
  const [isWin, setIsWin] = useState(false);
  const [words, setWords] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isShowSolution, setIsShowSolution] = useState(false);
  const [solution, setSolution] = useState("");
  const [originalWords, setOriginalWords] = useState([]);
  const [sentence, setSentence] = useState("");
  const boxItems = sentence.split(" ").map((word, index) => {
    return { id: index, title: word };
  });
  const [boxes, setBoxes] = useState({});
  const [isRestart, setIsRestart] = useState(false);
  const moveBox = useCallback(
    (id, left, top, title) => {
      let newBox;
      const index = Object.keys(boxes).findIndex((op) => op === id);
      if (index === -1) {
        const width = document.getElementById("drop-box-container").offsetWidth;
        const height =
          document.getElementById("drop-box-container").offsetHeight;
        newBox = {
          ...boxes,
          [id]: {
            top: Math.floor(Math.random() * (height - 100)),
            left: Math.floor(Math.random() * (width - 100)),
            title,
          },
        };
        setBasket((basket) => {
          return basket.filter((item) => item.id !== id);
        });
      } else {
        const _boxes = boxes ?? {};
        newBox = update(_boxes, {
          [index]: { $merge: { left, top, title } },
        });
      }
      setBoxes(newBox);
    },
    [boxes]
  );
  const [, boardDrop] = useDrop(
    () => ({
      accept: ItemTypes.BOX,
      drop(item, monitor) {
        const delta = monitor.getDifferenceFromInitialOffset();
        let left = Math.round(item.left + delta.x);
        let top = Math.round(item.top + delta.y);
        const title = item.title;
        if (snapToGrid) {
          [left, top] = doSnapToGrid(left, top);
        }
        moveBox(item.id, left, top, title);
        return undefined;
      },
    }),
    [moveBox]
  );
  useEffect(() => {
    if (basket.length && basket.length === originalWords.length) {
      if (checkWin(basket, originalWords)) {
        setIsWin(true);
      } else {
        setIsWin(false);
      }
      setIsOpen(true);
    }
  }, [basket, originalWords]);
  useEffect(() => {
    const width = document.getElementById("drop-box-container").offsetWidth;
    const height = document.getElementById("drop-box-container").offsetHeight;
    const initBoxes = boxItems.reduce((acc, box) => {
      acc[box.id] = {
        top: Math.floor(Math.random() * (height - 100)),
        left: Math.floor(Math.random() * (width - 100)),
        title: box.title,
      };
      return acc;
    }, {});
    setBoxes(initBoxes);

    const _words = sentence
      .split(" ")
      .map((word, index) => ({ id: index, title: word }));
    setOriginalWords(_words);
  }, [sentence]);

  useEffect(() => {
    const _sentence = Sentences[Math.floor(Math.random() * Sentences.length)];
    setSentence(_sentence);
  }, [isRestart]);

  const checkWin = (basket, originalWords) => {
    if (basket.length && basket.length === originalWords.length) {
      return !originalWords.some(
        (word, index) => word.title !== basket[index].title
      );
    }
    return false;
  };
  const handleRestart = () => {
    setIsRestart(!isRestart);
    setIsShowSolution(false);
    setBasket([]);
    setSolution("");
  };
  const onShowSolution = () => {
    setIsShowSolution(true);
    setSolution(sentence);
  };
  const [{ isOver }, busketDropRef] = useDrop({
    accept: ItemTypes.BOX,
    drop: (item) => {
      setBasket((basket) => {
        return !basket.map((item) => item.id).includes(item.id)
          ? [...basket, item]
          : basket;
      });
      setBoxes((boxes) => {
        return Object.keys(boxes).reduce((acc, key) => {
          if (key !== item.id) {
            acc[key] = boxes[key];
          }
          return acc;
        }, {});
      });
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });
  return (
    <div className="w-full p-4">
      <div
        id="drop-box-container"
        ref={boardDrop}
        className="w-full bg-yellow-200 h-fit"
        style={{ height: 300, border: "1px solid black" }}
      >
        {Object.keys(boxes).map((key) => (
          <DraggableBox key={key} id={key} {...boxes[key]} />
        ))}
      </div>

      <div
        className="mt-4 basket flex flex-wrap justify-center items-center h-fit bg-lime-100"
        style={{ minHeight: "10em" }}
        ref={busketDropRef}
      >
        {basket.map((pet) => (
          <PetCard key={pet.id} id={pet.id} title={pet.title} />
        ))}
        {basket.length === 0 && (
          <div className="pet-card p-4 m-4 bg-grey-100 rounded-md border-solid border-1 h-fit">
            Drop Here!
          </div>
        )}
      </div>
      <div className="flex grow flex-col justify-center pb-6 short:pb-2 mt-4">
        <div className="flex justify-center mb-4">
          <Button
            onClick={handleRestart}
            className="ml-2"
            variant="contained"
            color="primary"
          >
            Restart
          </Button>
          <Button
            onClick={onShowSolution}
            className="ml-2"
            variant="contained"
            color="primary"
          >
            Show Answer
          </Button>
        </div>

        <div className="flex justify-center mx-4">
          {isShowSolution && (
            <p className="text-green-400 text-4xl font-bold">{solution}</p>
          )}
        </div>
      </div>
      <AlertModal
        isOpen={isOpen}
        isWinMode={isWin}
        handleClose={() => {
          setIsOpen(!isOpen);
        }}
      />
    </div>
  );
};
