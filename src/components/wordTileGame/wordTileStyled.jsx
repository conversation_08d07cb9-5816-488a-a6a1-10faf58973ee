import React from "react";
import { styled } from "@mui/system";

export const Wrapper = styled("div")`
  width: ${(props) => props.width}px;
  height: ${(props) => props.height}px;
  position: relative;
  overflow-x: hidden;
  overscroll-behavior-y: none;
  overflow-y: hidden;
  touch-action: none;
  padding: 2px;
`;

export const BlockContainer = styled("div")`
  flex-grow: 2;
  justify-content: center;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  overscroll-behavior-y: none;
  overflow-y: hidden;
  touch-action: none;
`;
