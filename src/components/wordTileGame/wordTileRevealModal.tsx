import { BaseModal } from "@/components/Modals/BaseModal";
import React from "react";
import { REVEAL_MESSAGES } from "./wordTileConstants/wordTileMessages";

type Props = {
  isOpen: boolean;
  handleClose: () => void;
  answer: string;
  translation: string;
};

export const RevealModal = ({
  isOpen,
  handleClose,
  answer,
  translation,
}: Props) => {
  const randomIndex = Math.floor(Math.random() * REVEAL_MESSAGES.length);
  const randomMessage = REVEAL_MESSAGES[randomIndex];

  return (
    <BaseModal title={randomMessage} isOpen={isOpen} handleClose={handleClose}>
      <h2 className="text-2xl text-black">{answer}</h2>
      <h2 className="text-md text-gray-500 italic">{translation}</h2>
    </BaseModal>
  );
};
