import React from "react";
import { BaseDrawer } from "../BaseDrawer";
import { Typography, Button, List, Divider } from "@mui/material";

export const WordTileDrawer = ({
  isOpen,
  onClose,
  onOpen,
  levelOptions,
  typeOptions,
  handleTypeClick,
  handleLevelClick,
}) => {
  return (
    <>
      <BaseDrawer isOpen={isOpen} onClose={onClose} onOpen={onOpen}>
        <List sx={{ paddingTop: "50px" }}>
          <Typography
            sx={{
              fontWeight: "bold",
              fontSize: "1.5rem",
              margin: "10px 0 5px 25px",
            }}
          >
            Proficiency
          </Typography>
          {levelOptions.map((option, index) => (
            <Button
              key={index}
              onClick={() => handleLevelClick(option.value)}
              sx={{
                width: "100%",
                justifyContent: "flex-start",
                textTransform: "none",
                backgroundColor: "white",
                color: "#000000",
                borderTop: "1px solid #e0e0e0",
                fontSize: "1.2rem",
                padding: "15px 0 15px 25px",
                height: "auto",
                borderBottom: "none",
                borderRight: "none",
                borderLeft: "none",
                borderRadius: "0",
                boxSizing: "border-box",
                "&:last-child": {
                  borderBottom: "1px solid #e0e0e0",
                },
              }}
            >
              {option.label}
            </Button>
          ))}
          <Divider sx={{ margin: "10px 0", border: "none" }} />
          <Typography
            sx={{
              fontWeight: "bold",
              fontSize: "1.5rem",
              margin: "10px 0 5px 25px",
            }}
          >
            Phrase Type
          </Typography>
          {typeOptions.map((option, index) => (
            <Button
              key={index}
              onClick={() => handleTypeClick(option.value)}
              sx={{
                width: "100%",
                justifyContent: "flex-start",
                textTransform: "none",
                backgroundColor: "white",
                color: "#000000",
                borderTop: "1px solid #e0e0e0",
                fontSize: "1.2rem",
                padding: "15px 0 15px 25px",
                height: "auto",
                borderBottom: "none",
                borderRight: "none",
                borderLeft: "none",
                borderRadius: "0",
                boxSizing: "border-box",
                "&:last-child": {
                  borderBottom: "1px solid #e0e0e0",
                },
              }}
            >
              {option.label}
            </Button>
          ))}
        </List>
      </BaseDrawer>
    </>
  );
};
