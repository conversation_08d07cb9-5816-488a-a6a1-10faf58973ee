import React from "react";
import { Select, MenuItem, FormControl, Typography, Box } from "@mui/material";

const dropdownContainerStyle = {
  marginBottom: "15px",
  display: { xs: "none", sm: "flex" },
  alignItems: "center",
  justifyContent: "center",
  paddingBottom: "25px",
  borderBottom: "1px solid gray",
};
const labelStyle = { marginRight: "10px" };
const formControlStyle = { width: "130px" };
const selectStyle = { width: { sm: "125px" } };
const dropdownItemStyle = {
  marginRight: "10px",
  display: "flex",
  alignItems: "center",
};

export const WordTileDropdowns = ({
  tilesInRow,
  handleTilesInRowChange,
  dropdownLevel,
  handleLevelChange,
  dropdownType,
  handleTypeChange,
  levelOptions,
  typeOptions,
}) => {
  return (
    <Box sx={dropdownContainerStyle}>
      <Box className="dropdown-wrapper" sx={dropdownItemStyle}>
        <Typography sx={labelStyle}>Tiles in Row</Typography>
        <FormControl
          variant="outlined"
          sx={{ ...formControlStyle, marginRight: "20px" }}
        >
          <Select
            labelId="tiles-in-row-label"
            value={tilesInRow}
            onChange={handleTilesInRowChange}
            sx={selectStyle}
          >
            <MenuItem value={3}>Three</MenuItem>
            <MenuItem value={4}>Four</MenuItem>
            <MenuItem value={5}>Five</MenuItem>
            <MenuItem value={6}>Six</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box Box className="dropdown-wrapper" sx={dropdownItemStyle}>
        <Typography sx={labelStyle}>Level</Typography>
        <FormControl
          variant="outlined"
          sx={{ ...formControlStyle, marginRight: "20px" }}
        >
          <Select
            labelId="level-label"
            value={dropdownLevel}
            onChange={handleLevelChange}
            sx={selectStyle}
          >
            {levelOptions.map((option, index) => (
              <MenuItem key={index} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
      <Box Box className="dropdown-wrapper" sx={dropdownItemStyle}>
        <Typography sx={labelStyle}>Type</Typography>
        <FormControl variant="outlined" sx={formControlStyle}>
          <Select
            labelId="type-label"
            value={dropdownType}
            onChange={handleTypeChange}
            sx={selectStyle}
          >
            {typeOptions.map((option, index) => (
              <MenuItem key={index} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    </Box>
  );
};
