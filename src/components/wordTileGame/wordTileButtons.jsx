import React from "react";
import { <PERSON>, Button } from "@mui/material";

const buttonStyle = {
  width: { xs: "85px", md: "100px" },
  height: { xs: "35px", md: "45px" },
  color: "black",
  backgroundColor: "white",
};

const middleButtonStyle = {
  margin: "0 10px",
};

const optionsButtonStyle = {
  display: { sm: "none" },
};

export const WordTileButtons = ({
  handleNext,
  handleReveal,
  handleDrawerOpen,
}) => {
  return (
    <Box sx={{ textAlign: "center", margin: "15px 20px 0 20px" }}>
      <Button onClick={handleNext} sx={buttonStyle}>
        Next
      </Button>
      <Button
        onClick={handleReveal}
        sx={{ ...buttonStyle, ...middleButtonStyle }}
      >
        Reveal
      </Button>
      <Button
        onClick={handleDrawerOpen}
        sx={{ ...buttonStyle, ...optionsButtonStyle }}
      >
        Options
      </Button>
    </Box>
  );
};
