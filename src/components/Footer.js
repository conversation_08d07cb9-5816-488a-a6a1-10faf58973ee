import React from "react";
import Typography from "@mui/material/Typography";
import TextBoxWithButton from "@/components/TextBoxWithButton";
import Image from "next/image";
import logo from "@/../public/images/icons/patito-feo.svg";
import facebook from "@/../public/images/icons/facebook.svg";
import instagram from "@/../public/images/icons/instagram.svg";
import whatsapp from "@/../public/images/icons/whatsapp.svg";
import spotify from "@/../public/images/icons/spotify.svg";
import { useSnackbar } from "@/hooks/useSnackbar";

import Link from "next/link";
import { Box, Grid } from "@mui/material";
import { addUserSubmission, sendSubscriptionEmail } from "@/utils/sendEmail";
import { useRouter } from "next/router";
import useTranslate from "@/hooks/useTranslate";

const NEXT_PUBLIC_WHATSAPP_PHONE = process.env.NEXT_PUBLIC_WHATSAPP_PHONE || "";
const NEXT_PUBLIC_FB_URL = process.env.NEXT_PUBLIC_FB_URL || "#";
const NEXT_PUBLIC_INSTA_URL = process.env.NEXT_PUBLIC_INSTA_URL || "#";
const NEXT_PUBLIC_SPOTIFY_URL = process.env.NEXT_PUBLIC_SPOTIFY_URL || "#";

export default function Footer() {
  const router = useRouter();
  const currentPath = router.pathname;
  const { showSnackbar } = useSnackbar();
  const { translate } = useTranslate();

  const handleSubmit = async (email) => {
    const handleError = () => {
      showSnackbar(translate("common.failed-sub-news"), {
        type: "error",
      });
    };

    try {
      const ADDRESS = "/api/launch-waitlist/";
      const res = addUserSubmission(email, ADDRESS);
      if (res) {
        const { data, success } = await sendSubscriptionEmail(email);
        if (!success) {
          handleError();
        } else {
          showSnackbar(translate("common.success-sub-news"), {
            type: "success",
          });
        }
      } else {
        handleError();
      }
    } catch (error) {
      console.error("Something went wrong while sending");
      handleError();
    }
  };

  return (
    <footer>
      <Grid
        container
        sx={{
          pt: 12,
          pb: { md: 10 },
          px: 5,
          justifyContent: "space-around",
          width: "100%",
          mx: "auto",
        }}
      >
        {false && (
          <Grid
            item
            xs={12}
            sm={3}
            md={3}
            sx={{ textAlign: { xs: "center", md: "left" } }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: { xs: "center", md: "left" },
              }}
            >
              <Image
                src={logo}
                width={100}
                height={100}
                alt="Patito Feo Logo"
              />
            </Box>
          </Grid>
        )}

        {/* Subscribe Section */}
        {currentPath != "/waitlist" && (
          <Grid
            item
            xs={12}
            md={3}
            sx={{
              textAlign: { xs: "center", md: "left" },
              my: { xs: 2, sm: 2, md: 0 },
            }}
          >
            <Box sx={{ paddingBottom: "12px" }}>
              <Typography
                sx={{
                  mb: 1,
                  textAlign: { xs: "center", md: "left" },
                }}
              >
                {translate("common.sub-news")}
              </Typography>
              <TextBoxWithButton
                handleSubmit={handleSubmit}
                placeholderText={translate("common.ur-email")}
                size="small"
              />
            </Box>
          </Grid>
        )}

        {/* Copyright Section */}
        <Grid
          item
          xs={12}
          md={3}
          order={{ xs: 3, md: 2 }}
          sx={{
            textAlign: "center",
            pt: 4,
            paddingBottom: "12px",
            display: "flex",
            justifyContent: "center",
            alignItems: "flex-end",
            my: { xs: 2, sm: 2, md: 0 },
          }}
        >
          <Typography variant="body2" sx={{ color: "#666666" }}>
            © 2024 Patito Feo LLC. All rights reserved.
          </Typography>
        </Grid>

        {/* Social Media Section */}
        <Grid
          item
          xs={12}
          md={3}
          order={{ xs: 2, md: 3 }}
          sx={{
            textAlign: { xs: "center", md: "left" },
            my: { xs: 2, sm: 2, md: 0 },
          }}
        >
          <Box
            sx={{
              pl: {
                sm: 0,
                md: 15,
              },
            }}
          >
            <Typography
              sx={{
                mb: 1,
                textAlign: { xs: "center", sm: "center", md: "left" },
              }}
            >
              {translate("common.connect")}
            </Typography>
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-end",
                justifyContent: {
                  xs: "center",
                  sm: "center",
                  md: "flex-start",
                },
              }}
            >
              <Link
                href={NEXT_PUBLIC_INSTA_URL}
                passHref
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  src={instagram}
                  width={48}
                  height={48}
                  alt="Instagram Icon"
                  style={{ marginRight: 16 }}
                />
              </Link>
              <Link
                href={`https://wa.me/${NEXT_PUBLIC_WHATSAPP_PHONE}?text=Hello%2C%20Patito%20Feo`}
                passHref
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  src={whatsapp}
                  width={48}
                  height={48}
                  alt="WhatsApp Icon"
                  style={{ marginRight: 16 }}
                />
              </Link>

              <Link
                href={NEXT_PUBLIC_FB_URL}
                passHref
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  src={facebook}
                  width={48}
                  height={48}
                  alt="Facebook Icon"
                  style={{ marginRight: 16 }}
                />
              </Link>
              <Link
                href={NEXT_PUBLIC_SPOTIFY_URL}
                passHref
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  src={spotify}
                  width={48}
                  height={48}
                  alt="Spotify Icon"
                  style={{ padding: 7 }}
                />
              </Link>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </footer>
  );
}
