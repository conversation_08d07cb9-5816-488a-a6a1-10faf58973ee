import React from "react";
import Link from "next/link";
import clsx from "clsx";
import Card from "@mui/material/Card";
import CardHeader from "@mui/material/CardHeader";
import CardMedia from "@mui/material/CardMedia";
import CardContent from "@mui/material/CardContent";
import { CardActions } from "@mui/material";
import Collapse from "@mui/material/Collapse";
import Avatar from "@mui/material/Avatar";
import IconButton from "@mui/material/IconButton";
import RestaurantOutlinedIcon from "@mui/icons-material/RestaurantOutlined";
import PetsOutlinedIcon from "@mui/icons-material/PetsOutlined";
import HomeOutlinedIcon from "@mui/icons-material/HomeOutlined";
import HowToVoteOutlinedIcon from "@mui/icons-material/HowToVoteOutlined";
import Typography from "@mui/material/Typography";
import FavoriteIcon from "@mui/icons-material/Favorite";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import SocialShareBtns from "./SocialShareBtns";
import Toast from "../components/toast";

const iconDict = {
  food: <RestaurantOutlinedIcon />,
  animals: <PetsOutlinedIcon />,
  shelter: <HomeOutlinedIcon />,
  civic: <HowToVoteOutlinedIcon />,
};

export default function VideoCard({
  key,
  href,
  org,
  handleFavorite,
  isFave,
  isAuth,
}) {
  const [expanded, setExpanded] = React.useState(false);
  const [cardElevation, setCardElevation] = React.useState(4);
  const [isFavorite, setIsFavorite] = React.useState(isFave);
  const [isFaveError, setIsFaveError] = React.useState(false);
  const categoryTest = "food";
  const favIconColor = isFavorite ? "red" : "gray";

  const openUrl = (href) => window.open(href, "_blank");

  const handleExpandClick = () => setExpanded(!expanded);

  const handleHover = (status) => setCardElevation(status ? 20 : 4);

  const handleClickFavorite = () => {
    if (isAuth) {
      try {
        setIsFavorite(!isFavorite);
        handleFavorite(org.id);
      } catch (err) {
        console.log(err);
      }
    } else {
      setIsFaveError(true);
    }
  };

  return (
    <Card
      key={key}
      className={""}
      elevation={cardElevation}
      onMouseEnter={() => handleHover(true)}
      onMouseLeave={() => handleHover(false)}
    >
      <Link href={href}>
        <CardHeader
          // onClick={openUrl}
          avatar={
            <Avatar aria-label="category" className={""}>
              Category
            </Avatar>
          }
          action={
            <IconButton aria-label="settings">
              <MoreVertIcon />
            </IconButton>
          }
          title={"name here"}
          subheader={"this is a short description"}
        />
        <CardContent>
          <CardMedia
            style={{ height: 0, paddingTop: "56%" }}
            // onClick={openUrl}
            className={""}
            image={org.posterImage}
            title={"a patita"}
          />
        </CardContent>
        <CardContent>
          <Typography variant="body2" color="textSecondary" component="p">
            {"a short description of what the thing is etc"}
          </Typography>
        </CardContent>
      </Link>
      <CardActions disableSpacing>
        <IconButton aria-label="add to favorites">
          <FavoriteIcon
            style={{ color: favIconColor }}
            onClick={handleClickFavorite}
          />
        </IconButton>
        {isFaveError && (
          <Toast message={"you must be logged in to use favorites"} />
        )}
        <SocialShareBtns
          url={"https://www.npmjs.com/package/react-share"}
          img={""}
        />
      </CardActions>
    </Card>
  );
}
