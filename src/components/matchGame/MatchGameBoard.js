import React, { useState, useEffect } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Button } from "@mui/material";
import ProgressBar from "../ProgressBar";
import { matchVocab } from "./matchVocab";

const vocab = matchVocab;

const shuffle = (array) => {
  let currentIndex = array.length,
    randomIndex;

  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;

    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex],
      array[currentIndex],
    ];
  }

  return array;
};

const DropZone = ({ definition, handleDrop, placedWord }) => {
  const [, dropRef] = useDrop({
    accept: "word",
    drop: (item) => handleDrop(item, definition),
  });

  const smallScreenMarginTop = window.innerWidth < 640 ? "18px" : "8px";

  return (
    <div
      style={{ margin: "8px", marginTop: smallScreenMarginTop }}
      className="sm:m-3 md:m-4 lg:m-5 xl:m-6 relative inline-block"
    >
      {placedWord && (
        <div
          className="placed-word"
          style={{
            position: "absolute",
            top: "-20px",
            left: "50%",
            transform: "translateX(-50%)",
            background: "#FFD6A5",
            padding: "3px",
            border: "1px dashed #000",
            borderRadius: "3px",
            fontSize: "14px",
          }}
        >
          {placedWord}
        </div>
      )}
      <div
        className="definition-zone"
        ref={dropRef}
        style={{
          padding: "16px",
          borderRadius: "8px",
          border: "2px solid grey",
          backgroundColor: "#A8E6CF",
          fontSize: "16px",
        }}
      >
        {definition}
      </div>
    </div>
  );
};

const PetCard = ({ word, isPlaced }) => {
  const [{ isDragging }, dragRef] = useDrag({
    type: "word",
    item: { word },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return (
    <div
      ref={dragRef}
      style={{
        padding: "8px 14px",
        margin: "12px",
        borderRadius: "8px",
        border: "2px solid grey",
        backgroundColor: isPlaced ? "#A8DADCaa" : "#A8DADC",
        opacity: isDragging ? 0.5 : 1,
        fontSize: "16px",
      }}
    >
      {word}
    </div>
  );
};

const Board = () => {
  const [placementMap, setPlacementMap] = useState({});
  const [shuffledKeys, setShuffledKeys] = useState([]);
  const [correctPlacements, setCorrectPlacements] = useState({});
  const [progress, setProgress] = useState(0);
  const [selectedVocab, setSelectedVocab] = useState({});
  const [placedWords, setPlacedWords] = useState(new Set());

  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * vocab.length);
    const chosenVocab = vocab[randomIndex];
    setSelectedVocab(chosenVocab);
    setShuffledKeys(shuffle(Object.keys(chosenVocab)));
  }, []);

  useEffect(() => {
    const numCorrect = Object.values(correctPlacements).filter(Boolean).length;
    const total = Object.keys(selectedVocab).length;
    const newProgress = Math.round((numCorrect / total) * 100);
    setProgress(newProgress);
  }, [correctPlacements, selectedVocab]);

  const resetGame = () => {
    // Randomly select a vocabulary set
    const randomIndex = Math.floor(Math.random() * vocab.length);
    const chosenVocab = vocab[randomIndex];

    // Reset states
    setSelectedVocab(chosenVocab);
    setShuffledKeys(shuffle(Object.keys(chosenVocab)));
    setPlacementMap({});
    setCorrectPlacements({});
    setProgress(0);
    setPlacedWords(new Set());
  };

  useEffect(() => {
    resetGame();
  }, []);

  const handleDrop = (item, definition) => {
    setPlacementMap((prevPlacementMap) => {
      const newPlacementMap = { ...prevPlacementMap, [definition]: item.word };
      const isCorrect = selectedVocab[item.word] === definition;
      setPlacedWords((prevSet) => new Set([...prevSet, item.word]));
      setCorrectPlacements({
        ...correctPlacements,
        [definition]: isCorrect,
      });
      return newPlacementMap;
    });
  };

  return (
    <div style={{ textAlign: "center" }}>
      <h1 style={{ marginBottom: "20px", fontSize: "24px" }}>
        Match Words with Definitions
      </h1>
      <div>
        <ProgressBar percentage={progress} />
      </div>
      <div
        className="vocabWords"
        style={{
          display: "flex",
          justifyContent: "center",
          flexWrap: "wrap",
          marginTop: "30px",
        }}
      >
        {shuffledKeys.map((word) => (
          <PetCard key={word} word={word} isPlaced={placedWords.has(word)} />
        ))}
      </div>
      <div
        className="definitions"
        style={{
          display: "flex",
          justifyContent: "center",
          flexWrap: "wrap",
          marginTop: "30px",
        }}
      >
        {Object.values(selectedVocab).map((definition, index) => (
          <DropZone
            key={index}
            definition={definition}
            handleDrop={handleDrop}
            placedWord={placementMap[definition]}
          />
        ))}
      </div>
      <div>
        <Button onClick={resetGame} style={{ marginTop: "20px" }}>
          Refresh
        </Button>
      </div>
    </div>
  );
};

const App = () => {
  return (
    <DndProvider backend={HTML5Backend}>
      <Board />
    </DndProvider>
  );
};

export default App;
