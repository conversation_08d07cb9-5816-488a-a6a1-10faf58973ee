.trailsText {
  position: relative;
  width: 100%;

  font-weight: 800;
  letter-spacing: -0.05em;
  will-change: transform, opacity;
  overflow: hidden;
}

.trailsText > div {
  padding-right: 0.05em;
  overflow: hidden;
  line-height: 150px;
  font-size: 5em;
}

.bubbuleText {
  position: relative;
  width: 100%;
  height: 100px;
  /* line-height: 80px; */
  line-height: 60px;
  /* color: black; */
  font-size: 2em;
  font-weight: 800;
  letter-spacing: -0.05em;
  will-change: transform, opacity;
  overflow: hidden;
}

.container {
  display: flex;
  align-items: center;
  height: 100vh;
  justify-content: center;
  margin: "5px";
}

.trailUglyDuckling {
  transform: rotate(10deg);
  animation-duration: 2s;
}
.updowntextone {
  transform: rotate(-180deg) scale(-1, 1) translate3d(20px, 0px, 0px);
}

.updowntexttwo {
  transform: rotate(0deg) scale(-1, 1) translate3d(20px, 0px, 0px);
}

/* these media queries represent the current MUI breakpoints for XS, SM, MD, LG, AND XL */

/* XS */
@media screen and (min-width: 0px) and (max-width: 600px) {
  .trailsText {
    height: 125px;
  }
}

/* SM */
@media screen and (min-width: 601px) and (max-width: 900px) {
  .trailsText {
    height: 150px;
  }
}

/* MD */
/* @media screen and (min-width: 901px) and (max-width: 1200px) {
  
}

/* LG */
/* @media screen and (min-width: 1201px) and (max-width: 1536px) {
  
}  */

/* XL */
/* @media screen and (min-width: 1536px)  {
  
}  */
