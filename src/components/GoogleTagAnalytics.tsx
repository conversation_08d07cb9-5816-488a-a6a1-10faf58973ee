import Script from "next/script";

export default function GoogleTagAnalytics() {
  if (process.env.NODE_ENV === "production") {
    return (
      <>
        <Script
          strategy="afterInteractive"
          src="https://www.googletagmanager.com/gtag/js?id=G-KR5JNSV9RP"
        />
        <Script
          id="google-analytics"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-KR5JNSV9RP');
            `,
          }}
        />
      </>
    );
  }
  return null;
}
