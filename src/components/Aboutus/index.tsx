import React, { useMemo } from "react";
import Banner from "../classes/Banner";
import backgroundImage from "public/images/about/banner.svg";
import Box from "@mui/material/Box";
import { Container, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/router";
import useTranslate from "@/hooks/useTranslate";

const TABS_TYPE = {
  WHO_WE_ARE: 1,
  WHY_PATITO: 2,
  TEAM: 3,
  TESTIMONIALS: 4,
};

const tabs = [
  {
    id: TABS_TYPE.WHO_WE_ARE,
    name: "about.who-we-are",
    href: "/about-us",
    subtitle: "about.who-we-are",
  },
  {
    id: TABS_TYPE.WHY_PATITO,
    subtitle: "Why Patito Feo?",
    name: "about.why-patitofeo",
    href: "/about-us/why-patito-feo",
  },
  {
    id: TABS_TYPE.TEAM,
    name: "about.team",
    subtitle: "about-team.title",
    href: "/about-us/team",
  },
  {
    id: TABS_TYPE.TESTIMONIALS,
    name: "about.testimonials",
    href: "/about-us/testimonials",
    subtitle: "about-team.title",
  },
];

const AboutUsLayout = ({ children }) => {
  const { translate } = useTranslate();
  const router = useRouter();
  const subtitle = useMemo(() => {
    const path = router.asPath;
    const data = tabs.find((f) => f.href === path);
    return data.subtitle;
  }, [router.asPath]);

  return (
    <div>
      <Banner title={translate("about.us")} backgroundImage={backgroundImage} />
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        gap={4}
        mt={5}
        sx={{ width: "100%" }}
      >
        {tabs.map((m, i) => {
          const isActive = router.asPath === m.href;
          return (
            <Box
              key={i}
              sx={{
                width: {
                  xs: "22%",
                  sm: 175,
                },
                background: isActive && "#EAF8F7",
                padding: "10px 0px",
                cursor: "pointer",
              }}
            >
              <Link href={m.href}>
                <Typography
                  fontWeight="800"
                  color={isActive ? "#000000" : "#64748B"}
                  sx={{
                    fontSize: {
                      xs: 12,
                      md: 19,
                    },
                  }}
                  textAlign="center"
                >
                  {translate(m.name)}
                </Typography>
              </Link>
            </Box>
          );
        })}
      </Box>
      <Typography
        mt={10}
        textAlign="center"
        fontWeight="700"
        fontSize={32}
        color="#005F56"
      >
        {translate(subtitle)}
      </Typography>
      <Container maxWidth="lg" sx={{ textAlign: "center" }}>
        {children}
      </Container>
    </div>
  );
};

export default AboutUsLayout;
