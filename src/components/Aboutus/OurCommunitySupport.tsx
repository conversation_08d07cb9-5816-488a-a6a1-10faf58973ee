import useTranslate from "@/hooks/useTranslate";
import { Box, Container, Typography } from "@mui/material";
import Image from "next/image";
import React from "react";

const OurCommunitySupport = () => {
  const { translate } = useTranslate();
  return (
    <div
      style={{
        background: "#F2FFFE",
      }}
    >
      <Container maxWidth="lg" sx={{ textAlign: "center" }}>
        <Box
          width="100%"
          display="flex"
          flexDirection="column"
          alignItems="center"
        >
          <Typography
            mt={10}
            textAlign="center"
            fontWeight="700"
            fontSize={32}
            color="#005F56"
          >
            {translate("about-team.subtitle")}
          </Typography>

          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            sx={{
              width: {
                xs: "100%",
                md: "70%",
              },
            }}
          >
            <Typography
              textAlign="center"
              fontSize={16}
              fontWeight="600"
              mt={6}
              px={4}
              color="#64748B"
            >
              {translate("about-team.description")}
            </Typography>
          </Box>

          <Box
            mt={8}
            sx={{
              height: {
                xs: 200,
                sm: 300,
                md: 350,
                lg: 450,
              },
              width: "100%",
              border: "5px solid #14A79C",
              position: "relative",
              borderRadius: 5,
              overflow: "hidden",
            }}
          >
            <Image
              src="/images/about/team/community.webp"
              alt="Our community support"
              fill
            />
          </Box>
        </Box>
      </Container>
    </div>
  );
};

export default OurCommunitySupport;
