import { Box, Typography } from "@mui/material";
import React, { useMemo, useRef, useState } from "react";
import Image from "next/image";
import useOnClickOutside from "@/hooks/useOnClickOutside";

import userbg from "public/images/about/team/userbg.svg";
import textbg from "public/images/about/team/textbg.svg";
import LanguageSwitcher from "./LanguageSwitcher";
import { TeamDataType } from "@/types";

type DetailProps = React.FC<{
  setShowExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  data: TeamDataType;
  isModal?: boolean;
}>;
const Detail: DetailProps = ({ setShowExpanded, data, isModal }) => {
  const [isEnglish, setIsEnglish] = useState(false);
  const ref = useRef();
  useOnClickOutside(ref, () => {
    setShowExpanded(false);
  });

  return (
    <>
      <Box
        sx={{
          width: {
            xs: "100%",
            md: "40%",
          },
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          gap: 1,
        }}
      >
        <Box
          sx={{
            background: "rgba(249, 178, 56, 1)",
            borderRadius: 3,
            backgroundPosition: "center",
            p: 3,
            position: "relative",
            zIndex: 1,
            display: "flex",
          }}
        >
          <Box
            width={isModal ? "95%" : "100%"}
            sx={{
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Typography
              textAlign="left"
              color="rgba(0, 95, 86, 1)"
              fontSize="1.1rem"
              fontWeight="700"
              mb={2}
              style={{ zIndex: 10 }}
            >
              {data.name}
            </Typography>
            <Typography
              style={{ zIndex: 10 }}
              fontSize={12}
              textAlign="left"
              color="#fff"
            >
              {data.designation}
            </Typography>
          </Box>
          {isModal && (
            <Box width={isModal ? "5%" : "0%"}>
              <span
                style={{ cursor: "pointer" }}
                onClick={() => setShowExpanded(false)}
              >
                <Close />
              </span>
            </Box>
          )}
          <Box
            sx={{
              backgroundImage: `url(${userbg.src})`,
              position: "absolute",
              top: 0,
              right: 0,
              left: 0,
              bottom: 0,
              backgroundPosition: "right",
              zIndex: -10,
            }}
          />
        </Box>
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          gap={2}
        >
          <UserImage img={data.img1} name={data.name} />
          <UserImage img={data.img2} name={data.name} />
        </Box>
      </Box>
      <Box
        sx={{
          background: "rgba(20, 167, 156, 1)",
          p: 3,
          borderRadius: 3,
          width: {
            xs: "100%",
            md: "60%",
          },
          position: "relative",
          zIndex: 10,
          overflowY: "auto",
          height: "100%",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: 0,
            right: 0,
            left: 0,
            bottom: 0,
            zIndex: -10,
          }}
        >
          <Box
            sx={{
              backgroundImage: `url(${textbg.src})`,
              backgroundRepeat: "no-repeat",
              backgroundPosition: "bottom right",
              height: "100%",
              width: "100%",
            }}
          />
        </Box>

        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
          mb={2}
          sx={{
            zIndex: 100,
          }}
        >
          <LanguageSwitcher setIsEnglish={setIsEnglish} isEnglish={isEnglish} />
          {!isModal && (
            <span
              style={{ cursor: "pointer" }}
              onClick={() => setShowExpanded(false)}
            >
              <Close />
            </span>
          )}
        </Box>
        <Typography
          fontSize={14}
          color="rgba(201, 255, 219, 1)"
          textAlign="left"
        >
          {isEnglish ? data.description.en : data.description.es}
        </Typography>
      </Box>
    </>
  );
};

export default Detail;

const UserImage = ({ img, name }) => {
  return (
    <Box
      sx={{
        borderRadius: 5,
        height: 185,
        width: "50%",
        position: "relative",
        overflow: "hidden",
      }}
    >
      <Image alt={name} src={img} fill objectFit="cover" />
    </Box>
  );
};

const Close = () => {
  return (
    <svg width="15" height="15" viewBox="0 0 17 17" fill="none">
      <path
        d="M8.50045 10.1916L2.57962 16.1125C2.35809 16.334 2.07615 16.4448 1.73379 16.4448C1.39143 16.4448 1.10948 16.334 0.887955 16.1125C0.666427 15.8909 0.555664 15.609 0.555664 15.2666C0.555664 14.9243 0.666427 14.6423 0.887955 14.4208L6.80879 8.49997L0.887955 2.57913C0.666427 2.35761 0.555664 2.07566 0.555664 1.7333C0.555664 1.39094 0.666427 1.10899 0.887955 0.887467C1.10948 0.665939 1.39143 0.555176 1.73379 0.555176C2.07615 0.555176 2.35809 0.665939 2.57962 0.887467L8.50045 6.8083L14.4213 0.887467C14.6428 0.665939 14.9248 0.555176 15.2671 0.555176C15.6095 0.555176 15.8914 0.665939 16.113 0.887467C16.3345 1.10899 16.4452 1.39094 16.4452 1.7333C16.4452 2.07566 16.3345 2.35761 16.113 2.57913L10.1921 8.49997L16.113 14.4208C16.3345 14.6423 16.4452 14.9243 16.4452 15.2666C16.4452 15.609 16.3345 15.8909 16.113 16.1125C15.8914 16.334 15.6095 16.4448 15.2671 16.4448C14.9248 16.4448 14.6428 16.334 14.4213 16.1125L8.50045 10.1916Z"
        fill="black"
      />
    </svg>
  );
};
