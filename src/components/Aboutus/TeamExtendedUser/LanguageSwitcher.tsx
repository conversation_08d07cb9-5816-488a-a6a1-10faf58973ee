import { Box, Typography } from "@mui/material";
import React from "react";

const activeColor = "rgba(0, 95, 86, 1)";
type LanguageSwitcherProps = React.FC<{
  setIsEnglish: React.Dispatch<React.SetStateAction<boolean>>;
  isEnglish: boolean;
}>;
const LanguageSwitcher: LanguageSwitcherProps = ({
  isEnglish,
  setIsEnglish,
}) => {
  return (
    <Box
      sx={{
        background: "rgba(175, 244, 198, 1)",
        borderRadius: 10,
        display: "flex",
        flexDirection: "row",
        gap: 2,
        overflow: "hidden",
        p: 1,
      }}
    >
      <Tab
        isEnglish
        isActive={isEnglish}
        onClick={() => {
          setIsEnglish(true);
        }}
      />
      <Tab
        isEnglish={false}
        isActive={!isEnglish}
        onClick={() => {
          setIsEnglish(false);
        }}
      />
    </Box>
  );
};

export default LanguageSwitcher;

const Tab = ({ isActive, isEnglish, onClick }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      gap={2}
      p={1}
      px={2}
      onClick={onClick}
      sx={{
        borderRadius: 5,
        color: !isActive ? activeColor : "#fff",
        background: isActive && activeColor,
        cursor: "pointer",
      }}
    >
      <LanguageIcon isEnglish={isEnglish} isActive={isActive} />
      <Typography fontSize={12}>{isEnglish ? "English" : "Español"}</Typography>
    </Box>
  );
};

type LanguageIconProps = React.FC<{
  isEnglish: boolean;
  isActive: boolean;
}>;
const LanguageIcon: LanguageIconProps = ({ isEnglish, isActive }) => {
  const color = !isActive ? activeColor : "#fff";
  if (isEnglish) {
    return (
      <svg
        width="29"
        height="16"
        viewBox="0 0 29 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17 13.0742C22.1851 13.0742 24.7784 13.0742 26.3885 12.1952C27.9986 11.3162 28 9.90247 28 7.07422C28 4.24597 28 2.83147 26.3885 1.95322C24.777 1.07497 22.1851 1.07422 17 1.07422H11.5C6.31488 1.07422 3.72162 1.07422 2.1115 1.95322C0.501375 2.83222 0.5 4.24597 0.5 7.07422C0.5 9.90247 0.5 11.317 2.1115 12.1952C3.00937 12.6857 4.2125 12.9025 6 12.9977"
          stroke={color}
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M17.0002 13.0742C15.3007 13.0742 13.428 13.4492 11.7188 13.933C8.97158 14.7107 7.59796 15.1 6.92146 14.8517C6.24496 14.6035 6.37283 13.8355 6.62996 12.2987L6.68771 11.9492"
          stroke={color}
          stroke-linecap="round"
        />
        <path
          d="M15.2148 4.65625C15.1628 4.84635 15.1367 5.01172 15.1367 5.15234C15.1367 5.22526 15.1289 5.33854 15.1133 5.49219C15.1003 5.64323 15.0938 5.75521 15.0938 5.82812C15.0938 6.07552 15.0807 6.44271 15.0547 6.92969C15.0286 7.41667 15.0156 7.78385 15.0156 8.03125C15.0156 8.22135 15.0299 8.50521 15.0586 8.88281C15.0872 9.26042 15.1016 9.54427 15.1016 9.73438C15.1016 9.8776 15.0508 9.9987 14.9492 10.0977C14.8477 10.1966 14.7214 10.2461 14.5703 10.2461C14.4167 10.2461 14.2839 10.1966 14.1719 10.0977C14.0599 10.0013 14.0039 9.88281 14.0039 9.74219C14.0039 9.55208 13.9948 9.26693 13.9766 8.88672C13.9609 8.50651 13.9531 8.22135 13.9531 8.03125C13.9531 7.88281 13.9557 7.7513 13.9609 7.63672C13.5521 7.65495 13.0469 7.71875 12.4453 7.82812C11.9453 7.91927 11.4453 8.01172 10.9453 8.10547C10.9401 8.28255 10.9219 8.57812 10.8906 8.99219C10.8646 9.35677 10.8516 9.65495 10.8516 9.88672C10.8516 10.0299 10.8008 10.151 10.6992 10.25C10.5977 10.349 10.4714 10.3984 10.3203 10.3984C10.1693 10.3984 10.043 10.349 9.94141 10.25C9.83984 10.151 9.78906 10.0299 9.78906 9.88672C9.78906 9.6237 9.8112 9.23047 9.85547 8.70703C9.90234 8.18359 9.92578 7.79036 9.92578 7.52734C9.92578 7.19401 9.93359 6.69271 9.94922 6.02344C9.96484 5.35156 9.97266 4.84896 9.97266 4.51562C9.97266 4.3724 10.0234 4.2513 10.125 4.15234C10.2266 4.05339 10.3529 4.00391 10.5039 4.00391C10.6549 4.00391 10.7812 4.05339 10.8828 4.15234C10.9844 4.2513 11.0352 4.3724 11.0352 4.51562C11.0352 4.71354 11.0273 5.01172 11.0117 5.41016C10.9987 5.80599 10.9922 6.10286 10.9922 6.30078C10.9922 6.36589 10.9831 6.49089 10.9648 6.67578C10.9492 6.84766 10.9427 6.97917 10.9453 7.07031L12.4453 6.78906C13.0312 6.6849 13.5365 6.625 13.9609 6.60938C13.9714 6.4349 13.9883 5.96615 14.0117 5.20312C14.0169 5.0026 14.0781 4.77214 14.1953 4.51172C14.3411 4.19141 14.5065 4.03125 14.6914 4.03125C14.8294 4.03125 14.9518 4.07682 15.0586 4.16797C15.1732 4.26693 15.2305 4.39062 15.2305 4.53906C15.2305 4.57812 15.2253 4.61719 15.2148 4.65625ZM16.7891 5.37891C16.6224 5.37891 16.4779 5.32292 16.3555 5.21094C16.2331 5.09896 16.1719 4.96224 16.1719 4.80078C16.1719 4.63932 16.2331 4.5026 16.3555 4.39062C16.4779 4.27865 16.6224 4.22266 16.7891 4.22266C16.9557 4.22266 17.099 4.27865 17.2188 4.39062C17.3411 4.5026 17.4023 4.63932 17.4023 4.80078C17.4023 4.96224 17.3411 5.09896 17.2188 5.21094C17.099 5.32292 16.9557 5.37891 16.7891 5.37891ZM17.1211 8.24219C17.1211 8.39062 17.125 8.62891 17.1328 8.95703C17.1432 9.28516 17.1484 9.52344 17.1484 9.67188C17.1484 9.82292 17.0977 9.94792 16.9961 10.0469C16.8945 10.1432 16.7656 10.1914 16.6094 10.1914C16.4531 10.1914 16.3242 10.1432 16.2227 10.0469C16.1211 9.94792 16.0703 9.82292 16.0703 9.67188C16.0703 9.52344 16.0651 9.28516 16.0547 8.95703C16.0469 8.62891 16.043 8.39062 16.043 8.24219C16.043 8.01042 16.056 7.72005 16.082 7.37109C16.1081 7.02214 16.1211 6.73177 16.1211 6.5C16.1211 6.34896 16.1719 6.22526 16.2734 6.12891C16.375 6.02995 16.5039 5.98047 16.6602 5.98047C16.8164 5.98047 16.9453 6.02995 17.0469 6.12891C17.1484 6.22526 17.1992 6.34896 17.1992 6.5C17.1992 6.73177 17.1862 7.02214 17.1602 7.37109C17.1341 7.72005 17.1211 8.01042 17.1211 8.24219ZM18.6875 8.91406C18.5417 8.91406 18.4206 8.86849 18.3242 8.77734C18.2305 8.6862 18.1836 8.57161 18.1836 8.43359C18.1836 8.33203 18.1797 8.18099 18.1719 7.98047C18.1667 7.77734 18.1641 7.625 18.1641 7.52344C18.1641 7.16406 18.1667 6.625 18.1719 5.90625C18.1797 5.1875 18.1836 4.64844 18.1836 4.28906C18.1836 4.15104 18.2318 4.03646 18.3281 3.94531C18.4245 3.85156 18.5443 3.80469 18.6875 3.80469C18.8307 3.80469 18.9505 3.85156 19.0469 3.94531C19.1432 4.03646 19.1914 4.15104 19.1914 4.28906V8.43359C19.1914 8.57161 19.1432 8.6862 19.0469 8.77734C18.9531 8.86849 18.8333 8.91406 18.6875 8.91406ZM18.6758 10.332C18.5195 10.332 18.3737 10.2487 18.2383 10.082C18.1159 9.92839 18.0547 9.78516 18.0547 9.65234C18.0547 9.51693 18.1081 9.40365 18.2148 9.3125C18.3216 9.22135 18.4427 9.17578 18.5781 9.17578C18.7344 9.17578 18.8789 9.25911 19.0117 9.42578C19.1341 9.57943 19.1953 9.72135 19.1953 9.85156C19.1953 9.98438 19.1419 10.0977 19.0352 10.1914C18.931 10.2852 18.8112 10.332 18.6758 10.332Z"
          stroke={color}
        />
      </svg>
    );
  }
  return (
    <svg
      width="29"
      height="16"
      viewBox="0 0 29 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.5 13.0742C22.6851 13.0742 25.2784 13.0742 26.8885 12.1952C28.4986 11.3162 28.5 9.90247 28.5 7.07422C28.5 4.24597 28.5 2.83147 26.8885 1.95322C25.277 1.07497 22.6851 1.07422 17.5 1.07422H12C6.81488 1.07422 4.22162 1.07422 2.6115 1.95322C1.00137 2.83222 1 4.24597 1 7.07422C1 9.90247 1 11.317 2.6115 12.1952C3.50937 12.6857 4.7125 12.9025 6.5 12.9977"
        stroke={color}
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M17.5002 13.0742C15.8007 13.0742 13.928 13.4492 12.2188 13.933C9.47158 14.7107 8.09796 15.1 7.42146 14.8517C6.74496 14.6035 6.87283 13.8355 7.12996 12.2987L7.18771 11.9492"
        stroke={color}
        stroke-linecap="round"
      />
      <path
        d="M11.4102 4.65625C11.3581 4.84635 11.332 5.01172 11.332 5.15234C11.332 5.22526 11.3242 5.33854 11.3086 5.49219C11.2956 5.64323 11.2891 5.75521 11.2891 5.82812C11.2891 6.07552 11.276 6.44271 11.25 6.92969C11.224 7.41667 11.2109 7.78385 11.2109 8.03125C11.2109 8.22135 11.2253 8.50521 11.2539 8.88281C11.2826 9.26042 11.2969 9.54427 11.2969 9.73438C11.2969 9.8776 11.2461 9.9987 11.1445 10.0977C11.043 10.1966 10.9167 10.2461 10.7656 10.2461C10.612 10.2461 10.4792 10.1966 10.3672 10.0977C10.2552 10.0013 10.1992 9.88281 10.1992 9.74219C10.1992 9.55208 10.1901 9.26693 10.1719 8.88672C10.1562 8.50651 10.1484 8.22135 10.1484 8.03125C10.1484 7.88281 10.151 7.7513 10.1562 7.63672C9.7474 7.65495 9.24219 7.71875 8.64062 7.82812C8.14062 7.91927 7.64062 8.01172 7.14062 8.10547C7.13542 8.28255 7.11719 8.57812 7.08594 8.99219C7.0599 9.35677 7.04688 9.65495 7.04688 9.88672C7.04688 10.0299 6.99609 10.151 6.89453 10.25C6.79297 10.349 6.66667 10.3984 6.51562 10.3984C6.36458 10.3984 6.23828 10.349 6.13672 10.25C6.03516 10.151 5.98438 10.0299 5.98438 9.88672C5.98438 9.6237 6.00651 9.23047 6.05078 8.70703C6.09766 8.18359 6.12109 7.79036 6.12109 7.52734C6.12109 7.19401 6.12891 6.69271 6.14453 6.02344C6.16016 5.35156 6.16797 4.84896 6.16797 4.51562C6.16797 4.3724 6.21875 4.2513 6.32031 4.15234C6.42188 4.05339 6.54818 4.00391 6.69922 4.00391C6.85026 4.00391 6.97656 4.05339 7.07812 4.15234C7.17969 4.2513 7.23047 4.3724 7.23047 4.51562C7.23047 4.71354 7.22266 5.01172 7.20703 5.41016C7.19401 5.80599 7.1875 6.10286 7.1875 6.30078C7.1875 6.36589 7.17839 6.49089 7.16016 6.67578C7.14453 6.84766 7.13802 6.97917 7.14062 7.07031L8.64062 6.78906C9.22656 6.6849 9.73177 6.625 10.1562 6.60938C10.1667 6.4349 10.1836 5.96615 10.207 5.20312C10.2122 5.0026 10.2734 4.77214 10.3906 4.51172C10.5365 4.19141 10.7018 4.03125 10.8867 4.03125C11.0247 4.03125 11.1471 4.07682 11.2539 4.16797C11.3685 4.26693 11.4258 4.39062 11.4258 4.53906C11.4258 4.57812 11.4206 4.61719 11.4102 4.65625ZM13.7031 10.3047C13.2214 10.3047 12.8073 10.1393 12.4609 9.80859C12.0807 9.44661 11.8815 8.96354 11.8633 8.35938C11.8451 7.78385 12.0026 7.27083 12.3359 6.82031C12.7318 6.28906 13.2826 6.02344 13.9883 6.02344C14.5091 6.02344 14.9232 6.23177 15.2305 6.64844C15.5065 7.02344 15.6445 7.48828 15.6445 8.04297C15.6445 8.64714 15.4831 9.16276 15.1602 9.58984C14.7982 10.0664 14.3125 10.3047 13.7031 10.3047ZM13.9375 7.05469C13.6172 7.05208 13.3646 7.19792 13.1797 7.49219C13.0234 7.73958 12.9453 8.02865 12.9453 8.35938C12.9453 8.67188 13.0339 8.91146 13.2109 9.07812C13.3516 9.21094 13.5156 9.27734 13.7031 9.27734C13.9297 9.27734 14.1263 9.20052 14.293 9.04688C14.4831 8.86719 14.5846 8.6237 14.5977 8.31641C14.6315 7.47786 14.4115 7.05729 13.9375 7.05469ZM17.5078 9.5C17.5078 9.99479 17.3281 10.2422 16.9688 10.2422C16.8125 10.2422 16.6836 10.1927 16.582 10.0938C16.4805 9.99479 16.4297 9.86719 16.4297 9.71094V4.30859C16.4297 4.15755 16.4844 4.03385 16.5938 3.9375C16.6979 3.84896 16.8268 3.80469 16.9805 3.80469C17.1341 3.80469 17.2604 3.85156 17.3594 3.94531C17.4583 4.03646 17.5078 4.15755 17.5078 4.30859V9.5ZM21.8711 10.332C21.7513 10.332 21.5612 10.2018 21.3008 9.94141C21.0612 10.056 20.8503 10.1419 20.668 10.1992C20.4857 10.2591 20.3307 10.2891 20.2031 10.2891C19.5755 10.2891 19.1042 10.1211 18.7891 9.78516C18.4766 9.44922 18.3203 8.94661 18.3203 8.27734C18.3203 7.64974 18.5521 7.11328 19.0156 6.66797C19.4792 6.22005 20.0339 5.99609 20.6797 5.99609C20.9271 5.99609 21.2135 6.06641 21.5391 6.20703C21.9323 6.3763 22.1289 6.57292 22.1289 6.79688C22.1289 6.89062 22.099 6.97266 22.0391 7.04297C22.013 7.14714 21.9922 7.27865 21.9766 7.4375C21.9635 7.59375 21.957 7.77734 21.957 7.98828C21.957 8.48568 21.9922 8.84375 22.0625 9.0625C22.0703 9.08594 22.1302 9.22135 22.2422 9.46875C22.3438 9.69271 22.3945 9.82031 22.3945 9.85156C22.3945 9.98958 22.3411 10.1042 22.2344 10.1953C22.1302 10.2865 22.0091 10.332 21.8711 10.332ZM20.9023 7.65234C20.9023 7.56901 20.9076 7.47396 20.918 7.36719C20.931 7.25781 20.9492 7.13672 20.9727 7.00391C20.9206 6.97786 20.8737 6.95964 20.832 6.94922C20.793 6.9362 20.7591 6.92969 20.7305 6.92969C20.3763 6.92969 20.0664 7.06901 19.8008 7.34766C19.5352 7.6237 19.4023 7.94401 19.4023 8.30859C19.4023 8.65755 19.4648 8.92057 19.5898 9.09766C19.7148 9.27214 19.9023 9.35938 20.1523 9.35938C20.3138 9.35938 20.4661 9.33333 20.6094 9.28125C20.7552 9.22656 20.8919 9.14714 21.0195 9.04297C20.9414 8.42318 20.9023 7.95964 20.9023 7.65234ZM23.4922 8.91406C23.3464 8.91406 23.2253 8.86849 23.1289 8.77734C23.0352 8.6862 22.9883 8.57161 22.9883 8.43359C22.9883 8.33203 22.9844 8.18099 22.9766 7.98047C22.9714 7.77734 22.9688 7.625 22.9688 7.52344C22.9688 7.16406 22.9714 6.625 22.9766 5.90625C22.9844 5.1875 22.9883 4.64844 22.9883 4.28906C22.9883 4.15104 23.0365 4.03646 23.1328 3.94531C23.2292 3.85156 23.349 3.80469 23.4922 3.80469C23.6354 3.80469 23.7552 3.85156 23.8516 3.94531C23.9479 4.03646 23.9961 4.15104 23.9961 4.28906V8.43359C23.9961 8.57161 23.9479 8.6862 23.8516 8.77734C23.7578 8.86849 23.638 8.91406 23.4922 8.91406ZM23.4805 10.332C23.3242 10.332 23.1784 10.2487 23.043 10.082C22.9206 9.92839 22.8594 9.78516 22.8594 9.65234C22.8594 9.51693 22.9128 9.40365 23.0195 9.3125C23.1263 9.22135 23.2474 9.17578 23.3828 9.17578C23.5391 9.17578 23.6836 9.25911 23.8164 9.42578C23.9388 9.57943 24 9.72135 24 9.85156C24 9.98438 23.9466 10.0977 23.8398 10.1914C23.7357 10.2852 23.6159 10.332 23.4805 10.332Z"
        fill={color}
      />
    </svg>
  );
};
