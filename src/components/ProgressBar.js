import React from "react";
import LinearProgress from "@mui/material/LinearProgress";
import Typography from "@mui/material/Typography";

function ProgressBar({ percentage }) {
  const clampedPercentage = Math.min(Math.max(percentage, 0), 100);

  return (
    <div>
      <Typography variant="subtitle1" gutterBottom>
        Progress: {clampedPercentage}%
      </Typography>
      <LinearProgress
        variant="determinate"
        value={clampedPercentage}
        sx={{
          height: 10, 
          borderRadius: 5, 
          backgroundColor: "#ccc", 
        }}
      />
    </div>
  );
}

export default ProgressBar;
