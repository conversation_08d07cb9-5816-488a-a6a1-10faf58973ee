/* eslint-disable react/no-unescaped-entities */
import { Box, Typography } from "@mui/material";
import React from "react";

type EmptyDashboardListProps = React.FC<{
  title: string;
  description: string;
}>;
const EmptyDashboardList: EmptyDashboardListProps = ({
  title,
  description,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        background: "#e7e7e7",
        width: "100%",
        p: 4,
        py: 10,
        borderRadius: 2,
        height: 270,
      }}
    >
      <Typography fontSize="1.65rem" fontWeight={600}>
        {title}
      </Typography>
      <Typography variant="body1" color="textSecondary">
        {description}
      </Typography>
    </Box>
  );
};

export default EmptyDashboardList;
