import { <PERSON>, Button, Typography } from "@mui/material";
import fallbackImage from "@/../../public/images/dashboard/mylearning.webp";
import TagComponent from "./TagComponent";
import LikeComponent from "./LikeComponent";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { getProficiencyEn } from "@/utils/common";
import ProgressBar from "./ProgressBar";
import { LikedListType } from "@/types";
const level = {
  color: "#B3B3B3",
  fontSize: "14px",
  fontWeight: "500",
  marginRight: "10px",
  display: "flex",
  alignItems: "center",
  justifyContent: "left",
};

const cardContainerStyles = {
  display: "flex",
  borderRadius: "15px",
  width: "298px",
  height: "366px",
  flexDirection: "column",
};

const percentageTextStyles = {
  fontSize: { xs: "10px", sm: "14px" },
  fontWeight: { xs: "500", sm: "400" },
};

const likeComponentStyles = {
  top: 0,
  right: 0,
  position: "absolute",
};

const tagContainerStyles = {
  display: "flex",
  overflowX: "auto",
  whiteSpace: "nowrap",
  width: "265px",
  "::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none",
  marginBottom: "20px",
};

const tagStyles = {
  display: "inline-block",
  backgroundColor: "#D7F7F5", // light teal color
  color: "#14A79C", // teal color for text
  fontSize: "13px",
  fontWeight: "500",
  marginRight: "5px",
};

const descriptionStyles = {
  fontWeight: "400",
  fontSize: "14px",
  color: "white",
  marginBottom: "10px",
  display: "-webkit-box",
  overflow: "hidden",
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 3,
};

type CollectionCardProps = React.FC<{
  data: any;
  isAdmin: boolean;
  isLiked?: boolean;
  isLearning?: boolean;
  onDelete: (id: string) => void;
  onDislike?: (id: string) => void;
  onlike?: (data: LikedListType) => void;
}>;
const CollectionCard: CollectionCardProps = ({
  data,
  isAdmin,
  onDelete,
  isLearning = false,
  // isLiked = false,
  onDislike = () => {},
  onlike = () => {},
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const { showSnackbar } = useSnackbar();
  const [isLiked, setIsLiked] = useState(data.isLiked);

  useEffect(() => {
    setIsLiked(data.isLiked);
  }, [data]);

  const handleDelete = async () => {
    const handleError = () => {
      setIsDeleting(false);
      showSnackbar("Failed to delete collection", {
        type: "error",
      });
    };

    try {
      setIsDeleting(true);
      const { data: deletedData } = await axiosInstance.delete(
        `collection/delete/${data._id}`
      );
      if (deletedData.success && deletedData?.data) {
        showSnackbar("Deleted the collection successfully", {
          type: "success",
        });
        onDelete && onDelete(data._id);
      } else {
        handleError();
      }
      setIsDeleting(false);
    } catch (error) {
      handleError();
    }
  };

  const proficiency = useMemo(() => {
    return getProficiencyEn({
      data: data.proficiencyLevel,
    });
  }, [data]);

  const progress = useMemo(() => {
    return data.videoCollectionProgress?.progress ?? 0;
  }, [data]);

  const link = isAdmin
    ? `/create/collection/edit/${data._id}`
    : `/videos/collection/${data._id}`;

  return (
    <Box sx={cardContainerStyles} aria-disabled={isDeleting}>
      <Link href={link}>
        <Box sx={{ borderRadius: "15px" }}>
          {/* Top section with image and favorite button */}
          <Box
            position="relative"
            sx={{
              position: "relative",
              width: "100%",
              borderRadius: "15px",
              padding: "15px",
              height: "366px",
              backgroundImage: `url(${data.coverImageUrl})`,
              backgroundSize: "cover", // Ensures the image covers the entire Box
              backgroundPosition: "center", // Centers the image
              backgroundRepeat: "no-repeat", // Prevents tiling
            }}
          >
            <Box
              sx={{
                borderRadius: "15px",
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background:
                  "linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 1))",
                zIndex: 1,
              }}
            />

            {/* Favorite Icon */}
            <Box
              sx={{
                position: "relative",
                zIndex: 2,
                color: "white",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                height: "100%",
              }}
            >
              {!isAdmin && (
                <LikeComponent
                  id={data._id}
                  isLiked={isLiked}
                  isCollection
                  styles={likeComponentStyles}
                  onDislike={() => {
                    onDislike && onDislike(data._id);
                  }}
                  onlike={() => {
                    onlike && onlike(data);
                  }}
                />
              )}

              {/* Card Content */}
              <Box sx={{ paddingTop: "130px", width: "268px" }}>
                <Box sx={{ paddingBottom: "20px" }}>
                  <Typography
                    noWrap
                    sx={{ fontSize: "20px", color: "white" }}
                    fontWeight="600"
                  >
                    {data.name || "Collection Title"}
                  </Typography>
                  <Typography sx={descriptionStyles} marginTop={1}>
                    {data.desc}
                  </Typography>
                </Box>

                {isLearning && (
                  <Box width="100%">
                    <ProgressBar value={progress} />
                    <Typography sx={percentageTextStyles}>
                      {progress}% Complete
                    </Typography>
                  </Box>
                )}

                {/* Skill Level */}
                <Box sx={tagContainerStyles}>
                  {data?.categories?.map((category, index) => (
                    <TagComponent
                      key={index}
                      label={category}
                      tagStyles={tagStyles}
                    ></TagComponent>
                  ))}
                </Box>
                {proficiency && (
                  <Typography sx={level}>{proficiency}</Typography>
                )}
              </Box>
            </Box>

            {isAdmin && (
              <Button
                sx={{
                  position: "absolute",
                  bottom: 10,
                  zIndex: 100,
                  width: "90%",
                }}
                type="submit"
                onClick={(e) => {
                  e.preventDefault();
                  handleDelete();
                }}
                disabled={isDeleting}
              >
                Delete
              </Button>
            )}
          </Box>
        </Box>
      </Link>
    </Box>
  );
};

export default CollectionCard;
