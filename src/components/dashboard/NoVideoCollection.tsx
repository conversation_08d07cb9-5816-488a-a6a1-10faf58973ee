import { Box, Typography } from "@mui/material";
import React from "react";
import CustomButton from "../CustomButton";
import { useRouter } from "next/router";
import Image from "next/image";

const NoVideoCollection = ({ isVideo }) => {
  const router = useRouter();
  const title = isVideo ? "Video Not Found" : "Collection Not Found";
  const description = isVideo
    ? "The requested video could not be found or may have been removed."
    : "The requested collection could not be found or may no longer exist.";

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
        py: 10,
      }}
    >
      <Image height={250} width={250} alt="patito" src="/patitoB.png" />
      <Typography fontWeight={700} fontSize="1.5rem">
        {title}
      </Typography>
      <Typography fontSize="1.25rem"> {description}</Typography>
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          gap: 2,
          alignItems: "center",
          mt: 5,
        }}
      >
        <CustomButton
          text="Refresh"
          onClick={() => {
            window.location.reload();
          }}
        />
        <CustomButton
          colortype="secondary"
          text="Dashboard"
          onClick={() => {
            router.push("/videos");
          }}
        />
      </Box>
    </Box>
  );
};

export default NoVideoCollection;
