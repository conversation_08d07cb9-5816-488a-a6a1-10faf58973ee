import { Hidden } from "@mui/material";
import Link from "next/link";
import SectionContainer from "./SectionContainer";
import LearningCard from "./LearningCard";
import { memo } from "react";
import EmptyDashboardList from "./EmptyDashboardList";
import ScrollerWithButtons from "./ScrollerWithButtons";
import { LikedListType, ProgressType } from "@/types";
import {
  getCollectionDetails,
  getVideoDetails,
} from "@/utils/videoDashboardUtils";
import { VideosCollectionType, VideoType } from "@/api/mongoTypes";

const linkStyles = {
  color: "#72CAC4",
  fontSize: "14px",
  fontWeight: "500",
};

type MyLearningProps = React.FC<{
  data: ProgressType[];
  setLikedData: React.Dispatch<React.SetStateAction<LikedListType[]>>;
  setFeaturedData: React.Dispatch<React.SetStateAction<VideoType[]>>;
  setCollectionData: React.Dispatch<
    React.SetStateAction<VideosCollectionType[]>
  >;
}>;
const MyLearning: MyLearningProps = ({
  data,
  setLikedData,
  setFeaturedData,
  setCollectionData,
}) => {
  return (
    <SectionContainer title="My Learning">
      {data?.length > 0 ? (
        <>
          <ScrollerWithButtons
            childrenWrapperStyle={{
              display: "flex",
              gap: 4,
              p: 0,
            }}
            containerStyle={{ mb: 2 }}
          >
            {data?.map((item, index) => (
              <LearningCard
                data={item}
                key={index}
                onDislike={(id) => {
                  setLikedData((prev) =>
                    prev.filter(
                      (f) => f.videoId !== id && f.collectionId !== id
                    )
                  );

                  const videoDetails = getVideoDetails(item);
                  if (videoDetails) {
                    setFeaturedData((prev) => {
                      return prev.map((m) => {
                        if (m?._id === id) {
                          return {
                            ...m,
                            isLiked: false,
                          };
                        }
                        return m as any;
                      });
                    });
                  }

                  const collectionDetails = getCollectionDetails(item);
                  if (collectionDetails) {
                    setCollectionData((prev) => {
                      return prev.map((m) => {
                        if (m?._id === id) {
                          return {
                            ...m,
                            isLiked: false,
                          };
                        }
                        return m as any;
                      });
                    });
                  }
                }}
                onlike={() => {
                  const video = getVideoDetails(item);
                  const collection = getCollectionDetails(item);

                  console.log("video", video);
                  console.log("collection", collection);

                  let newData: any = {
                    _id: Date.now(),
                    createdAt: new Date(),
                    updatedAt: new Date(),
                  };

                  if (video?._id) {
                    newData = {
                      ...newData,
                      videoId: String(video._id),
                      type: "video",
                      video: {
                        ...video,
                        isLiked: true,
                      },
                    };
                    setFeaturedData((prev) => {
                      return prev.map((m) => {
                        if (m?._id === video?._id) {
                          return {
                            ...m,
                            isLiked: true,
                          };
                        }
                        return m as any;
                      });
                    });
                  }
                  if (collection?._id) {
                    newData = {
                      ...newData,
                      collectionId: String(collection._id),
                      videoId: null,
                      type: "collection",
                      collection: {
                        ...collection,
                        isLiked: true,
                      },
                      video: null,
                    };
                    setCollectionData((prev) => {
                      return prev.map((m) => {
                        if (m?._id === collection._id) {
                          return {
                            ...m,
                            isLiked: true,
                          };
                        }
                        return m as any;
                      });
                    });
                  }
                  console.log("newData", newData);

                  setLikedData((prev) => [...prev, newData] as any);
                }}
              />
            ))}
          </ScrollerWithButtons>
        </>
      ) : (
        <EmptyDashboardList
          title="No collections in My Learning"
          description="You haven’t started any collections yet. Play some videos or collections and they’ll appear here. New collections coming soon!"
        />
      )}

      <Hidden smDown>
        <Link href="/videos/my-learning" style={linkStyles}>
          Visit My Learning Page
        </Link>
      </Hidden>
    </SectionContainer>
  );
};

export default MyLearning;
