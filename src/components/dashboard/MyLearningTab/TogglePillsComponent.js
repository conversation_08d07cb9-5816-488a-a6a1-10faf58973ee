import React, { useState } from "react";
import { Button, Stack, Box } from "@mui/material";

function TogglePillsComponent({ pillFilters, setSelectedFilters }) {
  const [clickedStates, setClickedStates] = useState(
    pillFilters.map(() => false) // Initialize state for each pill
  );

  const handlePillClick = (index) => {
    setClickedStates((prev) => {
      const updatedStates = [...prev];
      updatedStates[index] = !updatedStates[index]; // Toggle the clicked state for the pill
      return updatedStates;
    });

    setSelectedFilters(
      (prev) =>
        prev.includes(pillFilters[index])
          ? prev.filter((filter) => filter !== pillFilters[index]) // Remove filter if already selected
          : [...prev, pillFilters[index]] // Add filter if not selected
    );
  };

  const pillContainerStyles = {
    display: "flex",
    gap: "12px",
    marginBottom: "20px",
  };

  return (
    <Stack direction="row" spacing={6}>
      <Box style={pillContainerStyles}>
        {pillFilters.map((filter, index) => {
          const buttonStyles = {
            width: "114px",
            fontSize: "12px",
            fontWeight: "400",
            backgroundColor: clickedStates[index] ? "#14A79C" : "#FFFFFF",
            color: clickedStates[index] ? "white" : "#000000",
            borderRadius: "20px",
            outline: clickedStates[index] ? "None" : "1px solid #B3B3B3",
          };

          return (
            <Button
              key={filter}
              onClick={() => handlePillClick(index)}
              size="small"
              sx={buttonStyles}
            >
              {filter}
            </Button>
          );
        })}
      </Box>
    </Stack>
  );
}

export default TogglePillsComponent;
