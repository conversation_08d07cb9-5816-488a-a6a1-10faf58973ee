import { Box, Grid, Typography } from "@mui/material";
import { useState, useEffect } from "react";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import InfiniteScroll from "react-infinite-scroll-component";
import { dashboardContentAllOptions } from "@/constant/dashboard";
import SearchInput from "@/components/SearchInput";
import DropDownComponent from "../DropDownComponent";
import ContentCard from "../ContentCard";
import CollectionCard from "../CollectionCard";
import TogglePillsComponent from "./TogglePillsComponent";
import DashboardCardsLoading from "../DashboardCardsLoading";

const filterContainerStyles = {
  marginTop: "28px",
  display: "flex",
  flexDirection: { xs: "column", sm: "row" },
  alignItems: { xs: "left", sm: "center" },
  gap: 4,
};

const statusContainerStyles = {
  marginTop: "28px",
  marginBottom: "15px",
};

const dropDownStyles = {
  m: 1,
  width: "170px",
  marginTop: { xs: "30px", sm: "0px" },
};
const LIMIT = 10;

const pillFilters = ["IN PROGRESS", "WATCHED"];

export default function MylearningTabSection() {
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState([]);
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [contentTypeFilter, setContentTypeFilter] = useState("All");
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");

  const fetchData = async ({
    skipCount = 0,
    search = "",
    type = "",
    status,
  }) => {
    const handleError = () => {
      setIsLoading(false);
      showSnackbar("Failed to fetch my learning", {
        type: "error",
      });
    };
    if (skipCount === 0) {
      setIsLoading(true);
      setData([]);
    }
    try {
      setIsLoading(true);
      const statuses = (() => {
        const array = [];
        if (status.length === 0) {
          return [];
        }
        if (status.some((s) => s.trim().toUpperCase() === "IN PROGRESS")) {
          array.push(1);
        }
        if (status.some((s) => s.trim().toUpperCase() === "WATCHED")) {
          array.push(2);
        }
        return array;
      })();
      const { data } = await axiosInstance.get("learning/all", {
        params: {
          skip: skipCount,
          limit: LIMIT,
          search,
          type: type === "All" ? "" : type,
          status: statuses.join(","),
        },
      });
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        showSnackbar("Failed to fetch learnings", {
          type: "error",
        });
      }
      setIsLoading(false);
    } catch (error) {
      console.error("Something went wrong in fetchData due to ", error);
      handleError();
    }
  };

  useEffect(() => {
    fetchData({
      skipCount: 0,
      type: contentTypeFilter,
      search: searchTerm,
      status: selectedFilters,
    });
  }, [selectedFilters, searchTerm, contentTypeFilter]);

  const handleSortByFilterChange = (event) => {
    setContentTypeFilter(event.target.value);
  };

  return (
    <>
      <Box sx={filterContainerStyles}>
        <SearchInput
          disabled={isLoading}
          placeholder="Find the perfect course or instructor"
          searchValue={searchTerm}
          handleClear={() => setSearchTerm("")}
          setSearchValue={setSearchTerm}
          isLoading={isLoading}
          handleSearch={() => {}}
        />
        <DropDownComponent
          customStyles={dropDownStyles}
          inputLabel="FILTER"
          choices={dashboardContentAllOptions}
          handleSortByFilterChange={handleSortByFilterChange}
          filter={contentTypeFilter}
        />
      </Box>
      <Box sx={statusContainerStyles}>
        <TogglePillsComponent
          pillFilters={pillFilters}
          setSelectedFilters={setSelectedFilters}
        />
      </Box>
      {isLoading ? (
        <DashboardCardsLoading />
      ) : (
        <InfiniteScroll
          dataLength={data.length}
          next={() =>
            fetchData({
              skipCount: skip,
              search: searchTerm,
              type: contentTypeFilter,
              status: selectedFilters,
            })
          }
          hasMore={hasMore}
          loader={<p style={{ textAlign: "center" }}>Loading</p>}
          endMessage={
            <p style={{ textAlign: "center" }}>
              <b>Yay! You have seen it all</b>
            </p>
          }
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              flexWrap: "wrap",
            }}
          >
            <Grid container spacing={11}>
              {!data ? (
                <Grid item xs={12} sm={6} md={4}>
                  <Typography>
                    Could not find any videos or collections
                  </Typography>
                </Grid>
              ) : (
                data.map((item, index) => (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={4}
                    key={index}
                    sx={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    {item?.videoId && (
                      <ContentCard
                        isLearning
                        isLiked={item?.videoId?.isLiked}
                        isAdmin={false}
                        data={item.videoId}
                      />
                    )}
                    {item?.collectionId && (
                      <CollectionCard
                        isAdmin={false}
                        isLearning
                        onDelete={() => {}}
                        isLiked={item?.collectionId?.isLiked}
                        data={item.collectionId}
                      />
                    )}
                  </Grid>
                ))
              )}
            </Grid>
          </Box>
        </InfiniteScroll>
      )}
    </>
  );
}
