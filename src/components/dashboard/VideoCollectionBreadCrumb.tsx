import { Box, Typography } from "@mui/material";
import Link from "next/link";
import React from "react";

const breadCrumbStyle = {
  color: "#6D6D6D",
  fontSize: "1rem",
  fontWeight: "500",
};

type VideoCollectionBreadCrumbProps = React.FC<{
  title: string;
}>;
const VideoCollectionBreadCrumb: VideoCollectionBreadCrumbProps = ({
  title,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        gap: 4,
        mt: 4,
      }}
    >
      <Link href="/videos" style={breadCrumbStyle}>
        video
      </Link>
      <svg
        width="10"
        height="12"
        viewBox="0 0 10 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.35725 16.3125C1.2542 16.3137 1.15203 16.2935 1.05731 16.2532C0.962584 16.2128 0.877405 16.1532 0.807248 16.0781C0.492962 15.7656 0.492962 15.2813 0.807248 14.9688L7.32868 8.48437L0.807248 2.01562C0.492962 1.70312 0.492962 1.21875 0.807248 0.90625C1.12153 0.59375 1.60868 0.59375 1.92296 0.90625L8.97868 7.95312C9.29296 8.26562 9.29296 8.75 8.97868 9.0625L1.90725 16.0781C1.7501 16.2344 1.54582 16.3125 1.35725 16.3125Z"
          fill="#0B0B0B"
        />
      </svg>
      <Typography sx={breadCrumbStyle}>{title}</Typography>
    </Box>
  );
};

export default VideoCollectionBreadCrumb;
