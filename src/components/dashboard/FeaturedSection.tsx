import SectionContainer from "./SectionContainer";
import EmptyDashboardList from "./EmptyDashboardList";
import ScrollerWithButtons from "./ScrollerWithButtons";
import ContentCard from "./ContentCard";
import { LikedListType, ProgressType } from "@/types";
import { getVideoDetails } from "@/utils/videoDashboardUtils";

type FeaturedSectionProps = React.FC<{
  data: any[];
  setLikedData: React.Dispatch<React.SetStateAction<LikedListType[]>>;
  setLearningData: React.Dispatch<React.SetStateAction<ProgressType[]>>;
}>;
const FeaturedSection: FeaturedSectionProps = ({
  data,
  setLikedData,
  setLearningData,
}) => {
  return (
    <SectionContainer title="Featured">
      {data?.length > 0 ? (
        <>
          <ScrollerWithButtons
            childrenWrapperStyle={{
              display: "flex",
              gap: 4,
              p: 0,
            }}
          >
            {data.map((video, index) => (
              <ContentCard
                isAdmin={false}
                onlike={() => {
                  const newData = {
                    _id: Date.now(),
                    collectionId: null,
                    videoId: String(video._id),
                    type: "video",
                    video: {
                      ...video,
                      isLiked: true,
                    },
                    collection: null,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                  };
                  setLikedData((prev) => [...prev, newData] as any);
                  setLearningData((prev) => {
                    return prev.map((m) => {
                      const vidDetails = getVideoDetails(m);
                      console.log({
                        m,
                        vidDetails,
                      });
                      if (vidDetails?._id === video._id) {
                        return {
                          ...m,
                          videoId: {
                            ...vidDetails,
                            isLiked: true,
                          },
                        };
                      }
                      return m as any;
                    });
                  });
                }}
                onDislike={() => {
                  setLikedData((prev) =>
                    prev.filter((f) => f.videoId !== video._id)
                  );
                  setLearningData((prev) => {
                    return prev.map((m) => {
                      const vidDetails = getVideoDetails(m);
                      if (vidDetails?._id === video._id) {
                        return {
                          ...m,
                          videoId: {
                            ...vidDetails,
                            isLiked: false,
                          },
                        };
                      }
                      return m as any;
                    });
                  });
                }}
                isLearning={false}
                onDelete={() => {}}
                isLiked={video?.isLiked}
                key={index}
                data={video}
              />
            ))}
          </ScrollerWithButtons>
        </>
      ) : (
        <EmptyDashboardList
          title="No featured content found"
          description="We will be adding new content soon. Stay tuned!"
        />
      )}
    </SectionContainer>
  );
};
export default FeaturedSection;
