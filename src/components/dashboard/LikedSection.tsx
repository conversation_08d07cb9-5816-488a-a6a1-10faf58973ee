/* eslint-disable react/no-unescaped-entities */
import { LikedListType, ProgressType } from "@/types";
import SectionContainer from "./SectionContainer";
import ContentCard from "./ContentCard";
import CollectionCard from "./CollectionCard";
import EmptyDashboardList from "./EmptyDashboardList";
import ScrollerWithButtons from "./ScrollerWithButtons";
import { VideosCollectionType, VideoType } from "@/api/mongoTypes";
import {
  getCollectionDetails,
  getVideoDetails,
} from "@/utils/videoDashboardUtils";

type LikedSectionProps = React.FC<{
  data: LikedListType[];
  setLikedData: React.Dispatch<React.SetStateAction<LikedListType[]>>;
  setCollectionData: React.Dispatch<
    React.SetStateAction<VideosCollectionType[]>
  >;
  setLearningData: React.Dispatch<React.SetStateAction<ProgressType[]>>;
  setFeaturedData: React.Dispatch<React.SetStateAction<VideoType[]>>;
}>;
const LikedSection: LikedSectionProps = ({
  data,
  setLikedData,
  setCollectionData,
  setLearningData,
  setFeaturedData,
}) => {
  return (
    <SectionContainer title="Liked">
      {!data || data?.length === 0 ? (
        <EmptyDashboardList
          description="You haven't liked any videos or collections yet. Explore content and tap the 'like' button to save your favorites here for easy access later."
          title="No liked video/collection found"
        />
      ) : (
        <ScrollerWithButtons
          childrenWrapperStyle={{
            display: "flex",
            gap: 4,
            p: 0,
          }}
        >
          {data?.map((item, index) => {
            if (item?.video?._id) {
              return (
                <ContentCard
                  key={index}
                  isAdmin={false}
                  data={item?.video}
                  isLiked={item?.video?.isLiked}
                  onDislike={() => {
                    setLikedData((prev) =>
                      prev.filter((f) => f._id !== item._id)
                    );
                    setLearningData((prev) => {
                      return prev.map((m) => {
                        const vidDetails = getVideoDetails(m);
                        console.log({
                          m,
                          vidDetails,
                          item,
                        });
                        if (vidDetails?._id === item?.video?._id) {
                          return {
                            ...m,
                            videoId: {
                              ...vidDetails,
                              isLiked: false,
                            },
                          };
                        }
                        return m as any;
                      });
                    });
                    setFeaturedData((prev) => {
                      return prev.map((m) => {
                        if (m?._id === item?.video?._id) {
                          return {
                            ...m,
                            isLiked: false,
                          };
                        }
                        return m as any;
                      });
                    });
                  }}
                />
              );
            }

            if (item?.collection?._id) {
              return (
                <CollectionCard
                  key={index}
                  isAdmin={false}
                  isLiked={item?.collection?.isLiked}
                  onDelete={() => {}}
                  data={item.collection}
                  onDislike={() => {
                    setLikedData((prev) =>
                      prev.filter((f) => f._id !== item._id)
                    );
                    setCollectionData((prev) => {
                      return prev.map((m) => {
                        if (m?._id === item?.collection?._id) {
                          return {
                            ...m,
                            isLiked: false,
                          };
                        }
                        return m as any;
                      });
                    });
                    setLearningData((prev) => {
                      return prev.map((m) => {
                        const collectionDetaiils = getCollectionDetails(m);
                        if (collectionDetaiils?._id === item?.collection?._id) {
                          return {
                            ...m,
                            collectionId: {
                              ...collectionDetaiils,
                              isLiked: false,
                            },
                          };
                        }
                        return m as any;
                      });
                    });
                  }}
                />
              );
            }
          })}
        </ScrollerWithButtons>
      )}
    </SectionContainer>
  );
};

export default LikedSection;
