import { Box, Skeleton } from "@mui/material";
import React from "react";

const DashboardCardsLoading = () => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        flexWrap: "wrap",
        gap: 2,
        justifyContent: "center",
        width: "100%",
      }}
    >
      {new Array(10).fill("").map((m, i) => (
        <Box
          key={i}
          sx={{
            width: 250,
            height: 300,
            borderRadius: 3,
            mb: 2,
            overflow: "hidden",
          }}
        >
          <Skeleton variant="rectangular" width="100%" height="100%" />
        </Box>
      ))}
    </Box>
  );
};

export default DashboardCardsLoading;
