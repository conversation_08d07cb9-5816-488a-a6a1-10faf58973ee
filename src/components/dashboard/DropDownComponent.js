import * as React from "react";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import { Typography } from "@mui/material";

export default function DropDownComponent({
  customStyles,
  inputLabel,
  choices,
  handleSortByFilterChange,
  filter,
}) {
  return (
    <FormControl
      size="medium"
      sx={{
        ...customStyles,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        gap: 2,
        "& fieldset": { border: "none" },
      }}
    >
      <Typography>Filter:</Typography>
      {/* {inputLabel ? (
        <InputLabel id="demo-select-small-label" sx={inputLabelStyles}>
          {inputLabel}
        </InputLabel>
      ) : (
        <>
        <InputLabel id="demo-select-small-label">Filter</InputLabel>
        </>
      )} */}
      <Select
        labelId="demo-select-small-label"
        id="demo-select-small"
        value={filter ?? ""}
        label="Content Type"
        onChange={handleSortByFilterChange}
      >
        {choices.map((choice) => (
          <MenuItem value={choice.value} key={choice.value}>
            {choice.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}
