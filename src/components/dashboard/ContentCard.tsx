import { <PERSON>, Typo<PERSON>, Avatar, Button } from "@mui/material";
import TagComponent from "./TagComponent";
import Image from "next/image";
import LikeComponent from "./LikeComponent";

// this is temporary - need to pull image url from db
import avatar from "@/../../public/images/dashboard/avatar.webp";
import img from "@/../../public/images/dashboard/mylearning.webp";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";
import { UserType, VideoProgressSchemaType, VideoType } from "@/api/mongoTypes";
import { getUserFullName } from "@/utils/format";
import { getProficiencyEn } from "@/utils/common";
import ProgressBar from "./ProgressBar";
import { useUserContext } from "@/contexts/UserContext";
import { getVideoProgress } from "@/utils/dashboard";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { PROGRESS_STATUS } from "@/constant/Enums";
import { LikedListType } from "@/types";

const titleStyles = {
  fontSize: "20px",
  fontWeight: "500px",
  color: "black",
};
const imageStyles = {
  borderRadius: "15px",
};

const tagStyles = {
  backgroundColor: "#D7F7F5", // light teal color
  color: "#14A79C", // teal color for text
  fontSize: "13px",
  fontWeight: "500",
  marginRight: "5px",
};

const cardContainerStyles = {
  display: "flex",
  border: "1px solid #E6E6E6",
  borderRadius: "15px",
  padding: "15px",
  width: "298px",
  height: "366px",
  flexDirection: "column",
};

const AvatarOverlayStyles = {
  position: "absolute",
  bottom: 15,
  left: 8,
  display: "flex",
  alignItems: "center",
  bgcolor: "white",
  padding: "4px 8px",
  borderRadius: "16px",
  zIndex: "2",
};
const subtitleStyles = {
  fontSize: "14px",
  fontWeight: "400",
  color: "#6D6D6D",
};
const descriptionStyles = {
  fontSize: "16px",
  fontWeight: "400",
  color: "#6D6D6D",
  marginBottom: "10px",
  display: "-webkit-box",
  overflow: "hidden",
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 2, // Limits text to 2 lines and truncates
};

const GradientStyles = {
  borderRadius: "15px",
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: "linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7))",
  zIndex: 1,
};

const videoStatusContainerStyles = {
  position: "absolute",
  top: 8,
  left: 20,
  right: 0,
  bottom: 0,
  zIndex: 1,
};

const videoStatusTitleStyles = {
  color: "white",
  fontWeight: "500",
  fontSize: "12px",
};

const likedComponentStyles = { top: 10, right: 10, position: "absolute" };
const tagContainerStyles = {
  display: "flex",
  overflow: "auto",
  marginBottom: "20px",
  overflowX: "scroll",
  scrollSnapType: "x mandatory",

  "::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none",
};

const levelTitleStyles = {
  color: "#B3B3B3",
  fontSize: "14px",
  fontWeight: "500",
  marginRight: "10px",
  display: "flex",
  alignItems: "center",
  justifyContent: "left",
};

const percentageTextStyles = {
  fontSize: { xs: "10px", sm: "14px" },
  fontWeight: { xs: "500", sm: "400" },
  color: "#000",
};

type ContentCardProps = React.FC<{
  data: VideoType;
  isLiked?: boolean;
  isAdmin?: boolean;
  collectionId?: string;
  isLearning?: boolean;
  onDislike?: (id: string) => void;
  onDelete?: (id: string) => void;
  onlike?: (data: VideoType) => void;
}>;
const ContentCard: ContentCardProps = ({
  data,
  isAdmin = false,
  // isLiked = false,
  isLearning = false,
  collectionId = "",
  onDislike = () => {},
  onDelete = (id) => {},
  onlike = (data) => {},
}) => {
  const [isLiked, setIsLiked] = useState(data.isLiked);
  const [isDeleting, setIsDeleting] = useState(false);
  const { showSnackbar } = useSnackbar();
  const { dbUser } = useUserContext();
  const publicUrl = `/videos/${data._id}${
    collectionId ? `?collectionId=${collectionId}` : ""
  }`;
  const href = `${isAdmin ? `/create/video/edit/${data._id}` : publicUrl}`;

  useEffect(() => {
    setIsLiked(data.isLiked);
  }, [data]);

  const creatorDetails = useMemo(() => {
    const creator = data.creator;
    if (
      creator &&
      typeof creator !== "string" &&
      creator?._id &&
      "profileImageUrl" in creator
    ) {
      return creator as UserType;
    }
    return null;
  }, [data]);

  const proficiency = useMemo(() => {
    return getProficiencyEn({
      data: data.proficiencyLevel,
    });
  }, [data]);

  const progress = useMemo(() => {
    return getVideoProgress(data?.progress);
  }, [data]);

  const handleDelete = async () => {
    const handleError = () => {
      setIsDeleting(false);
      showSnackbar("Failed to delete collection", {
        type: "error",
      });
    };

    try {
      setIsDeleting(true);
      const { data: deletedData } = await axiosInstance.delete(
        `video/delete/${data._id}`
      );
      if (deletedData.success && deletedData?.data) {
        showSnackbar("Deleted the collection successfully", {
          type: "success",
        });
        onDelete && onDelete(data._id);
      } else {
        handleError();
      }
      setIsDeleting(false);
    } catch (error) {
      handleError();
    }
  };

  return (
    <Link href={href}>
      <Box sx={cardContainerStyles}>
        <Box position="relative">
          {data.thumbnailUrl ? (
            <Image
              src={data.thumbnailUrl}
              width={269}
              height={143}
              style={{
                ...imageStyles,
                width: "269px",
                height: "143px",
              }}
              alt="alt title"
            />
          ) : (
            <Box
              sx={{
                background: "gray",
                width: 269,
                height: 143,
                ...imageStyles,
              }}
            />
          )}

          {!isAdmin && <ContentProgress progress={data?.progress} />}

          {creatorDetails && (
            <Box sx={AvatarOverlayStyles}>
              <Avatar
                alt="Alice"
                src={creatorDetails.profileImageUrl}
                sx={{ width: 24, height: 24, marginRight: 1 }}
              />
              <Typography variant="body2" color="text.primary">
                {getUserFullName(creatorDetails)}
              </Typography>
            </Box>
          )}

          {!isAdmin && (
            <LikeComponent
              id={data._id}
              isCollection={false}
              styles={likedComponentStyles}
              isLiked={isLiked}
              onlike={() => {
                onlike && onlike(data);
              }}
              onDislike={() => {
                onDislike && onDislike(data._id);
              }}
            />
          )}
        </Box>

        <Box sx={{ width: "268px" }}>
          <Box sx={{ paddingBottom: "15px" }}>
            <Typography noWrap sx={titleStyles}>
              {data.title}
            </Typography>

            {/* <Typography sx={subtitleStyles}>{data.collection}</Typography> */}
          </Box>
          <Box>
            <Typography sx={descriptionStyles}>{data.description}</Typography>

            {isLearning && (
              <Box width="100%">
                <ProgressBar value={+progress} />
                <Typography sx={percentageTextStyles}>
                  {+progress.toFixed(2)}% Complete
                </Typography>
              </Box>
            )}

            {data?.categories?.length > 0 && (
              <Box sx={tagContainerStyles}>
                {data?.categories?.map((tag, index) => (
                  <TagComponent
                    key={tag}
                    label={tag}
                    tagStyles={tagStyles}
                  ></TagComponent>
                ))}
              </Box>
            )}
            {proficiency && (
              <Typography sx={levelTitleStyles}>{proficiency}</Typography>
            )}
          </Box>
        </Box>
        {isAdmin && (
          <Button
            type="submit"
            sx={{ width: "100%" }}
            onClick={(e) => {
              e.preventDefault();
              handleDelete();
            }}
            disabled={isDeleting}
          >
            Delete
          </Button>
        )}
      </Box>
    </Link>
  );
};

type ContentProgressProps = React.FC<{
  progress: VideoProgressSchemaType;
}>;
const ContentProgress: ContentProgressProps = ({ progress }) => {
  const statusText = useMemo(() => {
    if (progress?.state === PROGRESS_STATUS.IN_PROGRESS) {
      return "IN PROGRESS";
    }
    if (progress?.state === PROGRESS_STATUS.COMPLETE) {
      return "COMPLETED";
    }
    return "NOT STARTED";
  }, [progress]);

  // if (!progress || !progress?._id) {
  //   return null;
  // }

  return (
    <>
      <Box sx={GradientStyles} />
      <Box sx={videoStatusContainerStyles}>
        <Typography sx={videoStatusTitleStyles}>
          &#x2022; {statusText}
        </Typography>
      </Box>
    </>
  );
};

export default ContentCard;
