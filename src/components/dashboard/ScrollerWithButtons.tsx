import { MUIStyle } from "@/types";
import { Box } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";

type ScrollerWithButtonsProps = React.FC<{
  children: React.ReactNode;
  childrenWrapperStyle?: MUIStyle;
  containerStyle?: MUIStyle;
  scrollByAmount?: number;
}>;
const ScrollerWithButtons: ScrollerWithButtonsProps = ({
  children,
  childrenWrapperStyle = {},
  containerStyle = {},
  scrollByAmount = 200,
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  const checkScroll = () => {
    if (!scrollRef.current) return;
    const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft + clientWidth < scrollWidth);
  };

  useEffect(() => {
    checkScroll();
    const el = scrollRef.current;
    if (!el) return;
    el.addEventListener("scroll", checkScroll);
    window.addEventListener("resize", checkScroll);
    return () => {
      el.removeEventListener("scroll", checkScroll);
      window.removeEventListener("resize", checkScroll);
    };
  }, []);

  const handleScrollLeft = () => {
    scrollRef.current?.scrollBy({ left: -scrollByAmount, behavior: "smooth" });
  };

  const handleScrollRight = () => {
    scrollRef.current?.scrollBy({ left: scrollByAmount, behavior: "smooth" });
  };

  return (
    <Box sx={{ position: "relative", ...containerStyle }}>
      <Box
        sx={{
          borderRadius: "15px",
          padding: "20px 40px",
          width: "100%",
          whiteSpace: "nowrap",
          overflowX: "scroll",
          overflowY: "hidden",
          "::-webkit-scrollbar": {
            display: "none",
          },
          scrollbarWidth: "none",
          ...childrenWrapperStyle,
        }}
        ref={scrollRef}
      >
        {children}
      </Box>

      {canScrollLeft && (
        <Box
          onClick={(e) => {
            e.preventDefault();
            handleScrollLeft();
          }}
          sx={{
            position: "absolute",
            height: 50,
            width: 50,
            top: "50%",
            left: 0,
            transform: "translateY(-50%)",
            background: "rgba(255, 255, 255, 0.8)",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
            boxShadow: "0px 4px 10px 0px #0000001A",
            zIndex: 10,
          }}
        >
          <svg
            width="31"
            height="31"
            viewBox="0 0 31 31"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{ transform: "rotate(180deg)" }}
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M20.2882 14.5868C20.5304 14.829 20.6664 15.1575 20.6664 15.5C20.6664 15.8425 20.5304 16.171 20.2882 16.4132L12.9812 23.7202C12.8621 23.8435 12.7196 23.9419 12.562 24.0096C12.4044 24.0773 12.2349 24.1129 12.0634 24.1144C11.8919 24.1159 11.7218 24.0832 11.5631 24.0183C11.4043 23.9534 11.2601 23.8574 11.1388 23.7362C11.0175 23.6149 10.9216 23.4707 10.8567 23.3119C10.7917 23.1532 10.7591 22.9831 10.7605 22.8116C10.762 22.6401 10.7977 22.4706 10.8654 22.313C10.9331 22.1554 11.0315 22.0129 11.1548 21.8937L17.5486 15.5L11.1548 9.10624C10.9195 8.86263 10.7893 8.53635 10.7923 8.19768C10.7952 7.85901 10.9311 7.53504 11.1706 7.29556C11.41 7.05607 11.734 6.92023 12.0727 6.91729C12.4114 6.91434 12.7376 7.04453 12.9812 7.27982L20.2882 14.5868Z"
              fill="black"
            />
          </svg>
        </Box>
      )}

      {canScrollRight && (
        <Box
          onClick={(e) => {
            e.preventDefault();
            handleScrollRight();
          }}
          sx={{
            position: "absolute",
            height: 50,
            width: 50,
            top: "50%",
            right: 0,
            transform: "translateY(-50%)",
            background: "rgba(255, 255, 255, 0.8)",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            cursor: "pointer",
            boxShadow: "0px 4px 10px 0px #0000001A",
            zIndex: 10,
          }}
        >
          <svg
            width="31"
            height="31"
            viewBox="0 0 31 31"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M20.2882 14.5868C20.5304 14.829 20.6664 15.1575 20.6664 15.5C20.6664 15.8425 20.5304 16.171 20.2882 16.4132L12.9812 23.7202C12.8621 23.8435 12.7196 23.9419 12.562 24.0096C12.4044 24.0773 12.2349 24.1129 12.0634 24.1144C11.8919 24.1159 11.7218 24.0832 11.5631 24.0183C11.4043 23.9534 11.2601 23.8574 11.1388 23.7362C11.0175 23.6149 10.9216 23.4707 10.8567 23.3119C10.7917 23.1532 10.7591 22.9831 10.7605 22.8116C10.762 22.6401 10.7977 22.4706 10.8654 22.313C10.9331 22.1554 11.0315 22.0129 11.1548 21.8937L17.5486 15.5L11.1548 9.10624C10.9195 8.86263 10.7893 8.53635 10.7923 8.19768C10.7952 7.85901 10.9311 7.53504 11.1706 7.29556C11.41 7.05607 11.734 6.92023 12.0727 6.91729C12.4114 6.91434 12.7376 7.04453 12.9812 7.27982L20.2882 14.5868Z"
              fill="black"
            />
          </svg>
        </Box>
      )}
    </Box>
  );
};

export default ScrollerWithButtons;
