import { Box, Grid, Typography } from "@mui/material";
import DropDownComponent from "./DropDownComponent";
import { useState, useEffect } from "react";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { useUserContext } from "@/contexts/UserContext";
import ContentCard from "./ContentCard";
import CollectionCard from "./CollectionCard";
import { dashboardContentAllOptions } from "@/constant/dashboard";
import useDebounce from "@/hooks/useDebounce";
import InfiniteScroll from "react-infinite-scroll-component";
import SearchInput from "../SearchInput";
import DashboardCardsLoading from "./DashboardCardsLoading";

const filterContainerStyles = {
  marginTop: "28px",
  marginBottom: "28px",
  display: "flex",
  flexDirection: { xs: "column", sm: "row" },
  alignItems: { xs: "left", sm: "center" },
  gap: 4,
};

const dropDownStyles = {
  m: 1,
  width: "170px",
  marginTop: { xs: "30px", sm: "0px" },
};

const LIMIT = 6;

export default function LikedTabSection() {
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState([]);
  const { showSnackbar } = useSnackbar();
  const [hasMore, setHasMore] = useState(false);
  const [skip, setSkip] = useState(0);
  const { dbUser } = useUserContext();
  const isLoggedin = !!dbUser;
  const [searchTerm, setSearchTerm] = useState("");
  const [contentTypeFilter, setContentTypeFilter] = useState("All");
  const debouncedSearchTerm = useDebounce(searchTerm, 1000);

  console.log({
    data,
  });

  const fetchData = async ({
    skipCount = 0,
    search = "",
    contentTypeFilter,
  }) => {
    const showLoading = +skipCount === 0;
    const handleError = () => {
      setIsLoading(false);
      showSnackbar("Failed to fetch liked videos", {
        type: "error",
      });
    };
    if (showLoading) {
      setIsLoading(true);
      setData([]);
    }

    try {
      setIsLoading(true);
      const { data: respData } = await axiosInstance.get("dashboard/liked", {
        params: {
          skip: skipCount,
          limit: LIMIT,
          search,
          contentType: contentTypeFilter === "All" ? "" : contentTypeFilter,
        },
      });
      if (respData.success && respData?.data) {
        const length = respData?.data?.length;
        if (showLoading) {
          setData(respData.data);
        } else {
          setData((prev) => [...prev, ...respData.data]);
        }
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        handleError();
      }
      setIsLoading(false);
    } catch (error) {
      handleError();
    }
  };

  useEffect(() => {
    if (isLoggedin) {
      fetchData({
        skipCount: 0,
        search: debouncedSearchTerm,
        contentTypeFilter,
      });
    }
  }, [isLoggedin, debouncedSearchTerm, contentTypeFilter]);

  const handleSortByFilterChange = (event) => {
    setContentTypeFilter(event.target.value);
  };

  const inputLabel = "SORT BY";

  return (
    <>
      <Box sx={filterContainerStyles}>
        <SearchInput
          disabled={isLoading}
          placeholder="Find the perfect course or instructor"
          searchValue={searchTerm}
          handleClear={() => setSearchTerm("")}
          setSearchValue={setSearchTerm}
          isLoading={isLoading}
          handleSearch={() => {}}
        />
        <DropDownComponent
          customStyles={dropDownStyles}
          inputLabel={inputLabel}
          choices={dashboardContentAllOptions}
          handleSortByFilterChange={handleSortByFilterChange}
          filter={contentTypeFilter}
        />
      </Box>
      {isLoading ? (
        <DashboardCardsLoading />
      ) : !data ? (
        <Grid item xs={12} sm={6} md={4}>
          <Typography>Could not find any videos or collections</Typography>
        </Grid>
      ) : (
        <InfiniteScroll
          dataLength={data.length}
          next={() =>
            fetchData({
              skipCount: skip,
              search: debouncedSearchTerm,
              contentTypeFilter,
            })
          }
          hasMore={hasMore}
          loader={<p style={{ textAlign: "center" }}>Loading</p>}
          endMessage={
            <p style={{ textAlign: "center" }}>
              <b>Yay! You have seen it all</b>
            </p>
          }
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              flexWrap: "wrap",
            }}
          >
            <Grid container spacing={11}>
              {data.map((item, index) => (
                <Grid
                  item
                  xs={12}
                  sm={6}
                  md={4}
                  key={index}
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {item?.video?._id && (
                    <ContentCard
                      isAdmin={false}
                      isLiked
                      data={item.video}
                      onDislike={() => {
                        setData((prev) =>
                          prev.filter((f) => f._id !== item._id)
                        );
                      }}
                    />
                  )}
                  {item?.collection?._id && (
                    <CollectionCard
                      isLiked
                      isAdmin={false}
                      onDelete={() => {}}
                      data={item.collection}
                      onDislike={() => {
                        setData((prev) =>
                          prev.filter((f) => f._id !== item._id)
                        );
                      }}
                    />
                  )}
                </Grid>
              ))}
            </Grid>
          </Box>
        </InfiniteScroll>
      )}
    </>
  );
}
