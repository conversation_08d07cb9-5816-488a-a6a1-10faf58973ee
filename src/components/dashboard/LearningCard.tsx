import { Box, Typography } from "@mui/material";
import TagComponent from "./TagComponent";
import LikeComponent from "./LikeComponent";
import ProgressBar from "./ProgressBar";
import { useMemo } from "react";
import {
  getCollectionDetails,
  getIsLiked,
  getLearningCategories,
  getLearningDescription,
  getLearningDetailPageUrl,
  getLearningId,
  getLearningImage,
  getLearningName,
  getLearningProficiencyLevel,
  getLearningProgress,
  getVideoDetails,
} from "@/utils/videoDashboardUtils";
import Image from "next/image";
import { ProgressType } from "@/types";
import Link from "next/link";
import { VideosCollectionType, VideoType } from "@/api/mongoTypes";

const videoComponentStyles = {
  display: "flex",
  width: { xs: "358px", sm: "574px" },
  height: { xs: "155px", sm: "205px" },
  border: "1px solid #E6E6E6",
  borderRadius: { xs: "18px", sm: "15px" },
  padding: { xs: "10px", sm: "15px" },
  position: "relative",
};

const imageContainerStyles = {
  display: "flex",
  alignItems: "center",
  borderRadius:
    "15px 0 0 15px" /* top-left, top-right, bottom-right, bottom-left */,
  // width: { xs: "79px", sm: "145px" },
  // height: { xs: "135px", sm: "180px" },
};

const levelStyles = {
  color: "#B3B3B3",
  fontSize: { xs: "9px", sm: "14px" },
  fontWeight: { xs: "400", sm: "500" },
  marginRight: "10px",
  display: "flex",
  alignItems: "center",
  justifyContent: "left",
};

const titleStyles = {
  fontSize: { xs: "16px", sm: "20px" },
  fontWeight: "500px",
  color: "black",
};

const subTitleStyles = {
  color: "#6D6D6D",
  fontSize: { xs: "10px", sm: "14px" },
  fontWeight: "400",
};

const tagStyles = {
  backgroundColor: "#D7F7F5", // light teal color
  color: "#14A79C", // teal color for text
  fontSize: { xs: "8px", sm: "13px" },
  fontWeight: { xs: "400", sm: "500" },
  marginRight: "5px",
};

const contentContainerStyles = {
  width: "100%",
  marginLeft: "15px",
  borderRadius: " 0 15px 15px 0",
};

const percentageTextStyles = {
  fontSize: { xs: "10px", sm: "14px" },
  fontWeight: { xs: "500", sm: "400" },
};

const imageStyles = {
  width: { xs: "79px", sm: "145px" },
  height: { xs: "135px", sm: "180px" },
  objectFit: "cover",
  borderRadius: "15px",
};

const likedComponentStyles = { top: 10, right: 10, position: "absolute" };

type LearningCardProps = React.FC<{
  data: ProgressType;
  onDislike?: (id: string) => void;
  onlike?: (data: VideoType | VideosCollectionType) => void;
}>;

const LearningCard: LearningCardProps = ({
  data,
  onlike = () => {},
  onDislike = () => {},
}) => {
  const name = useMemo(() => {
    return getLearningName(data);
  }, [data]);

  const describe = useMemo(() => {
    return getLearningDescription(data);
  }, [data]);

  const categories = useMemo(() => {
    return getLearningCategories(data);
  }, [data]);

  const progress = useMemo(() => {
    return getLearningProgress(data);
  }, [data]);

  const proficiencyLevel = useMemo(() => {
    return getLearningProficiencyLevel(data);
  }, [data]);

  const imgSrc = useMemo(() => {
    return getLearningImage(data);
  }, [data]);

  const isLiked = useMemo(() => {
    return getIsLiked(data);
  }, [data]);

  const url = useMemo(() => {
    return getLearningDetailPageUrl(data);
  }, [data]);

  const id = useMemo(() => {
    return getLearningId(data);
  }, [data]);

  return (
    <Box sx={videoComponentStyles}>
      <Box sx={imageContainerStyles}>
        <Box sx={{ ...imageStyles, position: "relative", overflow: "hidden" }}>
          <Image src={imgSrc} alt={String(name)} fill />
        </Box>
      </Box>
      <Box sx={contentContainerStyles}>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            flexWrap: "wrap",
            width: "100%",
          }}
        >
          <Typography sx={levelStyles}>{proficiencyLevel}</Typography>
        </Box>

        <Box sx={{ width: { xs: "200px", sm: "375px" } }}>
          <Link href={url}>
            <Typography sx={titleStyles} noWrap>
              {name}
            </Typography>
          </Link>
          <Typography sx={subTitleStyles} noWrap>
            {describe}
          </Typography>

          <ProgressBar value={progress} />
          <Typography sx={percentageTextStyles}>
            {+progress.toFixed()}% Complete
          </Typography>
          <Box
            sx={{
              marginBottom: { xs: "5px", sm: "0px" },
              marginTop: { xs: "5px", sm: "0px" },
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              gap: 2,
              overflowX: "scroll",
              "::-webkit-scrollbar": {
                display: "none",
              },
              scrollbarWidth: "none",
            }}
          >
            {categories.map((m) => (
              <TagComponent key={String(m)} label={m} tagStyles={tagStyles} />
            ))}
          </Box>
        </Box>
      </Box>
      <Box>
        <LikeComponent
          id={id}
          isLiked={isLiked}
          isCollection={data.type === "collection"}
          styles={likedComponentStyles}
          onlike={() => {
            const videoDetails = getVideoDetails(data);
            const collectionDetails = getCollectionDetails(data);
            if (videoDetails) {
              onlike && onlike(videoDetails);
            }
            if (collectionDetails) {
              onlike && onlike(collectionDetails);
            }
          }}
          onDislike={() => {
            const videoDetails = getVideoDetails(data);
            const collectionDetails = getCollectionDetails(data);
            if (videoDetails) {
              onDislike && onDislike(videoDetails._id);
            }
            if (collectionDetails) {
              onDislike && onDislike(collectionDetails._id);
            }
          }}
        />
      </Box>
    </Box>
  );
};

export default LearningCard;
