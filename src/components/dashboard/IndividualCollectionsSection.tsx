import SectionContainer from "./SectionContainer";
import EmptyDashboardList from "./EmptyDashboardList";
import ScrollerWithButtons from "./ScrollerWithButtons";
import CollectionCard from "./CollectionCard";
import { VideosCollectionType } from "@/api/mongoTypes";
import { LikedListType, ProgressType } from "@/types";
import { getCollectionDetails } from "@/utils/videoDashboardUtils";

type IndividualCollectionsSectionProps = React.FC<{
  data: VideosCollectionType[];
  setLikedData: React.Dispatch<React.SetStateAction<LikedListType[]>>;
  setLearningData: React.Dispatch<React.SetStateAction<ProgressType[]>>;
}>;
const IndividualCollectionsSection: IndividualCollectionsSectionProps = ({
  data,
  setLikedData,
  setLearningData,
}) => {
  return (
    <SectionContainer title="Individual Collections">
      {data?.length > 0 ? (
        <ScrollerWithButtons
          childrenWrapperStyle={{
            display: "flex",
            gap: 4,
            p: 0,
          }}
        >
          {data.map((collection, index) => (
            <CollectionCard
              key={index}
              isAdmin={false}
              onDelete={() => {}}
              isLearning={false}
              onDislike={() => {
                setLikedData((prev) =>
                  prev.filter((f) => f.collectionId !== collection._id)
                );
                setLearningData((prev) => {
                  return prev.map((m) => {
                    const collectionDetaiils = getCollectionDetails(m);
                    if (collectionDetaiils?._id === collection._id) {
                      return {
                        ...m,
                        collectionId: {
                          ...collection,
                          isLiked: false,
                        },
                      };
                    }
                    return m as any;
                  });
                });
              }}
              data={collection}
              isLiked={collection?.isLiked}
              onlike={() => {
                const newData = {
                  _id: Date.now(),
                  collectionId: String(collection._id),
                  videoId: null,
                  type: "collection",
                  collection: {
                    ...collection,
                    isLiked: true,
                  },
                  video: null,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                };
                setLikedData((prev) => [...prev, newData] as any);
                setLearningData((prev) => {
                  return prev.map((m) => {
                    const collectionDetaiils = getCollectionDetails(m);
                    if (collectionDetaiils?._id === collection._id) {
                      return {
                        ...m,
                        collectionId: {
                          ...collection,
                          isLiked: true,
                        },
                      };
                    }
                    return m as any;
                  });
                });
              }}
            />
          ))}
        </ScrollerWithButtons>
      ) : (
        <EmptyDashboardList
          title="No collections found"
          description="We will be adding new collections soon. Stay tuned!"
        />
      )}
    </SectionContainer>
  );
};

export default IndividualCollectionsSection;
