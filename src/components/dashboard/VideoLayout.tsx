import { MUIStyle } from "@/types";
import { Box, Container, SxProps, Theme, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useState } from "react";

const titleStyles: MUIStyle = {
  textAlign: "center",
  fontWeight: "500",
  fontSize: "1.75rem",
  margin: {
    xs: "1rem 0px",
    md: "30px 0px",
  },
};

const tabs = [
  {
    id: 1,
    name: "Dashboard",
    href: "/videos",
  },
  {
    id: 2,
    name: "My Learning",
    href: "/videos/my-learning",
  },
  {
    id: 3,
    name: "Liked",
    href: "/videos/liked",
  },
];

const parentTabContainerStyles = { width: "100%" };
const tabLabelContainerStyles: MUIStyle = {
  borderBottom: 2,
  borderColor: "#E8B931",
  display: "flex",
  flexDirection: "row",
  color: "#3C3C3C",
  gap: 6,
  width: "100%",
  mb: 8,
  flexWrap: "wrap",
};

type VideoLayoutProps = React.FC<{
  children: React.ReactNode;
}>;
const VideoLayout: VideoLayoutProps = ({ children }) => {
  const router = useRouter();

  return (
    <Container>
      <Typography sx={titleStyles}>Videos</Typography>
      <Box sx={parentTabContainerStyles}>
        <Box sx={tabLabelContainerStyles}>
          {tabs.map((m, i) => (
            <Tab
              key={i}
              label={m.name}
              href={m.href}
              active={(() => {
                if (m.id === 1) {
                  return (
                    router.pathname === m.href || router.pathname === "/videos/"
                  );
                }
                return router.pathname === m.href;
              })()}
            />
          ))}
        </Box>
        {children}
      </Box>
    </Container>
  );
};

const Tab = ({
  label,
  href,
  active,
}: {
  label: string;
  href: string;
  active: boolean;
}) => {
  return (
    <Link href={href}>
      <Typography
        color={active ? "black" : "#3C3C3C"}
        sx={{
          fontWeight: active ? 700 : 500,
          borderBottom: active ? "4px solid #E8B931" : "2px solid transparent",
          px: {
            xs: 1,
            md: 3,
            lg: 4,
          },
          py: 1,
          cursor: "pointer",
          fontSize: {
            xs: "0.75rem",
            md: "1rem",
          },
        }}
      >
        {label}
      </Typography>
    </Link>
  );
};

export default VideoLayout;
