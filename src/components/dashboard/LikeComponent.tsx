import { IconButton } from "@mui/material";
import FavoriteIcon from "@mui/icons-material/Favorite";
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorder";
import { useEffect, useState } from "react";
import axiosInstance from "@/utils/interceptor";
import { useSnackbar } from "@/hooks/useSnackbar";

type LikeComponentProps = React.FC<{
  styles?: object;
  id: string;
  isCollection: boolean;
  isLiked: boolean;
  onDislike?: () => void;
  onlike?: () => void;
}>;

const LikeComponent: LikeComponentProps = ({
  styles = {},
  id,
  isCollection = false,
  isLiked = false,
  onDislike = () => {},
  onlike = () => {},
}) => {
  const { showSnackbar } = useSnackbar();
  const [isLiking, setIsLiking] = useState(false);
  const [liked, setLiked] = useState(isLiked);

  useEffect(() => {
    setLiked(isLiked);
  }, [isLiked]);

  const handleLikeDislike = async () => {
    const handleError = () => {
      setIsLiking(false);
      showSnackbar(liked ? "Failed to unlike" : "Failed to like", {
        type: "error",
      });
    };
    setIsLiking(true);
    try {
      const url = isCollection
        ? "collection/like-dislike"
        : "video/like-dislike";
      const { data: respData } = await axiosInstance.post(url, {
        isLiked: !liked,
        id,
      });
      if (respData.success) {
        setLiked(!liked);
        if (liked) {
          onDislike && onDislike();
        } else {
          onlike && onlike();
        }
      } else {
        handleError();
      }
      setIsLiking(false);
    } catch (error) {
      handleError();
    }
  };

  const computedStyles = {
    ...styles,
    border: "1px solid #E6E6E6",
    bgcolor: "white",
    padding: "4px",
    zIndex: 2,
  };

  return (
    <IconButton
      disabled={isLiking}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
        handleLikeDislike();
      }}
      color="primary"
      aria-label="add to favorites"
      sx={computedStyles}
    >
      {liked ? (
        <FavoriteIcon style={{ color: "red" }} />
      ) : (
        <FavoriteBorderIcon style={{ color: "gray" }} />
      )}
    </IconButton>
  );
};

export default LikeComponent;
