/* eslint-disable react/no-unescaped-entities */
import DropDownComponent from "./DropDownComponent";
import FilterBox from "./FilterBox";
import { useState, useEffect } from "react";
import React from "react";
import {
  Box,
  Button,
  Grid,
  Hidden,
  Typography,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import { useSnackbar } from "@/hooks/useSnackbar";
import ContentCard from "./ContentCard";
import CollectionCard from "./CollectionCard";
import axiosInstance from "@/utils/interceptor";
import { LanguageProficiencyType } from "@/api/mongoTypes";
import { DASHBOARD_SORT_BY, dashboardSortFilters } from "@/constant/dashboard";
import InfiniteScroll from "react-infinite-scroll-component";
import useDebounce from "@/hooks/useDebounce";
import DashboardCardsLoading from "./DashboardCardsLoading";

const dropDownStyles = {
  width: "170px",
  marginTop: { xs: "00px", sm: "0px" },
};

const searchGridContainerStyles = {
  display: "flex",
  flexDirection: { xs: "column", sm: "row" },
  marginTop: { xs: "20px", sm: "50px" },
};

const mobileFilterContainerStyles = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  alignContent: "center",
  justifyContent: "space-evenly",
  marginBottom: "20px",
  flexWrap: "wrap",
};

const gridStyles = { width: "100%" };

const mobileDisplayFilterButtonStyles = {
  padding: "0px",
  border: "1px solid #B3B3B3",
  backgroundColor: "white",
  color: "black",
  borderRadius: "5px",
  fontWeight: "500",
  fontSize: "14px",
  height: "55px",
};

const LIMIT = 12;
const inputLabel = "SORT BY";

type DashboardSearchProps = React.FC<{
  searchTerm: string;
  selectedCategory: string;
  categories: string[];
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
}>;
const DashboardSearch: DashboardSearchProps = ({
  searchTerm,
  setSearchTerm,
  categories,
  selectedCategory,
}) => {
  const { showSnackbar } = useSnackbar();
  const [data, setData] = useState([]);
  const [selectedProficiency, setSelectedProficiency] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedSubtitles, setSelectedSubtitles] = useState([]);
  const [selectedContentType, setSelectedContentType] = useState([]);
  const [sortByFilter, setSortByFilter] = useState(DASHBOARD_SORT_BY.RELEVANT);
  const [isLoading, setIsLoading] = useState(false);
  const [skip, setSkip] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [checkedFilters, setCheckedFilters] = useState(0);
  const [displayMobileFilter, setDisplayMobileFilter] = useState(false);
  const debouncedSearchTerm = useDebounce(searchTerm, 1000);
  const [proficiencies, setProficiencies] = useState<LanguageProficiencyType[]>(
    []
  );
  const [isFethcingProficiencies, setIsFetchingProficiencies] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useEffect(() => {
    const fetchProficiencies = async () => {
      const handleError = () => {
        setIsFetchingProficiencies(false);
        showSnackbar("Failed to fetch proficiencies", { type: "error" });
      };

      try {
        setIsFetchingProficiencies(true);
        const { data: respData } = await axiosInstance.get(
          `utils/get-proficiencies`
        );
        if (respData.success) {
          setProficiencies(respData.data);
        } else {
          handleError();
        }
        setIsFetchingProficiencies(false);
      } catch (error) {
        handleError();
      }
    };
    fetchProficiencies();
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      setSelectedCategories([selectedCategory]);
    }
  }, []);

  const fetchData = async ({
    skip = 0,
    search = "",
    contentType = [],
    subtitles = [],
    contentLevel = [],
    sort = "",
    categories = [],
  }) => {
    const handleError = () => {
      showSnackbar("Failed to fetch data", { type: "error" });
      setIsLoading(false);
    };
    try {
      if (skip === 0) {
        setData([]);
        setIsLoading(true);
      }
      const { data: respData } = await axiosInstance.get("dashboard/all", {
        params: {
          skip,
          limit: LIMIT,
          search,
          contentType: contentType.join(","),
          subtitles: subtitles.join(","),
          contentLevel: contentLevel.join(","),
          categories: categories.join(","),
          sortBy: sort,
        },
      });
      if (respData.success && respData?.data) {
        const length = respData?.data?.length;
        if (skip === 0) {
          setData(respData.data);
        } else {
          setData((prev) => [...prev, ...respData.data]);
        }
        setHasMore(length >= LIMIT);
        setSkip(skip + LIMIT);
      } else {
        handleError();
      }
      setIsLoading(false);
    } catch (error) {
      console.error("Something went wrong in fetchData due to ", error);
      handleError();
    }
  };

  useEffect(() => {
    fetchData({
      skip: 0,
      search: debouncedSearchTerm,
      contentType: selectedContentType,
      subtitles: selectedSubtitles,
      contentLevel: selectedProficiency,
      sort: sortByFilter,
      categories: selectedCategories,
    });
  }, [
    selectedContentType,
    selectedProficiency,
    selectedSubtitles,
    debouncedSearchTerm,
    sortByFilter,
    selectedCategories,
  ]);

  const handleProficiencyChange = (event) => {
    const { value, checked } = event.target;
    // if the event object has been checked, add the value to the selectedProficiency state array.
    // if the event object is not checked (unchecked), iterate through the selectedProficiency state array and remove the value from the liset
    setSelectedProficiency((prev) =>
      checked
        ? [...prev, value]
        : prev.filter((proficiency) => proficiency !== value)
    );
    setCheckedFilters((prev) => (event.target.checked ? prev + 1 : prev - 1));
  };

  const handleSubtitleChange = (event) => {
    const { value, checked } = event.target;
    setSelectedSubtitles((prev) =>
      checked ? [...prev, value] : prev.filter((subtitle) => subtitle !== value)
    );
    setCheckedFilters((prev) => (event.target.checked ? prev + 1 : prev - 1));
  };

  const handleContentTypeChange = (event) => {
    const { value, checked } = event.target;
    //
    setSelectedContentType((prev) =>
      checked ? [...prev, value] : prev.filter((content) => content !== value)
    );
    setCheckedFilters((prev) => (event.target.checked ? prev + 1 : prev - 1));
  };

  const handleCategoryChange = (event) => {
    const { value, checked } = event.target;
    setSelectedCategories((prev) =>
      checked ? [...prev, value] : prev.filter((content) => content !== value)
    );
    setCheckedFilters((prev) => (event.target.checked ? prev + 1 : prev - 1));
  };

  const handleSortByFilterChange = (event) => {
    setSortByFilter(event.target.value);
  };

  function toggleMobileFilterView() {
    setDisplayMobileFilter((prevState) => !prevState);
    setSearchTerm("");
  }

  const allfiltersEmpty =
    selectedProficiency.length === 0 &&
    selectedSubtitles.length === 0 &&
    selectedContentType.length === 0 &&
    searchTerm === "";

  return (
    <Box sx={searchGridContainerStyles}>
      {isMobile ? (
        <>
          <Box sx={mobileFilterContainerStyles}>
            <Button
              variant="outlined"
              size="small"
              sx={mobileDisplayFilterButtonStyles}
              onClick={toggleMobileFilterView}
            >
              Display Filter {+checkedFilters > 0 ? `(${checkedFilters})` : ""}
            </Button>
            <DropDownComponent
              customStyles={dropDownStyles}
              inputLabel={inputLabel}
              choices={dashboardSortFilters}
              handleSortByFilterChange={handleSortByFilterChange}
              filter={sortByFilter}
            />
          </Box>

          {displayMobileFilter && (
            <FilterBox
              categories={categories}
              proficiencies={proficiencies}
              isProficienciesLoading={isFethcingProficiencies}
              handleLevelChange={handleProficiencyChange}
              handleSubtitleChange={handleSubtitleChange}
              handleContentTypeChange={handleContentTypeChange}
              checkedFilters={checkedFilters}
              displayMobileFilter={displayMobileFilter}
              toggleMobileFilterView={toggleMobileFilterView}
              selectedLevels={selectedProficiency}
              selectedSubtitles={selectedSubtitles}
              selectedContentType={selectedContentType}
              handleCategoryChange={handleCategoryChange}
              selectedCategories={selectedCategories}
            />
          )}
        </>
      ) : (
        <FilterBox
          categories={categories}
          proficiencies={proficiencies}
          isProficienciesLoading={isFethcingProficiencies}
          handleLevelChange={handleProficiencyChange}
          handleSubtitleChange={handleSubtitleChange}
          handleContentTypeChange={handleContentTypeChange}
          checkedFilters={checkedFilters}
          selectedLevels={selectedProficiency}
          selectedContentType={selectedContentType}
          selectedSubtitles={selectedSubtitles}
          handleCategoryChange={handleCategoryChange}
          selectedCategories={selectedCategories}
        />
      )}

      {!displayMobileFilter && (
        <>
          <Box sx={{ width: { sm: "100%" } }}>
            <Box
              sx={{
                paddingBottom: "20px",
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: { xs: "column", sm: "row" },
                  alignItems: { xs: "start", sm: "center" },
                }}
              >
                {searchTerm.length > 0 && (
                  <Typography
                    sx={{
                      fontWeight: { xs: "500", sm: "700" },
                      fontSize: { xs: "16px", sm: "24px" },
                    }}
                  >
                    Search Results for "{searchTerm}"
                  </Typography>
                )}

                {allfiltersEmpty ? (
                  <></>
                ) : (
                  <Typography
                    sx={{
                      fontWeight: "400",
                      fontSize: { xs: "10px", sm: "20px" },
                      color: "#6D6D6D",
                      textAlign: "left",
                      marginLeft: { xs: "0px", sm: "15px" },
                    }}
                  >
                    ({data.length} Search Results)
                  </Typography>
                )}
              </Box>
              {!isMobile && (
                <DropDownComponent
                  customStyles={dropDownStyles}
                  inputLabel={inputLabel}
                  choices={dashboardSortFilters}
                  handleSortByFilterChange={handleSortByFilterChange}
                  filter={sortByFilter}
                />
              )}
            </Box>
            <Box>
              {isLoading ? (
                <DashboardCardsLoading />
              ) : data.length === 0 ? (
                <Typography variant="body1" style={{ marginTop: "1rem" }}>
                  No videos match your search or filters.
                </Typography>
              ) : (
                <Box sx={gridStyles}>
                  <Grid container spacing={11}>
                    {!data ? (
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography>
                          Could not find any videos or collections
                        </Typography>
                      </Grid>
                    ) : (
                      <InfiniteScroll
                        dataLength={data.length}
                        next={() =>
                          fetchData({
                            skip,
                            search: debouncedSearchTerm,
                            contentType: selectedContentType,
                            subtitles: selectedSubtitles,
                            contentLevel: selectedProficiency,
                            sort: sortByFilter,
                            categories: selectedCategories,
                          })
                        }
                        hasMore={hasMore}
                        loader={<p style={{ textAlign: "center" }}>Loading</p>}
                        endMessage={
                          <p style={{ textAlign: "center" }}>
                            <b>Yay! You have seen it all</b>
                          </p>
                        }
                      >
                        <Box
                          display="flex"
                          flexDirection="row"
                          alignItems="center"
                          justifyContent="center"
                          flexWrap="wrap"
                          gap={10}
                          mt={10}
                        >
                          {data.map((item, index) => (
                            <Grid
                              item
                              xs={12}
                              sm={6}
                              md={4}
                              key={index}
                              sx={{
                                display: "flex",
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            >
                              {item?.type === "video" && (
                                <ContentCard isAdmin={false} data={item} />
                              )}
                              {item?.type === "collection" && (
                                <CollectionCard
                                  isAdmin={false}
                                  onDelete={() => {}}
                                  data={item}
                                />
                              )}
                            </Grid>
                          ))}
                        </Box>
                      </InfiniteScroll>
                    )}
                  </Grid>
                </Box>
              )}
            </Box>
          </Box>
        </>
      )}
    </Box>
  );
};

export default DashboardSearch;
