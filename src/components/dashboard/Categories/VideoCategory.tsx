import { Typography, Box } from "@mui/material";
import Image from "next/image";
import defaultImage from "@/../../public/images/dashboard/category-icons/business.svg";
import { MUIStyle } from "@/types";

const categoryStylesContainer: MUIStyle = {
  borderRadius: "15px",
  boxShadow: "0px 0px 22px 0px #0000001A",
  float: "none",
  display: "inline-block",
  margin: "0px 10px",
  cursor: "pointer",
};

const CategoryStyles: MUIStyle = {
  display: "flex",
  width: {
    xs: "100px",
    sm: "130px",
    md: "170px",
  },
  height: {
    xs: "100px",
    sm: "130px",
    md: "170px",
  },
  flexDirection: "Column",
  justifyContent: "center",
  alignItems: "center",
  gap: "0px 40px",
};

const iconStyles: MUIStyle = {
  position: "relative",
  height: {
    xs: "30px",
    sm: "50px",
    md: "70px",
  },
  width: {
    xs: "30px",
    sm: "50px",
    md: "70px",
  },
};
const categoryNameStyle: MUIStyle = {
  marginTop: "9px",
  fontSize: {
    xs: "12px",
    sm: "14px",
    md: "16px",
  },
};

export default function VideoCategory({ category, onClick }) {
  return (
    <Box sx={categoryStylesContainer} onClick={onClick}>
      <Box sx={CategoryStyles}>
        <Box sx={iconStyles}>
          <Image
            fill
            src={category?.imgSrc || defaultImage}
            alt={category.name}
          />
        </Box>
        <Typography sx={categoryNameStyle}>{category.name}</Typography>
      </Box>
    </Box>
  );
}
