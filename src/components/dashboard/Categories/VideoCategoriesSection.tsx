import SectionContainer from "../SectionContainer";
import VideoCategory from "./VideoCategory";
import { CategoryType } from "@/api/mongoTypes";
import ScrollerWithButtons from "../ScrollerWithButtons";
import { LikedListType } from "@/types";

type VideoCategoriesSectionProps = React.FC<{
  onClick: (name: string) => void;
  categories: CategoryType[];
  setLikedData: React.Dispatch<React.SetStateAction<LikedListType[]>>;
}>;
const VideoCategoriesSection: VideoCategoriesSectionProps = ({
  onClick,
  categories,
  setLikedData = () => {},
}) => {
  return (
    <SectionContainer title="Video Categories">
      <ScrollerWithButtons>
        {categories?.map((category, index) => (
          <VideoCategory
            key={index}
            onClick={() => {
              onClick(String(category.name));
            }}
            category={category}
          />
        ))}
      </ScrollerWithButtons>
    </SectionContainer>
  );
};

export default VideoCategoriesSection;
