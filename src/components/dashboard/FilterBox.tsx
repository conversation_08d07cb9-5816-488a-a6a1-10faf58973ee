import React from "react";
import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
} from "@mui/material";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  dashboardContentTypeOptions,
  dashboardSubtitlesOptions,
} from "@/constant/dashboard";

const filterBoxContainerStyles = {
  width: { xs: "100%", sm: "268px" },
  marginRight: "48px",
  backgroundcolor: "#fcfcfc",
};

const filterTitleStyles = {
  display: "flex",
  flexDirection: "row",
  gap: "10px",
  height: "30px",
};

const lineStyles = {
  height: "1px",
  border: "0",
  borderTop: "1px solid #ccc",
  borderColor: "#B3B3B3",
};

const accordionContainerStyles = {
  boxShadow: "none", // Remove shadow
  "&:before": { display: "none" }, // Remove default divider line
  backgroundColor: "#fcfcfc",
};

const mobileButtonContainerStyles = {
  display: "flex",
  justifyContent: "center",
  paddingTop: "10px",
};

const mobileButtonStyles = { width: "80%", backgroundColor: "#14A79C" };

export default function FilterBox({
  proficiencies,
  isProficienciesLoading,
  handleLevelChange,
  handleSubtitleChange,
  handleContentTypeChange,
  checkedFilters,
  selectedLevels,
  selectedSubtitles,
  selectedContentType,
  displayMobileFilter = false,
  toggleMobileFilterView = () => {},
  categories,
  handleCategoryChange,
  selectedCategories,
}) {
  return (
    <Box sx={filterBoxContainerStyles}>
      {/* Side Filter Menu */}
      <Box sx={filterTitleStyles}>
        <Typography>Filter By: </Typography>
        <Typography color="#B3B3B3">({checkedFilters} Filters) </Typography>
      </Box>
      <hr style={lineStyles}></hr>
      {/* Content Type */}
      <Accordion defaultExpanded disableGutters sx={accordionContainerStyles}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="content-type-content"
          id="content-type-header"
        >
          <Typography>Content Type</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <FormGroup>
            {dashboardContentTypeOptions.map((m) => (
              <FormControlLabel
                key={m.value}
                control={<Checkbox />}
                label={m.label}
                value={m.value}
                onChange={handleContentTypeChange}
                checked={selectedContentType.includes(m.value)}
              />
            ))}
          </FormGroup>
        </AccordionDetails>
      </Accordion>
      <hr style={lineStyles}></hr>
      {/* Levels */}
      <Accordion defaultExpanded disableGutters sx={accordionContainerStyles}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="levels-content"
          id="levels-header"
        >
          <Typography>Levels</Typography>
        </AccordionSummary>

        {isProficienciesLoading ? (
          <p>loading...</p>
        ) : (
          <AccordionDetails>
            <FormGroup>
              {proficiencies.map((m) => (
                <FormControlLabel
                  key={m._id}
                  control={<Checkbox />}
                  label={m.pfLevel.en}
                  value={m._id}
                  onChange={handleLevelChange}
                  checked={selectedLevels.includes(m._id)}
                />
              ))}
            </FormGroup>
          </AccordionDetails>
        )}
      </Accordion>
      <hr style={lineStyles}></hr>
      <Accordion defaultExpanded disableGutters sx={accordionContainerStyles}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="levels-content"
          id="levels-header"
        >
          <Typography>Categories</Typography>
        </AccordionSummary>

        <AccordionDetails>
          <FormGroup>
            {categories.map((m) => (
              <FormControlLabel
                key={m._id}
                control={<Checkbox />}
                label={m.name}
                value={m.name}
                onChange={handleCategoryChange}
                checked={selectedCategories.includes(m.name)}
              />
            ))}
          </FormGroup>
        </AccordionDetails>
      </Accordion>
      <hr style={lineStyles}></hr>
      {/* Subtitle */}
      <Accordion defaultExpanded disableGutters sx={accordionContainerStyles}>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="subtitle-content"
          id="subtitle-header"
        >
          <Typography>Subtitle</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <FormGroup>
            {dashboardSubtitlesOptions.map((m) => (
              <FormControlLabel
                key={m.value}
                control={<Checkbox />}
                label={m.label}
                value={m.value}
                onChange={handleSubtitleChange}
                checked={selectedSubtitles.includes(m.value)}
              />
            ))}
          </FormGroup>
          {/* <Typography
            variant="body2"
            sx={{ color: "primary.main", cursor: "pointer" }}
          >
            See More
          </Typography> */}
        </AccordionDetails>
      </Accordion>
      <hr style={lineStyles}></hr>
      {displayMobileFilter && (
        <Box sx={mobileButtonContainerStyles}>
          <Button sx={mobileButtonStyles} onClick={toggleMobileFilterView}>
            Apply
          </Button>
        </Box>
      )}
    </Box>
  );
}
