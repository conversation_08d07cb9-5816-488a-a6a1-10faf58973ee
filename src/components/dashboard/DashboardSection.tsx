import VideoCategoriesSection from "./Categories/VideoCategoriesSection";
import IndividualCollectionsSection from "./IndividualCollectionsSection";
import FeaturedSection from "./FeaturedSection";
import LikedSection from "./LikedSection";
import { useState, useEffect } from "react";
import React from "react";
import { Box } from "@mui/material";
import DashboardSearch from "./DashboardSearch";
import SearchInput from "../SearchInput";
import MyLearning from "./MyLearning";
import { VideosCollectionType, VideoType } from "@/api/mongoTypes";
import { LikedListType, ProgressType } from "@/types";

const searchBarContainerStyles = {
  display: "flex",
  flexDirection: { xs: "column", sm: "row" },
  alignContent: "center",
  marginTop: "30px",
  gap: 5,
};

export default function DashboardSection({
  learning,
  categories,
  featured,
  collections,
  liked,
}) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSeletedCategory] = useState("");
  const [displaySearchGrid, setDisplaySearchGrid] = useState(true);
  const [likedData, setLikedData] = useState<LikedListType[]>(liked);
  const [learningData, setLearningData] = useState<ProgressType[]>(learning);
  const [featuredData, setFeaturedData] = useState<VideoType[]>(featured);
  const [collectionData, setCollectionData] =
    useState<VideosCollectionType[]>(collections);

  useEffect(() => {
    if (searchTerm.length > 0) {
      setDisplaySearchGrid(false);
    }
  }, [searchTerm]);

  return (
    <>
      <Box sx={searchBarContainerStyles}>
        <SearchInput
          disabled={false}
          placeholder="Find the perfect course or instructor"
          searchValue={searchTerm}
          handleClear={() => setSearchTerm("")}
          setSearchValue={setSearchTerm}
          isLoading={false}
          handleSearch={() => {}}
        />

        {!displaySearchGrid && (
          <Box
            onClick={() => {
              setDisplaySearchGrid(true);
              setSearchTerm("");
            }}
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                color: "#14a79c",
                borderBottom: "1px solid #14a79c",
                maxHeight: "fit-content",
                cursor: "pointer",
              }}
            >
              Go back to dashboard
            </Box>
          </Box>
        )}
      </Box>
      {!displaySearchGrid ? (
        <DashboardSearch
          categories={categories}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          selectedCategory={selectedCategory}
        />
      ) : (
        <>
          <MyLearning
            data={learningData}
            setLikedData={setLikedData}
            setFeaturedData={setFeaturedData}
            setCollectionData={setCollectionData}
          />
          <VideoCategoriesSection
            categories={categories}
            setLikedData={setLikedData}
            onClick={(name: string) => {
              setSeletedCategory(name);
              setDisplaySearchGrid(false);
            }}
          />
          <FeaturedSection
            data={featuredData}
            setLikedData={setLikedData}
            setLearningData={setLearningData}
          />
          <IndividualCollectionsSection
            data={collectionData}
            setLikedData={setLikedData}
            setLearningData={setLearningData}
          />
          <LikedSection
            data={likedData}
            setLikedData={setLikedData}
            setFeaturedData={setFeaturedData}
            setCollectionData={setCollectionData}
            setLearningData={setLearningData}
          />
        </>
      )}
    </>
  );
}
