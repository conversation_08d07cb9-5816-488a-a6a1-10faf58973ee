import { Box, Button, Typography, keyframes } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";
import React from "react";

const rotate = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

type PaymentLayoutProps = React.FC<{
  children: React.ReactNode;
  isError?: Boolean;
}>;
const PaymentLayout: PaymentLayoutProps = ({ children, isError }) => {
  const router = useRouter();

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="100%"
      width="100%"
      sx={{
        background: isError ? "red" : "#2a812a",
        flex: 1,
      }}
    >
      <Box
        sx={{
          animation: `${rotate} 5s linear infinite`,
        }}
        mb={10}
      >
        <Image alt="Logo" src="/patitoB.png" height={100} width={100} />
      </Box>

      <Typography variant="body1" mt={2} color="#fff" fontSize="2.2rem">
        {isError ? "Payment Failed" : "Payment successful"}
      </Typography>
      <Typography
        textAlign="center"
        variant="body1"
        mt={2}
        mb={5}
        color="#fff"
        fontSize="1.1rem"
        width="80%"
      >
        {isError
          ? "Unfortunately, your payment could not be processed. Please check your payment details and try again. If the issue persists, contact us for assistance."
          : "Thank you for your payment. Your transaction has been completed successfully. If you have any questions or need assistance, feel free to reach out to us."}
      </Typography>

      <Button
        onClick={() => {
          const link = isError ? "/cart" : "/classes";
          router.push(link);
        }}
      >
        View {isError ? "Cart" : "Classes"}
      </Button>
      <>{children}</>
    </Box>
  );
};

export default PaymentLayout;
