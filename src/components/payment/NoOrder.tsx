import { Box, Button, Typography, keyframes } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/router";
import React from "react";

const rotate = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const NoOrder = () => {
  const router = useRouter();
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="100%"
      width="100%"
      sx={{
        background: "#2a812a",
        flex: 1,
      }}
    >
      <Box
        sx={{
          animation: `${rotate} 5s linear infinite`,
        }}
        mb={10}
      >
        <Image alt="Logo" src="/patitoB.png" height={100} width={100} />
      </Box>

      <Typography variant="body1" mt={2} color="#fff" fontSize="2.2rem">
        No such payment Exists
      </Typography>
      <Typography
        textAlign="center"
        variant="body1"
        mt={2}
        mb={5}
        color="#fff"
        fontSize="1.1rem"
        width="80%"
      ></Typography>

      <Button
        onClick={() => {
          const link = "/classes";
          router.push(link);
        }}
      >
        View Classes
      </Button>
    </Box>
  );
};

export default NoOrder;
