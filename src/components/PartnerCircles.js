import React from "react";
import { useSprings, animated } from "react-spring";
import { Box } from "@mui/material";
import useIsDesktop from "../hooks/useIsDesktop";
import Image from "next/image";

const Circle = ({ img, springProps }) => {
  const isDesktop = useIsDesktop();
  const size = isDesktop ? 200 : 140;

  return (
    <animated.div style={springProps}>
      <Box
        sx={{
          width: size,
          height: size,
          margin: "5px",
          borderRadius: "50%",
          border: "1px solid black",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          overflow: "hidden",
          position: "relative",
        }}
      >
        <Image
          src={img}
          alt="partner"
          width={400}
          height={400}
          className="w-full h-full object-cover object-center absolute top-0 left-0 right-0 bottom-0"
        />
      </Box>
    </animated.div>
  );
};

export default function PartnerCircles({ images, originDirection }) {
  const imagesTwice = [...images, ...images, ...images];
  const [fromX, toX] =
    originDirection === "left" ? ["0%", "400%"] : ["0%", "-400%"];

  const springs = useSprings(
    imagesTwice.length,
    imagesTwice.map(() => ({
      from: { transform: `translateX(${fromX})` },
      to: { transform: `translateX(${toX})` },
      config: { duration: 12000 },
      loop: true,
      reset: true,
    }))
  );

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      overflow="hidden"
      sx={{ margin: "20px 0" }}
    >
      {springs.map((springProps, index) => (
        <Circle
          key={index}
          img={imagesTwice[index]}
          springProps={springProps}
        />
      ))}
    </Box>
  );
}

// import React from "react";
// import { useSprings, animated } from "react-spring";
// import { Box } from "@mui/material";

// const Circle = ({ img, springProps }) => {
//   return (
//     <animated.div style={springProps}>
//       <Box
//         sx={{
//           width: 200,
//           height: 200,
//           borderRadius: "50%",
//           border: "1px solid black",
//           display: "flex",
//           justifyContent: "center",
//           alignItems: "center",
//           overflow: "hidden",
//           position: "relative",
//         }}
//       >
//         <img
//           src={img}
//           alt={img}
//           style={{
//             width: "100%",
//             height: "auto",
//             position: "absolute",
//             top: 0,
//             left: 0,
//             right: 0,
//             bottom: 0,
//             objectFit: "cover",
//           }}
//         />
//       </Box>
//     </animated.div>
//   );
// };

// export default function PartnerCircles({ images, originDirection }) {
//   const imagesTwice = [images, ...images];
//   const [fromX, toX] =
//     originDirection === "left" ? ["0%", "100%"] : ["0%", "-100%"];

//   const springs = useSprings(
//     imagesTwice.length,
//     imagesTwice.map((_, index) => ({
//       from: { transform: `translateX(${fromX})` },
//       to: { transform: `translateX(${toX})` },
//       config: { duration: 3000 },
//       loop: true,
//       reset: true,
//       delay: 500, // Delay each circle's animation by a constant value
//     }))
//   );

//   return (
//     <Box
//       display="flex"
//       justifyContent="center"
//       alignItems="center"
//       overflow="hidden"
//       sx={{ margin: "20px 0" }}
//     >
//       {springs.map((springProps, index) => (
//         <Circle
//           key={index}
//           img={imagesTwice[index]}
//           springProps={springProps}
//         />
//       ))}
//     </Box>
//   );
// }

// import React from "react";
// import { useSprings, animated } from "react-spring";
// import { Box } from "@mui/material";

// const Circle = ({ img, index, springProps }) => {
//   return (
//     <animated.div style={springProps}>
//       <Box
//         sx={{
//           width: 200,
//           height: 200,
//           borderRadius: "50%",
//           border: "1px solid black",
//           display: "flex",
//           justifyContent: "center",
//           alignItems: "center",
//           overflow: "hidden",
//           // marginRight: 2,
//           position: "relative",
//         }}
//       >
//         <img
//           src={img}
//           alt={`circle-${index}`}
//           style={{
//             width: "100%",
//             height: "auto",
//             position: "absolute",
//             top: 0,
//             left: 0,
//             right: 0,
//             bottom: 0,
//             objectFit: "cover",
//           }}
//         />
//       </Box>
//     </animated.div>
//   );
// };

// export default function PartnerCircles({ images, originDirection }) {
//   const [fromX, toX] =
//     originDirection == "left"
//       ? ["translateX(0%)", "translateX(-100%)"]
//       : ["translateX(-100%)", "translateX(0%)"];

//   const imagesTwice = [images, ...images];

//   const springs = useSprings(
//     imagesTwice.length,
//     imagesTwice.map((_, index) => ({
//       from: { transform: fromX },
//       to: async (next) => {
//         while (1) {
//           await next({
//             transform: toX,
//             config: { duration: 3000 },
//           });
//           await next({ transform: fromX, immediate: true });
//           // await next({ transform: fromX, immediate: true });
//           // await next({ transform: fromX, immediate: true });
//         }
//       },
//       config: { duration: 2000 },
//       // delay: index * 200,
//       // loop: true,
//       // reset: true,
//     }))
//   );

//   // const springs = useSprings(
//   //   imagesTwice.length,
//   //   imagesTwice.map((_, index) => ({
//   //     from: { transform: fromX },
//   //     to: { transform: toX },
//   //     config: { duration: 3000 },
//   //     // delay: index * 200,
//   //     reset: true,
//   //     reverse: true,
//   //     loop: true,
//   //   }))
//   // );

//   return (
//     <Box
//       display="flex"
//       justifyContent="center"
//       alignItems="center"
//       overflow="hidden"
//       sx={{ margin: "20px 0" }}
//     >
//       {imagesTwice.map((img, index) => (
//         <Circle
//           key={index}
//           img={img}
//           index={index}
//           springProps={springs[index]}
//         />
//       ))}
//     </Box>
//   );
// }
