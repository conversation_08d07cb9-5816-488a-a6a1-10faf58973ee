import { ClassesPricingType } from "@/api/mongoTypes";
import PricingType from "@/components/classes/PricingType";
import { getPricingTitle, getSubTypeName, getTypeName } from "@/utils/format";
import { Box, Typography } from "@mui/material";
import React from "react";
import Close from "../Close";
import QuantitySelector from "../QuantitySelector";
import useTranslate from "@/hooks/useTranslate";

type HeaderProps = React.FC<{
  handleClose: () => void;
  count: number;
  onClickAdd: () => void;
  onClickSub: () => void;
  pricingData: ClassesPricingType;
}>;
const Header: HeaderProps = ({
  handleClose,
  count,
  onClickAdd,
  onClickSub,
  pricingData,
}) => {
  const title = getPricingTitle({
    data: pricingData,
  });
  const { translate } = useTranslate();

  return (
    <Box
      sx={{
        borderBottom: "1px solid rgba(245, 245, 245, 1)",
        pb: "1rem",
        mb: "1rem",
      }}
    >
      <Box
        width="100%"
        display="flex"
        flexDirection="row"
        justifyContent="space-between"
        mb={5}
      >
        <Box>
          <Typography sx={{ mb: 0, fontWeight: "bolder", fontSize: 20 }}>
            {title}
          </Typography>
          <Box display="flex" flexDirection="row" gap={5}>
            <PricingType text={getTypeName(pricingData.type)} />
            <PricingType text={getSubTypeName(pricingData.subType)} />
          </Box>
        </Box>
        <Box sx={{ cursor: "pointer" }}>
          <Close onClick={handleClose} />
        </Box>
      </Box>

      <Typography
        sx={{
          fontWeight: "700",
          mb: 1,
          fontSize: 14,
        }}
      >
        {translate("clubSubs.select-quantity")}
      </Typography>
      <QuantitySelector
        count={count}
        onClickAdd={onClickAdd}
        onClickSub={onClickSub}
      />
    </Box>
  );
};

export default Header;
