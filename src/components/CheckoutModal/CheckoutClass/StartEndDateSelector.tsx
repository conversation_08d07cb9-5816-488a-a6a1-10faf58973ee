import React, { useState, useMemo, useEffect } from "react";
import { MenuItem, Select, Box } from "@mui/material";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { isSameDay } from "date-fns";
import { getWeekOptions } from "@/utils/dateTime";

const StartEndDateSelector = ({ startDate, endDate, onSelect }) => {
  const [selectedWeekIndex, setSelectedWeekIndex] = useState(0);

  const weekOptions = useMemo(() => {
    return getWeekOptions({
      showStartEndDate: false,
    });
  }, []);

  useEffect(() => {
    if (!startDate) {
      const first = weekOptions[0];
      setSelectedWeekIndex(0);
      onSelect(first);
      return;
    }

    const index = weekOptions.findIndex((week) =>
      isSameDay(week.startDate, startDate)
    );

    if (index !== -1) {
      setSelectedWeekIndex(index);
    }
  }, [startDate, weekOptions, onSelect]);

  const handleWeekChange = (event) => {
    const newIndex = event.target.value;
    const selected = weekOptions[newIndex];
    setSelectedWeekIndex(newIndex);
    onSelect(selected);
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "flex-end",
        border: "1px solid #CCCCCC",
        borderRadius: 2,
        overflow: "hidden",
        height: 45,
        mb: 2,
        width: "100%",
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        height="100%"
        width={50}
        sx={{ background: "#F5F5F5" }}
      >
        <CalendarMonthIcon />
      </Box>
      <Select
        value={selectedWeekIndex}
        onChange={handleWeekChange}
        sx={{
          m: 0,
          fontSize: 14,
          height: 45,
          width: "100%",
          border: "none",
          outline: "none",
          "& .MuiSelect-select": {
            height: 45,
            padding: 0,
            lineHeight: "45px",
            px: 2,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            border: "none",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            border: "none",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            border: "none",
            outline: "none",
          },
        }}
      >
        {weekOptions.map((week) => (
          <MenuItem key={week.value} value={week.value}>
            {week.label}
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
};

export default StartEndDateSelector;
