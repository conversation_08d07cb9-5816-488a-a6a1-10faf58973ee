import React from "react";

const Close = (props) => {
  return (
    <svg
      {...props}
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        cursor: "pointer",
      }}
    >
      <path
        d="M6.8 16.026L11 11.826L15.2 16.026L16.026 15.2L11.826 11L16.026 6.8L15.2 5.974L11 10.174L6.8 5.974L5.974 6.8L10.174 11L5.974 15.2L6.8 16.026ZM11.0035 21.5C9.55217 21.5 8.18717 21.2247 6.9085 20.674C5.63061 20.1226 4.51878 19.3743 3.573 18.4293C2.62722 17.4843 1.87861 16.3737 1.32717 15.0973C0.775722 13.821 0.5 12.4564 0.5 11.0035C0.5 9.55061 0.775722 8.18561 1.32717 6.9085C1.87783 5.63061 2.62489 4.51878 3.56833 3.573C4.51178 2.62722 5.62283 1.87861 6.9015 1.32717C8.18017 0.775722 9.54517 0.5 10.9965 0.5C12.4478 0.5 13.8128 0.775722 15.0915 1.32717C16.3694 1.87783 17.4812 2.62528 18.427 3.5695C19.3728 4.51372 20.1214 5.62478 20.6728 6.90267C21.2243 8.18056 21.5 9.54517 21.5 10.9965C21.5 12.4478 21.2247 13.8128 20.674 15.0915C20.1233 16.3702 19.3751 17.482 18.4293 18.427C17.4836 19.372 16.3729 20.1206 15.0973 20.6728C13.8218 21.2251 12.4572 21.5008 11.0035 21.5ZM11 20.3333C13.6056 20.3333 15.8125 19.4292 17.6208 17.6208C19.4292 15.8125 20.3333 13.6056 20.3333 11C20.3333 8.39444 19.4292 6.1875 17.6208 4.37917C15.8125 2.57083 13.6056 1.66667 11 1.66667C8.39444 1.66667 6.1875 2.57083 4.37917 4.37917C2.57083 6.1875 1.66667 8.39444 1.66667 11C1.66667 13.6056 2.57083 15.8125 4.37917 17.6208C6.1875 19.4292 8.39444 20.3333 11 20.3333Z"
        fill="black"
      />
    </svg>
  );
};

export default Close;
