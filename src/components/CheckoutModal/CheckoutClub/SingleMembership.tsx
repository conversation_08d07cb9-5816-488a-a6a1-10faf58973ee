import { Box, TextField, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import SectionLabel from "../SectionLabel";
import PlanForCard from "../PlanForCard";
import { validateEmail } from "@/utils/validate";
import { PLAN_FOR } from "@/constant/Enums";
import useTranslate from "@/hooks/useTranslate";

const SingleMembership = ({ index, memberships, setMemberships, data }) => {
  const { translate } = useTranslate();
  const [isMySelf, setIsMySelf] = useState(
    data ? data?.planFor === PLAN_FOR.MYSELF : true
  );
  // const [isMySelf, setIsMySelf] = useState(true);
  const [recipent, setRecipent] = useState(data ? data?.emailId : "");

  useEffect(() => {
    setMemberships((prev) => {
      const newList = prev.map((m, i) => {
        if (i === index) {
          return {
            planFor: isMySelf ? PLAN_FOR.MYSELF : PLAN_FOR.SOMEONE,
            emailId: recipent,
          };
        }
        return m;
      });
      return newList;
    });
  }, [isMySelf, recipent, index]);

  return (
    <Box
      sx={{
        borderBottom: "1px solid silver",
        py: 4,
      }}
    >
      <Typography
        sx={{
          fontWeight: "700",
          mb: 2,
          fontSize: 16,
        }}
      >
        {translate("clubSubs.membership")} {index + 1}
      </Typography>

      <SectionLabel text={translate("clubSubs.membership-who")} />

      <Box mb={4}>
        <PlanForCard
          isActive={isMySelf}
          title={translate("common.myself")}
          description={translate("common.myself-desc")}
          onClick={() => {
            setIsMySelf(true);
          }}
        />
        <PlanForCard
          title={translate("common.someone-else")}
          description={translate("common.someone-desc")}
          isActive={!isMySelf}
          onClick={() => {
            setIsMySelf(false);
          }}
        />
      </Box>

      {!isMySelf && (
        <TextField
          label={
            <>
              {translate("clubSubs.email-recep")}
              &nbsp;<span style={{ color: "red" }}>*</span>
            </>
          }
          variant="outlined"
          value={recipent}
          error={validateEmail(recipent) === false}
          onChange={(e) => {
            const recipentValue = e.target.value;
            setRecipent(recipentValue);
          }}
          fullWidth
        />
      )}
    </Box>
  );
};

export default SingleMembership;
