import { Box, Modal, Typography } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { PLAN_FOR } from "@/constant/Enums";
import { useSnackbar } from "@/hooks/useSnackbar";
import usePayment from "@/hooks/usePayment";
import Header from "./Header";
import CheckoutModalFooter from "../CheckoutModalFooter";
import QuantitySelector from "../QuantitySelector";
import SingleMembership from "./SingleMembership";
import useCart from "@/hooks/useCart";
import { CartType, ClubType, MembershipType } from "@/api/mongoTypes";
import { useRouter } from "next/router";
import { useUserContext } from "@/contexts/UserContext";
import { validateEmail } from "@/utils/validate";
import useTranslate from "@/hooks/useTranslate";

const INITIAL_STATE = {
  planId: "1",
  planFor: PLAN_FOR.MYSELF,
  emailId: "",
};

type CheckoutClubsModalProps = React.FC<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  data: ClubType;
  isUpdate?: CartType;
  onUpdateCartSuccess?: (data: CartType) => void;
  redirectToSignup?: () => void;
}>;
const CheckoutClubsModal: CheckoutClubsModalProps = ({
  open,
  setOpen,
  data,
  isUpdate,
  onUpdateCartSuccess,
  redirectToSignup = () => {},
}) => {
  const router = useRouter();
  const [memberships, setMemberships] = useState([INITIAL_STATE]);
  const handleClose = () => setOpen(false);
  const [buttonDisabled, setButtonDisabled] = useState(false);
  const { showSnackbar } = useSnackbar();
  const { isMaking, isSubscribing, subscribeClub } = usePayment();
  const [isLoading, setIsLoading] = useState(false);
  const { dbUser } = useUserContext();
  const { translate } = useTranslate();

  const isLoggedIn = useMemo(() => {
    return !!dbUser;
  }, [dbUser]);

  const total = useMemo(() => {
    const length = memberships.length;
    return length * +data.price;
  }, [memberships]);

  const { isAddingToCart, isUpdatingCart } = useCart({
    onDeleteSuccess: () => {},
    onAddingSuccess: () => {
      handleClose();
    },
  });

  const onClickAdd = () => {
    setMemberships((prev) => [...prev, INITIAL_STATE]);
  };
  const onClickSub = () => {
    setMemberships((prev) => prev.slice(0, prev.length - 1));
  };

  useEffect(() => {
    const validateMembership = () => {
      if (memberships.length > 0) {
        let emailValid = true;
        let plansValid = true;

        const emails = memberships.filter(
          (f) => f.planFor === PLAN_FOR.SOMEONE
        );
        if (emails.length > 0) {
          const isDbEmail = emails.find((s) => s.emailId === dbUser?.email);
          if (isDbEmail) {
            showSnackbar(translate("clubSubs.email-cannot"), {
              type: "error",
            });
            emailValid = false;
          }
          if (emailValid) {
            const everyEmailGood = emails.every((f) =>
              validateEmail(f.emailId)
            );
            emailValid = everyEmailGood;
          }
        }

        const isMembershipValid = (memberships: MembershipType) => {
          if (memberships.length <= 1) {
            return true;
          }
          const getMembershipString = (m) => {
            const filteredEmail =
              m.planFor === PLAN_FOR.SOMEONE ? m.emailId : "";
            return `${m.planFor}_${filteredEmail}`;
          };

          const isDuplicatePlanExists = memberships.some((f) => {
            const dup = memberships.filter((j) => {
              return getMembershipString(j) === getMembershipString(f);
            });
            if (dup.length > 1) {
              return true;
            }
            return false;
          });

          return !isDuplicatePlanExists;
        };
        plansValid = isMembershipValid(memberships);
        const condition = !plansValid || !emailValid;
        setButtonDisabled(condition);
      }
    };

    validateMembership();
  }, [memberships]);

  useEffect(() => {
    if (isUpdate) {
      setMemberships(
        isUpdate.memberships.map((m, i) => ({
          emailId: m.emailId,
          planFor: m.planFor,
          planId: String(i),
        }))
      );
    }
  }, [isUpdate]);

  const handleSubscription = async () => {
    if (isLoggedIn) {
      subscribeClub({
        clubId: data._id,
        memberships: memberships.map((m) => {
          return {
            emailId: m.emailId,
            planFor: m.planFor,
          };
        }),
      });
    } else {
      router.push("/sign-up");
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: {
            xs: "100%",
            sm: "100%",
            md: 900,
          },
          bgcolor: "background.paper",
          boxShadow: 24,
          borderRadius: 2,
        }}
      >
        <Box
          sx={{
            p: 6,
            width: "100%",
            maxHeight: "90vh",
            position: "relative",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Header handleClose={handleClose} data={data} />
          <Typography
            sx={{
              fontWeight: "700",
              mb: 1,
              fontSize: 14,
            }}
          >
            {translate("clubSubs.select-quantity")}
          </Typography>
          <QuantitySelector
            count={memberships.length}
            onClickAdd={onClickAdd}
            onClickSub={onClickSub}
          />
          <Box
            sx={{
              overflow: "auto",
              flex: 1,
            }}
          >
            <Box>
              {memberships.map((m, i) => (
                <SingleMembership
                  index={i}
                  key={i}
                  data={m}
                  memberships={memberships}
                  setMemberships={setMemberships}
                />
              ))}
            </Box>
          </Box>
          <CheckoutModalFooter
            isUpdate={!!isUpdate}
            buttonDisabled={
              buttonDisabled ||
              isAddingToCart ||
              isUpdatingCart ||
              isSubscribing
            }
            isLoading={isLoading}
            isMaking={isMaking || isSubscribing}
            handleBuyNow={() => {}}
            handleAddTocart={() => {}}
            total={total}
            currency={data.currency}
            showSubscribe
            handleSubscription={() => {
              if (isLoggedIn) {
                handleSubscription();
              } else {
                localStorage.setItem("BUY_CLUB", JSON.stringify(data));
                redirectToSignup();
              }
            }}
          />
        </Box>
      </Box>
    </Modal>
  );
};

export default CheckoutClubsModal;
