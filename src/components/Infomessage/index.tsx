import { Typography, TypographyProps } from "@mui/material";
import React from "react";

type InfoMessageProps = React.FC<
  {
    message: String;
    size?: number;
  } & TypographyProps
>;

const InfoMessage: InfoMessageProps = ({
  message,
  size = 14,
  sx,
  ...props
}) => {
  return (
    <Typography
      {...props}
      color="rgba(120, 120, 120, 1)"
      sx={{
        ...sx,
        fontSize: size,
        textAlign: "center",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
      }}
    >
      <InfoIcon size={size} />
      <span style={{ marginLeft: 5 }}>{message}</span>
    </Typography>
  );
};

const InfoIcon = ({ size }) => {
  return (
    <svg
      height={size + 2}
      viewBox="0 0 12 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 5.28125C6.10774 5.28125 6.21108 5.32405 6.28726 5.40024C6.36345 5.47642 6.40625 5.57976 6.40625 5.6875V9.34375C6.40625 9.45149 6.36345 9.55483 6.28726 9.63101C6.21108 9.7072 6.10774 9.75 6 9.75C5.89226 9.75 5.78892 9.7072 5.71274 9.63101C5.63655 9.55483 5.59375 9.45149 5.59375 9.34375V5.6875C5.59375 5.57976 5.63655 5.47642 5.71274 5.40024C5.78892 5.32405 5.89226 5.28125 6 5.28125ZM6 4.46875C6.16162 4.46875 6.31661 4.40455 6.43089 4.29027C6.54517 4.17599 6.60938 4.02099 6.60938 3.85938C6.60938 3.69776 6.54517 3.54276 6.43089 3.42848C6.31661 3.3142 6.16162 3.25 6 3.25C5.83838 3.25 5.68339 3.3142 5.56911 3.42848C5.45483 3.54276 5.39062 3.69776 5.39062 3.85938C5.39062 4.02099 5.45483 4.17599 5.56911 4.29027C5.68339 4.40455 5.83838 4.46875 6 4.46875ZM0.3125 6.5C0.3125 3.35888 2.85888 0.8125 6 0.8125C9.14112 0.8125 11.6875 3.35888 11.6875 6.5C11.6875 9.64112 9.14112 12.1875 6 12.1875C2.85888 12.1875 0.3125 9.64112 0.3125 6.5ZM6 1.625C3.30778 1.625 1.125 3.80778 1.125 6.5C1.125 9.19222 3.30778 11.375 6 11.375C8.69222 11.375 10.875 9.19222 10.875 6.5C10.875 3.80778 8.69222 1.625 6 1.625Z"
        fill="#787878"
      />
    </svg>
  );
};

export default InfoMessage;
