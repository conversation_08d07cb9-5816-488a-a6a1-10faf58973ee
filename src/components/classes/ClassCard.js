import Link from "next/link";
import Image from "next/image";
import { Card, CardContent, Typography, Button, Box } from "@mui/material";

export default function ClassCard({
  title,
  description,
  image,
  color,
  url,
  buttonText,
}) {
  return (
    <Card
      sx={{
        textAlign: "center",
        borderRadius: "10px",
        width: "100%",
        backgroundColor: color,
        maxWidth: { xs: "100%", sm: 300 },
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: "200px",
          position: "relative",
          margin: "20px 20px 10px",
        }}
      >
        <Image
          src={image}
          fill
          alt="Card image"
          style={{
            objectFit: "cover",
            objectPosition: "center",
            borderRadius: "10px",
          }}
        />
      </Box>

      <CardContent
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Typography
          sx={{
            fontSize: { xs: "20px" },
            fontWeight: { xs: "600" },
            marginBottom: "20px",
          }}
        >
          {title}
        </Typography>

        <Box
          sx={{
            mt: "auto",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <Typography variant="body2" color="text.primary">
            {description}
          </Typography>
          <Link href={url}>
            <Button
              variant="contained"
              sx={{
                marginTop: "16px",
                backgroundColor: "#F5F5F5",
                color: "#000000",
                "&:hover": {
                  color: "#FFFFFF",
                },
              }}
            >
              {buttonText}
            </Button>
          </Link>
        </Box>
      </CardContent>
    </Card>
  );
}
