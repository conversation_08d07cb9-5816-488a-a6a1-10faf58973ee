import useWindowDimensions from "@/hooks/useWindowDimension";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import React from "react";

const commonTextStyle = {
  fontSize: { xs: "40px", sm: "55px" },
  fontWeight: { xs: "500", sm: "600" },
  textAlign: "center",
  zIndex: 10,
  textShadow:
    "2px 2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, -2px -2px 0 #000, 2px 0px 0 #000, 0px 2px 0 #000, -2px 0px 0 #000, 0px -2px 0 #000",
};

type ClassBannerProps = React.FC<{
  text: string;
}>;
const ClassBanner: ClassBannerProps = ({ text }) => {
  const { width } = useWindowDimensions();
  const imgSrc = width < 700 ? "phone" : "laptop";

  return (
    <Box
      sx={{
        minHeight: { xs: "250px", sm: "300px" },
        position: "relative",
        width: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <Box
        sx={{
          position: "relative",
          width: "fit-content",
        }}
      >
        <Typography
          sx={{
            ...commonTextStyle,
            color: "#038b6a",
            position: "relative",
          }}
        >
          {text}
        </Typography>
        <Typography
          sx={{
            ...commonTextStyle,
            top: -5,
            left: -5,
            color: "#FFF",
            position: "absolute",
          }}
        >
          {text}
        </Typography>
      </Box>
      <Image
        src={`/images/classes/banner_${imgSrc}.webp`}
        alt="Responsive image"
        priority
        fill
      />
    </Box>
  );
};

export default ClassBanner;
