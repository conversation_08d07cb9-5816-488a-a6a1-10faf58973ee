import React, { useMemo } from "react";
import { Avatar, AvatarGroup, Box, Card, Typography } from "@mui/material";
import { UserType } from "@/api/mongoTypes";

type CreatorDetailsProps = React.FC<{
  creatorDetails: UserType[];
  isSmall?: boolean;
  mt?: number;
}>;

const CreatorDetails: CreatorDetailsProps = ({
  creatorDetails,
  isSmall = false,
  mt = 5,
}) => {
  const { size, textFontSize, avatarFontSize } = useMemo(() => {
    if (isSmall) {
      return {
        size: 15,
        textFontSize: 10,
        avatarFontSize: 12,
      };
    }
    return {
      size: 20,
      textFontSize: 12,
      avatarFontSize: 15,
    };
  }, [isSmall]);

  if (!creatorDetails) {
    return null;
  }

  const names = creatorDetails
    .map((m) => `${m.firstName} ${m?.lastName ?? ""}`)
    .join(",");

  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      sx={{ overflow: "hidden" }}
      mt={mt}
    >
      <AvatarGroup
        max={4}
        sx={{
          "& .MuiAvatar-root": {
            width: size,
            height: size,
            fontSize: textFontSize,
          },
        }}
      >
        {creatorDetails.map((m) => (
          <UserIcon
            key={`user-icon-${m._id}`}
            src={m.profileImageUrl}
            name={m.firstName}
          />
        ))}
      </AvatarGroup>
      <Typography
        textOverflow="ellipsis"
        sx={{
          fontSize: avatarFontSize,
          overflow: "hidden",
          color: "rgba(109, 109, 109, 1)",
          whiteSpace: "nowrap",
          fontWeight: 500,
        }}
      >
        {names}
      </Typography>
    </Box>
  );
};

const UserIcon = ({ src = "", name = "" }) => {
  return <Avatar sx={{ width: 20, height: 20 }} alt={name} src={src} />;
};

export default CreatorDetails;
