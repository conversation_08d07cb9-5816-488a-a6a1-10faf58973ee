import { ClubType } from "@/api/mongoTypes";
import { useUserContext } from "@/contexts/UserContext";
import useDebounce from "@/hooks/useDebounce";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { Box, Container } from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import OnlineClubCard from "../OnlineClubCard";
import ExperiencesLoading from "../ExperiencesLoading";
import InfiniteScroll from "react-infinite-scroll-component";
import { getTargetLanguageFromCookieOrUser } from "@/utils/classes";
import useTranslate from "@/hooks/useTranslate";

const LIMIT = 12;

const ClubList = () => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState<ClubType[]>([]);
  const [search, setSearch] = useState("");
  const { dbUser, isfetching, isRetrying } = useUserContext();
  const { translate } = useTranslate();

  const debouncedSearchTerm = useDebounce(search, 1000);

  const fetchData = async ({ skipCount = 0, search = "" }) => {
    const showLoading = +skipCount === 0;
    if (skipCount === 0) {
      setData([]);
    }
    try {
      if (showLoading) {
        setIsLoading(true);
      }
      const language = getTargetLanguageFromCookieOrUser({ dbUser });
      const params = {
        needClubImage: true,
        skip: skipCount,
        limit: LIMIT,
        search,
        targetLanguage: language,
      };
      const { data } = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/club/all`,
        {
          params,
        }
      );
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        showSnackbar("Failed to fetch Events", {
          type: "error",
        });
      }
      if (showLoading) {
        setIsLoading(false);
      }
    } catch (error) {
      console.error(`Something went wrong in fetchData due to ${error}`);
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    if (!isfetching && !isRetrying) {
      fetchData({ skipCount: 0, search: debouncedSearchTerm });
    }
  }, [debouncedSearchTerm, isfetching, isRetrying]);

  const redirectToSignup = () => {
    router.push("/sign-up");
    showSnackbar(translate("common.create-new-acc"), {
      type: "warning",
    });
  };

  return (
    <>
      <Container
        sx={{
          display: "flex",
          flexDirection: "row",
          flexWrap: "wrap",
          alignItems: "center",
        }}
      >
        {isLoading ? (
          <ExperiencesLoading />
        ) : (
          <InfiniteScroll
            style={{
              width: "100%",
            }}
            dataLength={data.length}
            next={() => fetchData({ skipCount: skip, search: search })}
            hasMore={hasMore}
            loader={<p style={{ textAlign: "center" }}>Loading</p>}
            endMessage={
              <p style={{ textAlign: "center" }}>
                {search.length > 0 ? (
                  <p>
                    No club with title <b>{search}</b>
                  </p>
                ) : (
                  <b>{translate("common.seen-all")}</b>
                )}
              </p>
            }
          >
            <Box
              display="flex"
              flexDirection="row"
              alignItems="normal"
              justifyContent="center"
              flexWrap="wrap"
              gap={10}
              mt={10}
            >
              {data.map((m, i) => (
                <OnlineClubCard
                  data={m}
                  key={i}
                  isAdmin={false}
                  redirectToSignup={redirectToSignup}
                  onClick={() => {
                    router.push(`/classes/online-clubs/${m._id}`);
                  }}
                />
              ))}
            </Box>
          </InfiniteScroll>
        )}
      </Container>
    </>
  );
};

export default ClubList;
