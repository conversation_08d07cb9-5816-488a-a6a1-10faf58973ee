import { Box, Typography } from "@mui/material";
import React from "react";

const Rating = ({ ratings = 4 }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      borderRadius={2}
      px={2}
      sx={{
        background: "rgba(20, 174, 92, 1)",
      }}
    >
      <Typography color="#fff" fontSize={12}>
        {ratings}
      </Typography>
      &nbsp;
      <svg
        width="11"
        height="10"
        viewBox="0 0 11 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.57824 9.82689L3.35324 6.50627L0.776367 4.27377L4.17074 3.98002L5.50012 0.848145L6.82949 3.97939L10.2232 4.27314L7.64637 6.50564L8.42199 9.82627L5.50012 8.06377L2.57824 9.82689Z"
          fill="white"
        />
      </svg>
    </Box>
  );
};

export default Rating;
