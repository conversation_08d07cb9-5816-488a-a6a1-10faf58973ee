import { getPriceSymbol } from "@/utils/classes";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import React, { useState } from "react";

const ImagesCarousel = ({ images, price, currency, sx = {} }) => {
  const [activeImage, setActiveImage] = useState(0);

  const handleNextImage = () => {
    const index = activeImage + 1 >= images.length ? 0 : activeImage + 1;
    setActiveImage(index);
  };

  const handlePrevImage = () => {
    const index = activeImage - 1 < 0 ? images.length - 1 : activeImage - 1;
    setActiveImage(index);
  };

  return (
    <Box
      sx={{
        position: "relative",
        overflow: "hidden",
        borderRadius: 2,
        height: 400,
        width: "100%",
        background: "#000",
        ...sx,
      }}
    >
      <Image alt="" objectFit="contain" src={images[activeImage]} fill />
      <Price amount={`${price}`} currency={currency} />
      {images.length > 1 && (
        <>
          <Next
            onClick={() => {
              handleNextImage();
            }}
          />
          <Next
            isPrev
            onClick={() => {
              handlePrevImage();
            }}
          />
        </>
      )}
    </Box>
  );
};

const Price = ({ amount, currency }) => {
  return (
    <Box
      sx={{
        background: "rgba(0, 0, 0, 0.52)",
        color: "#fff",
        width: "fit-content",
        p: 1,
        px: 3,
        borderRadius: 3,
        position: "absolute",
        bottom: "1rem",
        left: "1rem",
      }}
    >
      <Typography>
        Starting at{" "}
        {getPriceSymbol({
          currency,
        })}
        {amount}{" "}
      </Typography>
    </Box>
  );
};

const Next = ({ onClick, isPrev = false }) => {
  return (
    <Box
      onClick={onClick}
      sx={{
        height: 40,
        width: 40,
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
        position: "absolute",
        top: "45%",
        right: isPrev ? "" : "2%",
        left: isPrev ? "2%" : "",
        background: "rgba(0, 0, 0, 0.18)",
        borderRadius: "50%",
        cursor: "pointer",
      }}
    >
      <svg
        style={{
          transform: isPrev ? "rotate(180deg)" : "",
        }}
        width="13"
        height="20"
        viewBox="0 0 13 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M2.45801 1.5L10.958 10L2.45801 18.5"
          stroke="white"
          stroke-width="3"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </Box>
  );
};

export default ImagesCarousel;
