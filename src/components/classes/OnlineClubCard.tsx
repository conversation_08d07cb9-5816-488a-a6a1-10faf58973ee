import React, { useState } from "react";
import CustomButton from "../CustomButton";
import { Avatar, AvatarGroup, Box, Card, Typography } from "@mui/material";
import Image from "next/image";
import Tag from "../Tag";
import Rating from "./OnlineClub/Rating";
import CheckoutClubsModal from "../CheckoutModal/CheckoutClub/CheckoutClubsModal";
import { ClubType, UserType } from "@/api/mongoTypes";
import { getClubImage, getPriceSymbol } from "@/utils/classes";
import CreatorDetails from "./OnlineClub/CreatorDetails";
import TargetLanguage from "./TargetLanguage";
import { getLanguageName } from "@/utils/common";
import useTranslate from "@/hooks/useTranslate";
import axiosInstance from "@/utils/interceptor";
import { useSnackbar } from "@/hooks/useSnackbar";

type OnlineClubCardProps = React.FC<{
  data: ClubType;
  onClick?: () => void;
  redirectToSignup?: () => void;
  onDelete?: () => void;
  isAdmin: boolean;
}>;
const OnlineClubCard: OnlineClubCardProps = ({
  data,
  onClick,
  isAdmin = false,
  redirectToSignup = () => {},
  onDelete = () => {},
}) => {
  const { translate } = useTranslate();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const imageUrl = getClubImage(data);
  const [isDeleting, setIsDeleting] = useState(false);
  const { showSnackbar } = useSnackbar();

  const handleDelete = async () => {
    if (data.noOfStudentsEnrolled > 0) {
      showSnackbar(
        `You cannot delete this club as it has been enrolled by ${data.noOfStudentsEnrolled} users`,
        {
          type: "error",
        }
      );
      return;
    }

    const handleError = () => {
      setIsDeleting(true);
      showSnackbar("Failed to delete club", {
        type: "error",
      });
    };

    try {
      const { data: deletedData, status } = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/club/delete/${data._id}`
      );
      if (status === 409) {
        showSnackbar(deletedData.message, {
          type: "error",
        });
        return;
      }
      if (deletedData.success && deletedData?.data) {
        showSnackbar("Deleted the club successfully", {
          type: "success",
        });
        onDelete && onDelete();
      } else {
        handleError();
      }
      setIsDeleting(false);
    } catch (error) {
      console.log("erropr", error);
      handleError();
    }
  };

  const creatorDetails = data.teachers.map((m) => m as UserType);

  const rating = data?.ratings?.value ?? 0;

  return (
    <>
      <Card
        onClick={() => {
          if (onClick) {
            onClick();
          }
        }}
        sx={{
          width: {
            xs: "100%",
            md: 350,
            sm: 275,
          },
          borderRadius: 5,
          p: 4,
          boxShadow: "0px 2px 10px 0px #00000029",
          flexDirection: "column",
          display: "flex",
          justifyContent: "space-between",
          cursor: "pointer",
        }}
      >
        <Box
          width="100%"
          height={250}
          mb={2}
          sx={{
            position: "relative",
            overflow: "hidden",
            borderRadius: 3,
          }}
        >
          <Image
            alt="beruh"
            src={imageUrl}
            quality={75}
            objectFit="cover"
            priority
            fill
          />

          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
            p={1}
            sx={{
              position: "absolute",
              bottom: 2,
            }}
          >
            <Box
              sx={{
                background: "rgba(0, 0, 0, 0.52)",
                color: "#fff",
                p: 2,
                borderRadius: 2,
              }}
            >
              <Typography sx={{ fontSize: 12 }}>
                <span style={{ fontSize: 10, fontWeight: 400 }}>
                  {translate("clubSubs.startingat")}&nbsp;
                </span>
                {getPriceSymbol({
                  currency: data.currency,
                })}
                {String(data.price)}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box display="flex" flexDirection="row" alignItems="center">
          <span
            style={{
              height: 6,
              width: 6,
              borderRadius: 3,
              background: "rgba(249, 178, 56, 1)",
            }}
          />
          &nbsp;
          <Typography fontSize={14} color="rgba(109, 109, 109, 1)">
            ONLINE
          </Typography>
        </Box>

        <Typography
          textAlign="start"
          sx={{ fontWeight: "bolder", fontSize: 16 }}
        >
          {data.title}
        </Typography>
        <CreatorDetails creatorDetails={creatorDetails} />
        <TargetLanguage name={getLanguageName(data?.targetLanguage)} />

        <Box
          display="flex"
          flexDirection="row"
          sx={{ overflow: "hidden" }}
          alignItems="center"
          mt={5}
        >
          {data.categories?.map((m) => (
            <Tag text={m} key={m} />
          ))}
        </Box>

        <Box
          mt={5}
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
        >
          {rating > 0 && (
            <Box display="flex" flexDirection="row" alignItems="center">
              <Rating ratings={rating} />
              &nbsp;
              <Typography fontSize={12}>
                {translate("clubSubs.rating")}
              </Typography>
            </Box>
          )}
          {+data?.noOfStudentsEnrolled > 0 && (
            <Typography color="rgba(129, 129, 129, 1)" fontSize={12}>
              {`${data?.noOfStudentsEnrolled} ${translate(
                "clubSubs.student-enrolled"
              )}`}
            </Typography>
          )}
        </Box>

        {isAdmin ? (
          <CustomButton
            disabled={isDeleting}
            text="Delete"
            colortype="secondary"
            sx={{
              width: "100%",
              background: "red",
              mt: 3,
              "&:hover": {
                background: "red",
              },
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleDelete();
            }}
          />
        ) : (
          <></>
        )}

        {!isAdmin && (
          <Box display="flex" flexDirection="column">
            <CustomButton
              text={translate("common.subscribe")}
              onClick={(e) => {
                e.stopPropagation();
                setIsModalOpen(true);
              }}
              sx={{
                width: "100%",
                mt: 3,
              }}
            />
          </Box>
        )}
      </Card>
      {isModalOpen && (
        <CheckoutClubsModal
          open={isModalOpen}
          setOpen={setIsModalOpen}
          redirectToSignup={redirectToSignup}
          data={data}
        />
      )}
    </>
  );
};

export default OnlineClubCard;
