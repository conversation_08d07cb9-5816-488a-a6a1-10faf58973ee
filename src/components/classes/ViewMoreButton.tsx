import useTranslate from "@/hooks/useTranslate";
import { Box, Typography } from "@mui/material";
import React from "react";

const ViewMoreButton = ({ isViewMore, toggle }) => {
  const { translate } = useTranslate();
  return (
    <Box
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="center"
      mt={6}
      mb={10}
    >
      <Typography
        onClick={() => {
          toggle();
        }}
        sx={{
          fontSize: "0.9rem",
          color: "#14A79C",
          fontWeight: "700",
          cursor: "pointer",
        }}
      >
        {translate(!isViewMore ? "common.more.details" : "common.less.details")}
      </Typography>
    </Box>
  );
};

export default ViewMoreButton;
