import React from "react";
import { Typo<PERSON>, <PERSON><PERSON>, Box } from "@mui/material";

import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

export default function FAQs({ faqs }) {
  return (
    <>
      <Box
        sx={{
          padding: { xs: "50px 40px", sm: "50px 0px" },
          display: { xs: "flex" },
          flexDirection: { xs: "column" },
        }}
      >
        <Typography
          sx={{
            textAlign: "center",
            fontSize: { xs: "24px", sm: "48px" },
            fontWeight: { xs: "700", sm: "500" },
          }}
        >
          Todo lo que Necesitas Saber
        </Typography>
        <Typography
          sx={{
            textAlign: "center",
            paddingBottom: "8px",
            fontSize: { xs: "16px", sm: "24px" },
            fontWeight: "400",
          }}
        >
          Preguntas Comunes con las Respuestas a tus Dudas
        </Typography>
        <Box
          sx={{
            display: { xs: "flex" },
            margin: { xs: "auto" },
            alignItems: "center",
            marginTop: { xs: "20px", sm: "50px" },
            flexDirection: { xs: "column" },
            maxWidth: { xs: "630px" },
          }}
        >
          {faqs.map((faq, index) => (
            <Accordion
              key={index}
              sx={{
                borderRadius: "5px",
                paddingLeft: { xs: "20px" },
                paddingRight: { xs: "20px" },
                paddingTop: { xs: "10px", sm: "10px" },
                paddingBottom: { xs: "10px", sm: "10px" },
                backgroundColor: index % 2 === 0 ? "white" : "#E6E6E6", // Alternating background colors
                marginBottom: { sm: "5px" },
                boxShadow: "none", // Remove drop shadow
                "&:before": {
                  display: "none", // Remove default border before the Accordion
                },
                "&.Mui-expanded": {
                  boxShadow: "none", // Remove shadow when expanded
                },
                "&.Mui-focused": {
                  outline: "none", // Remove outline on focus
                },
              }}
            >
              <AccordionSummary
                sx={{}}
                expandIcon={
                  <ExpandMoreIcon
                    sx={{
                      borderRadius: "50px",
                      backgroundColor: index % 2 === 0 ? "#E6E6E6" : "white",
                      color: index % 2 === 0 ? "#323232" : "#323232",
                    }}
                  />
                }
                aria-controls={"panel" + index + "-content"}
                id={"panel" + index + "-header"}
              >
                <Typography
                  sx={{
                    textAlign: "left",
                    fontSize: { xs: "14px", sm: "16px" },
                    fontWeight: "700",
                  }}
                >
                  {faq.question}
                </Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ textAlign: "left" }}>
                <Typography>{faq.answer}</Typography>
              </AccordionDetails>
            </Accordion>
          ))}
          <Button
            sx={{ marginTop: { xs: "40px" }, backgroundColor: "#14A79C" }}
          >
            Read all FAQs
          </Button>
        </Box>
      </Box>
    </>
  );
}
