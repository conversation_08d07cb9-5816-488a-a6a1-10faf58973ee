import React from "react";
import { Box, Typography, Button } from "@mui/material";
import Image from "next/image.js";
import TagComponent from "../dashboard/TagComponent";
import { useRouter } from "next/router";
import pin from "@./../../public/images/icons/pin.svg";
const cardContainerStyles = {
  padding: "16px 15px 26px 15px",
  gap: "16px",
  borderRadius: "14px",
  boxShadow: "0px 2px 10px 0px #00000029",
};
const cardImageContainerStyles = {
  // width: "100%",
  // position: "relative",
  marginBottom: "16px",
  position: "relative",
  width: "100%",
  height: "200px",
};

const imageGradientStyles = {
  position: "absolute",
  top: 0,
  left: 0,
  width: "100%",
  height: "100%",
  background: "linear-gradient(to bottom, rgba(0,0,0,0.5), rgba(0,0,0,0))",
  zIndex: 2,
  borderRadius: "14px",
};
const buttonStyles = {
  position: "absolute",
  bottom: "1em",
  right: "0.6em",
  zIndex: "3",
  fontSize: "14px",
  fontWeight: "500",
  width: "90px",
  height: "30px",
  whiteSpace: "nowrap",
  textTransform: "none",
  backgroundColor: "#F9B238",
};
const eventLevelStyles = {
  position: "absolute",
  top: "0.4em",
  left: "0.8em",
  zIndex: "3",
  fontSize: "14px",
  fontWeight: "500",
  color: "white",
  whiteSpace: "nowrap",
};
const cardBodyContainerStyles = { width: "100%" };
const onlineStatusStyles = {
  color: "#6d6d6d",
  fontSize: "12px",
  fontWeight: "400",
  marginBottom: "10px",
  textTransform: "uppercase",
};
const eventTitleStyles = {
  fontWeight: "500",
  fontSize: "18px",
  display: "-webkit-box",
  overflow: "hidden",
  textOverflow: "ellipsis",
  WebkitLineClamp: 2, // Limits to 2 lines
  WebkitBoxOrient: "vertical",
  lineHeight: "24px",
  marginBottom: "10px",
};
const eventDescriptionStyles = {
  fontWeight: "400",
  fontSize: "16px",
  color: "#B3B3B3",
  display: "-webkit-box",
  overflow: "hidden",
  textOverflow: "ellipsis",
  WebkitLineClamp: 1, // Limits to 2 lines
  WebkitBoxOrient: "vertical",
  marginBottom: "15px",
};
const tagContainerStyles = {
  display: "flex",
  overflowX: "auto",
  whiteSpace: "nowrap",
  "::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none",
  marginBottom: "20px",
};
const tagStyles = {
  marginRight: "5px",
  backgroundColor: "#D7F7F5",
  color: "#14A79C",
  fontSize: "13px",
  fontWeight: "500",
};
const dateSectionContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
};

const startDateContainerStyles = { width: "50%" };
const dateLabelStyles = {
  fontWeight: "400",
  fontSize: "12px",
  color: "#818181",
};
const dateStyles = {
  fontSize: "14px",
  fontWeight: "500",
  color: "#3C3C3C",
};
const endDateContainerStyles = {
  width: "50%",
  display: "flex",
  justifyContent: "flex-end",
};
const modeLocationContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
};

const imageStyles = { borderRadius: "10px", objectFit: "cover" };
export default function EventCard({ event }) {
  const router = useRouter();
  function handleClick() {
    router.push(`/classes/experiencias-libres/pricing/${event.id}`);
  }
  return (
    <Box sx={cardContainerStyles}>
      <Box sx={cardImageContainerStyles}>
        {/* Parent container */}
        <Image src={event.img} alt={event.title} fill style={imageStyles} />
        <Box sx={imageGradientStyles} />
        <Button sx={buttonStyles} onClick={handleClick}>
          Book Now
        </Button>
        <Typography sx={eventLevelStyles}>{event.level}</Typography>
      </Box>
      <Box sx={cardBodyContainerStyles}>
        <Box sx={modeLocationContainerStyles}>
          <Typography sx={onlineStatusStyles}>&#8226; {event.mode}</Typography>
          {event.mode == "offline" && (
            <Box sx={{ display: "flex", flexDirection: "row" }}>
              <Image src={pin} alt="evt"></Image>
              <Typography sx={onlineStatusStyles}>
                Offline Location Address
              </Typography>
            </Box>
          )}
        </Box>
        <Typography sx={eventTitleStyles}>{event.title}</Typography>
        <Typography sx={eventDescriptionStyles}>{event.description}</Typography>
        <Box sx={tagContainerStyles}>
          {event.tags.map((tag) => (
            <TagComponent key={tag} label={tag} tagStyles={tagStyles} />
          ))}
        </Box>
        <Box sx={dateSectionContainerStyles}>
          <Box sx={startDateContainerStyles}>
            <Typography sx={dateLabelStyles}>Start</Typography>
            <Typography sx={dateStyles}>{event.start}</Typography>
          </Box>
          <Box sx={endDateContainerStyles}>
            <Box>
              <Typography sx={dateLabelStyles}>End</Typography>
              <Typography sx={dateStyles}>{event.end}</Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
