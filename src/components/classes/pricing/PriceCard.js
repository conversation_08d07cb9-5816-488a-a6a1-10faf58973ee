import React from "react";
import { Box, Typography, Container, Grid, Button } from "@mui/material";

const cardContainerStyles = {
  border: "2px solid #14A79C",
  borderRadius: "10px",
  height: "500px",
  padding: "45px 18px 30px 18px",
  position: "relative",
};
const planeNameContainerStyles = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  justifyContent: "center",
  height: "70px",
};
const planNameStyles = {
  fontSize: "32px",
  fontWeight: "700",
  textAlign: "center",
  lineHeight: "1",
};
const priceStyles = {
  fontSize: "48px",
  fontWeight: "400",
  textAlign: "center",
  paddingBottom: "15px",
};
const costPerPeriodStyles = {
  fontSize: "14px",
  fontWeight: "400",
  textAlign: "center",
  color: "#64748B",
  marginBottom: "27px",
};
const buttonStyles = {
  backgroundColor: "#14A79C",
  width: "100%",
  marginBottom: "27px",
};
const dividerStyles = { border: "1px solid #EAEAEA", marginBottom: "27px" };
const descriptionStyles = {
  fontSize: "13px",
  fontWeight: "600",
  textAlign: "center",
  marginBottom: "14px",
};
const customizationOptionStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: "10px",
  marginBottom: "14px",
};

const customizationSavingsStyles = {
  fontSize: "10px",
  fontWeight: "500",
  backgroundColor: "#F9B238",
  color: "white",
  padding: "5px",
  borderRadius: "5px",
};
const savingsStyles = {
  position: "absolute",
  top: "1em",
  right: "0",
  zIndex: "2",
  fontSize: "12px",
  fontWeight: "700",
  backgroundColor: "#F9B238",
  color: "white",
  padding: "8px",
  borderRadius: "5px 0px 0px 5px",
};
export default function PriceCard({ data }) {
  return (
    <Box sx={cardContainerStyles}>
      <Box sx={planeNameContainerStyles}>
        <Typography sx={planNameStyles}>{data.planName}</Typography>
      </Box>
      <Typography sx={priceStyles}> ${data.price}</Typography>
      <Typography sx={costPerPeriodStyles}>
        ${data.costPerPeriod} / {data.costPeriod}
      </Typography>
      <Button sx={buttonStyles}>Buy Now</Button>
      <hr style={dividerStyles}></hr>
      <Typography sx={descriptionStyles}> {data.descriptionTitle}</Typography>
      {data.customizationOptions.map((option) => (
        <Box key={option.id} sx={customizationOptionStyles}>
          <Typography
            sx={{
              fontSize: "13px",
              fontWeight: "500",
              textAlign: "center",
              color: "#64748B",
            }}
          >
            {option.value} {option.timePeriod} : ${option.cost}
          </Typography>
          {option.savings != "" && (
            <Typography sx={customizationSavingsStyles}>
              {option.savings}
            </Typography>
          )}
        </Box>
      ))}
      {data.savings != "" && (
        <Typography sx={savingsStyles}>{data.savings}</Typography>
      )}
    </Box>
  );
}
