import React from "react";
import { createCommunityData } from "../../constant/patitoBasics/patitoBasics";
import { Grid, CardMedia, Typography } from "@mui/material";
import useTranslate from "@/hooks/useTranslate";

const getStyles = (index) => {
  return {
    maxWidth:
      index % 2 === 1
        ? { xs: "180px", sm: "200px", md: "250px", lg: "305px" }
        : {
            xs: "100px",
            sm: "150px",
            md: "200px",
            lg: "240px",
          },
    height:
      index % 2 === 1
        ? { xs: "180px", sm: "200px", md: "250px", lg: "305px" }
        : {
            xs: "100px",
            sm: "150px",
            md: "200px",
            lg: "240px",
          },
    borderRadius: { xs: "40px 0px 40px 0px" },
    margin: { xs: "10px" },
    overflow: "hidden",
  };
};

const CreateCommunity = () => {
  const { translate}=useTranslate()
  return (
    <Grid
      container
      sx={{
        marginTop: { sm: "80px" },
        marginBottom: { xs: "20px", sm: "0px" },
      }}
    >
      <Grid
        item
        order={{ xs: 2, sm: 1 }}
        xs={12}
        sx={{
          display: " flex",
          flexDirection: { xs: "row" },
          justifyContent: {
            xs: "center",
          },
          alignItems: {
            xs: "center",
          },
        }}
      >
        {createCommunityData.images.map((image, index) => {
          return (
            <CardMedia
              key={index}
              component="img"
              image={image}
              alt="Nuestra Misión"
              sx={{
                ...getStyles(index),
              }}
            />
          );
        })}
      </Grid>

      <Grid
        item
        order={{ xs: 1, sm: 2 }}
        xs={12}
        sx={{
          display: "flex",
          flexDirection: { xs: "column" },
          alignItems: { xs: "center" },
          padding: { xs: "0px 40px" },
        }}
      >
        <Typography
          sx={{
            marginBottom: "16px",
            fontSize: { xs: "24px", sm: "32px" },
            paddingTop: { xs: "30px", sm: "10px" },
          }}
        >
          <strong>{translate(createCommunityData.title)} </strong>
          {translate(createCommunityData.subtitle)}
        </Typography>

        <Typography
          maxWidth="md"
          sx={{
            marginBottom: { xs: "20px", sm: "60px" },
            fontSize: { xs: "16px" },
            fontWeight: { xs: "500" },
          }}
        >
          {translate(createCommunityData.body)}
        </Typography>
      </Grid>
    </Grid>
  );
};

export default CreateCommunity;