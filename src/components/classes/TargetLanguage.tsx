import { Box, Typography } from "@mui/material";
import React from "react";

const TargetLanguage = ({ name }) => {
  if (!name) {
    return null;
  }
  return (
    <Box
      display="flex"
      flexDirection="row"
      mt={2}
      gap={2}
      justifyContent="space-between"
    >
      <Box
        sx={{
          background: "#D7F7F5",
          borderRadius: 10,
          px: 2,
          py: 2,
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
        }}
      >
        <Typography fontSize="0.75rem" color="#14A79C" mb={0}>
          Target Language: <b style={{ textTransform: "uppercase" }}>{name}</b>
        </Typography>
      </Box>
    </Box>
  );
};

export default TargetLanguage;
