import useTranslate from "@/hooks/useTranslate";
import { Box, Typography } from "@mui/material";
import React from "react";
import BusinessEnglishCard from "./BusinessEnglishCard";
import { MUIStyle } from "@/types";

type BusinessEnglishBenefitsProps = React.FC<{
  data: any[];
  title: string;
  description: string;
  sx?: MUIStyle;
}>;
const BusinessEnglishBenefits: BusinessEnglishBenefitsProps = ({
  data,
  title,
  description,
  sx = {},
}) => {
  const { translate } = useTranslate();

  return (
    <Box sx={sx}>
      <Box my={12}>
        <Typography fontWeight={800} fontSize="1.75rem">
          {translate(title)}
        </Typography>
        <Typography fontSize="1.25rem">{translate(description)}</Typography>
      </Box>
      <Box
        display="flex"
        flexDirection="row"
        flexWrap="wrap"
        alignItems="stretch"
        justifyContent="center"
        gap={5}
        mt={10}
        mb={4}
      >
        {data.map((m, i) => (
          <BusinessEnglishCard key={m.id} data={m} />
        ))}
      </Box>
    </Box>
  );
};

export default BusinessEnglishBenefits;
