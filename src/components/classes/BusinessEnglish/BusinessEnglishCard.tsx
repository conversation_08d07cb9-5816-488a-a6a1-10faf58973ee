import React from "react";
import { Box, Card, Container, Typography } from "@mui/material";
import useTranslate from "@/hooks/useTranslate";
import TimelineIcon from "@mui/icons-material/Timeline";
import LocalPoliceIcon from "@mui/icons-material/LocalPolice";
import RadioButtonCheckedIcon from "@mui/icons-material/RadioButtonChecked";
import GroupIcon from "@mui/icons-material/Group";
import WorkIcon from "@mui/icons-material/Work";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

const BusinessEnglishCard = ({ data }) => {
  const { translate } = useTranslate();
  return (
    <Card
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: {
          xs: "100%",
          sm: "30%",
        },
        p: 4,
        gap: 4,
        borderRadius: 4,
        boxShadow: "1.73px 1.73px 6.06px 3.46px #cccccc2e",
      }}
    >
      <Box
        sx={{
          height: 60,
          width: 60,
          borderRadius: 2,
          background: getBgColor(data.id),
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          my: 4,
          justifyContent: "center",
        }}
      >
        <Icon id={data.id} />
      </Box>
      <Typography fontSize="1.35rem" fontWeight="800">
        {translate(data.title)}
      </Typography>
      <Typography fontSize="1rem" color="gray">
        {translate(data.description)}
      </Typography>
    </Card>
  );
};

const getBgColor = (id = 1) => {
  if (id === 1) {
    return "#ff000029";
  }
  if (id === 2) {
    return "#14e91457";
  }
  if (id === 3) {
    return "#ffa50059";
  }
  if (id === 4) {
    return "#ffff004f";
  }
  if (id === 5) {
    return "#0000ff2e";
  }
  if (id === 6) {
    return "#8000804f";
  }
};

const Icon = ({ id = 1 }) => {
  if (id === 1) {
    return <TimelineIcon />;
  }
  if (id === 2) {
    return <LocalPoliceIcon />;
  }
  if (id === 3) {
    return <RadioButtonCheckedIcon />;
  }
  if (id === 4) {
    return <GroupIcon />;
  }
  if (id === 5) {
    return <WorkIcon />;
  }
  if (id === 6) {
    return <AccessTimeIcon />;
  }
};

export default BusinessEnglishCard;
