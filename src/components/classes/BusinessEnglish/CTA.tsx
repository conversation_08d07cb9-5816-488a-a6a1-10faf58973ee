import { Box, <PERSON>ton, Typography } from "@mui/material";
import React from "react";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";

interface CTAProps {
  title: string;
  description: string;
  ctaButton: string;
}

const CTA: React.FC<CTAProps> = ({ title, description, ctaButton }) => {
  const handleSendMessage = () => {
    const message =
      "¡Hola, Patito Feo! Quisiera más información para las Business English Classes.";
    const NEXT_PUBLIC_WHATSAPP_PHONE = process.env.NEXT_PUBLIC_WHATSAPP_PHONE;
    if (!NEXT_PUBLIC_WHATSAPP_PHONE) return;

    const url = `https://wa.me/${NEXT_PUBLIC_WHATSAPP_PHONE}?text=${encodeURIComponent(
      message
    )}`;
    window.open(url, "_blank");
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Box
        sx={{
          mt: 10,
          borderRadius: 4,
          position: "relative",
          p: "2px",
          width: {
            xs: "100%",
            sm: "80%",
          },
          overflow: "hidden",
          background:
            "linear-gradient(90deg, rgba(20, 167, 156, 1) 0%, rgba(249, 178, 56, 1) 78%)",
        }}
      >
        <Box
          sx={{
            background: "#fff",
            p: 4,
            py: 10,
            borderRadius: 3,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Typography fontSize="1.45rem" fontWeight={700}>
            {title}
          </Typography>
          <Typography fontSize="1rem">{description}</Typography>
          <Button
            sx={{
              display: "flex",
              gap: 4,
              width: "fit-content",
              mt: 4,
              background:
                "linear-gradient(90deg, rgba(20, 167, 156, 1) 0%, rgba(249, 178, 56, 1) 78%)",
            }}
            onClick={handleSendMessage}
            variant="contained"
          >
            <WhatsAppIcon />
            {ctaButton}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default CTA;
