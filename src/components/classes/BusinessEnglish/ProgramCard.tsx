import React from "react";
import { Box, Typography, Button } from "@mui/material";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import useTranslate from "@/hooks/useTranslate";

const ProgramCard = () => {
  const { translate } = useTranslate();

  const handleSendMessage = () => {
    const message =
      "¡Hola, Patito Feo! Quisiera más información para las Business English Classes.”";
    const NEXT_PUBLIC_WHATSAPP_PHONE = process.env.NEXT_PUBLIC_WHATSAPP_PHONE;
    const url = `https://wa.me/${NEXT_PUBLIC_WHATSAPP_PHONE}?text=${message}`;
    window.open(url, "_blank");
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        background: "#8080803b",
        borderRadius: 2,
        p: 8,
        my: 15,
      }}
    >
      <Typography fontSize="2rem" fontWeight={700}>
        {translate("bec.different-program")}
      </Typography>
      <Typography fontSize="1.2rem">
        {translate("bec.different-program-info")}
      </Typography>
      <Button
        onClick={handleSendMessage}
        sx={{
          display: "flex",
          flexDirection: "row",
          width: "fit-content",
          p: 4,
          px: 6,
          mt: 4,
        }}
      >
        <WhatsAppIcon />
        &nbsp;
        <Typography fontSize="1.15rem">
          {translate("bec.different-program-button")}
        </Typography>
      </Button>
    </Box>
  );
};

export default ProgramCard;
