import React from "react";
import { Grid, Typography, CardMedia, Box } from "@mui/material";
import useTranslate from "@/hooks/useTranslate";

type SectionHeaderProps = React.FC<{
  children?: React.ReactNode;
  data: any;
  index?: number;
}>;
const SectionHeader: SectionHeaderProps = ({ data, index = 0, children }) => {
  const direction = +index % 2 === 0 ? "row" : "row-reverse";
  return (
    <Box
      justifyContent="center"
      display="flex"
      alignItems="center"
      flexDirection={direction}
      flexWrap="wrap"
      sx={{
        marginTop: "2rem",
        flexDirection: {
          xs: "column-reverse",
          md: direction,
        },
      }}
    >
      <Box
        sx={{
          width: {
            sm: "100%",
            md: "50%",
            lg: "35%",
          },
        }}
      >
        <ImgDetail image={data.image} />
      </Box>
      <Box
        sx={{
          width: {
            sm: "100%",
            md: "50%",
            lg: "65%",
          },
        }}
      >
        {children ? children : <InfoDetail section={data} />}
      </Box>
    </Box>
  );
};

export default SectionHeader;

const ImgDetail = ({ image }) => {
  return (
    <CardMedia
      component="img"
      image={image}
      alt="Clases Lineares"
      sx={{
        maxWidth: { xs: "480px" },
        minHeight: { xs: "352px" },
        borderRadius: { xs: "40px 0px 40px 0px" },
        objectFit: "cover",
        margin: { xs: "auto" },
      }}
    />
  );
};

const InfoDetail = ({ section }) => {
  const { translate } = useTranslate();
  return (
    <Box
      sx={{
        width: "90%",
        margin: "auto",
      }}
    >
      <Typography
        sx={{
          textAlign: "center",
          fontSize: { xs: "24px", sm: "32px" },
          margin: { xs: "40px" },
        }}
      >
        {translate(section.title)}:
        <strong> {translate(section.subtitle)}</strong>
      </Typography>

      <Box sx={{ padding: { xs: "0px 40px 20px 40px " } }}>
        {section.body.map((content, index) => (
          <Typography
            key={index}
            variant="body1"
            sx={{
              textAlign: "center",
              fontSize: { sm: "1rem" },
              fontWeight: { xs: "500", sm: "400" },
              paddingBottom: { xs: "10px" },
            }}
          >
            {translate(content)}
          </Typography>
        ))}
      </Box>
    </Box>
  );
};
