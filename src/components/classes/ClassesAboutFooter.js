import { Box } from "@mui/material";
import Pricing from "@/components/classes/Pricing";
import FAQs from "@/components/classes/FAQs";
import Testimonials from "@/components/classes/Testimonials";
import {
  ClassesFAQs,
  ClassesPackages,
  TestimonialData,
} from "@/constant/classes/ClasesLinearesData";

const inDevelopment = true;

const EmptyComponent = () => <></>;

export default function ClassesAboutFooter() {
  return (
    <>
      {/* Payment Options Section */}
      {inDevelopment ? (
        <EmptyComponent />
      ) : (
        <Pricing classesPackages={ClassesPackages} />
      )}

      {/* FAQ Section */}
      {inDevelopment ? <EmptyComponent /> : <FAQs faqs={ClassesFAQs} />}

      {/* Carousel Section */}
      <Box sx={{ marginTop: { sm: "40px" } }}>
        {inDevelopment ? (
          <EmptyComponent />
        ) : (
          <Testimonials data={TestimonialData} />
        )}
      </Box>
    </>
  );
}
