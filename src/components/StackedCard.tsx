import { Box, SxProps, Theme } from "@mui/material";
import React from "react";

type StackedCardProps = React.FC<{
  children: React.ReactNode;

  isRight?: boolean;
  color?: string;
  sx?: SxProps<Theme>;
}>;

const StackedCard: StackedCardProps = ({
  children,
  color = "#14A79C",
  isRight = false,
  sx = {},
}) => {
  const positionStyle = (() => {
    if (isRight) {
      return {
        top: 10,
        right: 10,
      };
    } else {
      return {
        top: 10,
        left: 10,
      };
    }
  })();

  return (
    <Box
      sx={{
        height: "100%",
        width: "100%",
        borderRadius: 5,
        position: "relative",
        p: 1,
        ...sx,
      }}
    >
      <Box
        sx={{
          background: "#fff",
          borderRadius: 3,
          position: "relative",
          zIndex: 10,
          border: `4px solid ${color}`,
          height: "100%",
          width: "100%",
          overflow: "hidden",
        }}
      >
        {children}
      </Box>
      <Box
        sx={{
          height: "100%",
          width: "100%",
          background: color,
          borderRadius: 5,
          position: "absolute",
          overflow: "hidden",
          zIndex: 0,
          ...positionStyle,
        }}
      />
    </Box>
  );
};

export default StackedCard;
