import React, { useState, useEffect } from 'react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import { Box, styled } from '@mui/material';
import { isPossiblePhoneNumber } from 'react-phone-number-input';

// Main container for the phone input
const StyledPhoneInputContainer = styled(Box)(({ theme }) => ({
  '& .PhoneInput': {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    position: 'relative',
    borderRadius: '8px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderColor:
      theme.palette.mode === 'light'
        ? 'rgba(0, 0, 0, 0.23)'
        : 'rgba(255, 255, 255, 0.23)',
    '&:hover': {
      borderColor: theme.palette.text.primary,
    },
    '&:focus-within': {
      borderColor: theme.palette.primary.main,
      borderWidth: '2px',
    },
  },
  '& .PhoneInputCountry': {
    display: 'flex',
    alignItems: 'center',
    margin: theme.spacing(0, 2),
    paddingLeft: theme.spacing(1),
    height: '56px',
  },
  '& .PhoneInputCountryIcon': {
    width: '24px',
    height: '16px',
    borderRadius: '2px',
    margin: theme.spacing(0, 1, 0, 0),
  },
  '& .PhoneInputInput': {
    flex: 1,
    border: 'none',
    background: 'transparent',
    height: '56px',
    padding: '16.5px 14px',
    paddingLeft: 0,
    paddingRight: theme.spacing(3.5),
    fontSize: '1rem',
    fontFamily: theme.typography.fontFamily,
    color: theme.palette.text.primary,
    '&:focus': {
      outline: 'none',
    },
    '&::placeholder': {
      color: theme.palette.text.disabled,
      opacity: 1,
    },
  },
  position: 'relative',
  marginTop: theme.spacing(2),
  marginBottom: theme.spacing(1),
}));

const MaterialUIPhoneInput = styled('div')(({ theme }) => ({
  position: 'relative',
  '& .label-floating': {
    position: 'absolute',
    top: '-10px',
    left: '10px',
    backgroundColor: theme.palette.background.paper,
    padding: '0 5px',
    fontSize: '12px',
    color: theme.palette.text.secondary,
    zIndex: 2,
  },
  '&.error .PhoneInput': {
    borderColor: theme.palette.error.main,
    '&:hover, &:focus-within': {
      borderColor: theme.palette.error.main,
    },
  },
  '&.disabled .PhoneInput': {
    backgroundColor: 'rgba(0, 0, 0, 0.12)',
    color: 'rgba(0, 0, 0, 0.38)',
    borderColor: 'rgba(0, 0, 0, 0.26)',
  },
}));

interface PhoneNumberInputProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  required?: boolean;
}

const PhoneNumberInput: React.FC<PhoneNumberInputProps> = ({
  value,
  onChange,
  label = 'Phone Number',
  placeholder = 'Enter your phone number',
  error = false,
  helperText = '',
  disabled = false,
  required = false,
}) => {
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [validationError, setValidationError] = useState(false);

  // Validate phone number whenever value changes
  useEffect(() => {
    if (value) {
      const isValid = isPossiblePhoneNumber(value);
      setValidationError(!isValid);
    } else {
      setValidationError(false);
    }
  }, [value]);

  // Get supported countries from environment variables
  let countries = [];
  if (process.env.NEXT_PUBLIC_SUPPORTED_COUNTRIES) {
    const supportedCountries =
      process.env.NEXT_PUBLIC_SUPPORTED_COUNTRIES.split(',').map((code) =>
        code.trim()
      );

    if (supportedCountries.length > 0) {
      countries = supportedCountries;
    }
  }

  return (
    <Box sx={{ width: '100%' }}>
      <MaterialUIPhoneInput
        className={`${error || validationError ? 'error' : ''} ${
          disabled ? 'disabled' : ''
        }`}
      >
        {label && (value || isInputFocused) && (
          <span className='label-floating'>
            {label}
            {required ? ' *' : ''}
          </span>
        )}

        <StyledPhoneInputContainer>
          <PhoneInput
            international
            countryCallingCodeEditable={false}
            countries={countries}
            defaultCountry='US'
            value={value}
            onChange={onChange}
            placeholder={label || placeholder}
            onFocus={() => setIsInputFocused(true)}
            onBlur={() => setIsInputFocused(false)}
            disabled={disabled}
          />
        </StyledPhoneInputContainer>
      </MaterialUIPhoneInput>

      {(error || validationError || helperText) && (
        <Box
          sx={{
            color: error || validationError ? 'error.main' : 'text.secondary',
            fontSize: '0.75rem',
            mt: 0.5,
            ml: 1.5,
          }}
        >
          {helperText ||
            (validationError && 'Please enter a valid phone number')}
        </Box>
      )}
    </Box>
  );
};

export default PhoneNumberInput;
