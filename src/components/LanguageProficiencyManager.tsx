import React, { useState, useEffect, useMemo } from "react";
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  Typography,
  Grid,
  Button,
  TextField,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import axios from "axios";
import { useSnackbar } from "@/hooks/useSnackbar";
import { LanguageProficiencyType, LanguageType } from "@/api/mongoTypes";
import { langugaeStateTypes, SingleLanguageType } from "@/types";
import InputAdornment from "@mui/material/InputAdornment";
import SearchIcon from "@mui/icons-material/Search";
import useTranslate from "@/hooks/useTranslate";
import { getProficiencyId } from "@/utils/profile";

interface LanguageProficiencyManagerProps {
  userId: string;
  value: langugaeStateTypes;
  onChange: (languages: langugaeStateTypes) => void;
}

// Styled container for select inputs
const StyledSelectContainer = {
  display: "inline-flex",
  alignItems: "center",
  gap: 2,
  backgroundColor: "#fff",
  borderRadius: "16px",
  boxShadow: "0px 2px 14px rgba(0, 0, 0, 0.08)",
  padding: "4px 16px",
  width: "100%",
  height: "100%",
};

// Label styling
const StyledLabel = {
  color: "rgba(0, 0, 0, 0.87)",
  fontSize: "16px",
  fontWeight: 400,
  minWidth: "fit-content",
};

// Add more button container
const ShowAddMoreContainerStyle = {
  marginTop: "10px",
};

export const AddMoreButton = (props) => {
  const { translate } = useTranslate();

  return (
    <Button
      sx={{
        fontSize: "12px",
        fontWeight: "700",
        background: "transparent",
        padding: "0",
        width: "auto",
        cursor: "pointer",
        color: "#14A79C",
        textTransform: "none",
        "&:hover": {
          background: "transparent",
        },
      }}
      {...props}
    >
      <svg
        width="25"
        height="25"
        viewBox="0 0 24 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_1891_8963)">
          <path
            d="M13 7.5H11V11.5H7V13.5H11V17.5H13V13.5H17V11.5H13V7.5ZM12 2.5C6.48 2.5 2 6.98 2 12.5C2 18.02 6.48 22.5 12 22.5C17.52 22.5 22 18.02 22 12.5C22 6.98 17.52 2.5 12 2.5ZM12 20.5C7.59 20.5 4 16.91 4 12.5C4 8.09 7.59 4.5 12 4.5C16.41 4.5 20 8.09 20 12.5C20 16.91 16.41 20.5 12 20.5Z"
            fill="#14A79C"
          />
        </g>
        <defs>
          <clipPath id="clip0_1891_8963">
            <rect
              width="24"
              height="24"
              fill="white"
              transform="translate(0 0.5)"
            />
          </clipPath>
        </defs>
      </svg>
      <span>{translate("kyc.add-more")}</span>
    </Button>
  );
};

const LanguageProficiencyManager: React.FC<LanguageProficiencyManagerProps> = ({
  userId,
  value: languages,
  onChange: setLanguages,
}) => {
  const { translate } = useTranslate();
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(true);
  const [languagesList, setLanguagesList] = useState<LanguageType[]>([]);
  const [proficiencyList, setProficiencyList] = useState<
    LanguageProficiencyType[]
  >([]);

  // Calculate if we should show the "Add More" button
  const showAddMoreButton = React.useMemo(() => {
    if (!languagesList.length) return false;
    return languagesList.length > languages.length;
  }, [languagesList, languages]);

  // Load data from API
  useEffect(() => {
    const fetchLanguageData = async () => {
      try {
        setIsLoading(true);
        if (!userId) return;

        // Fetch languages and proficiency levels
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
        );

        if (data && !data.isError) {
          setLanguagesList(data.languages || []);
          setProficiencyList(data.languageProficiencies || []);

          if (languages.length === 0) {
            const { data: languagesData } = await axios.get(
              `${process.env.NEXT_PUBLIC_BASE_URL}api/user/language/${userId}`
            );

            if (languagesData?.data && languagesData.data.length > 0) {
              // Map user languages from API
              const userLanguages = languagesData.data
                .filter((f) => f.language && f.proficiency && f._id)
                .map((m) => ({
                  language: m.language,
                  proficiency: m.proficiency,
                }));

              if (userLanguages.length > 0) {
                setLanguages(userLanguages);
              } else if (
                data.languages?.length > 0 &&
                data.languageProficiencies?.length > 0
              ) {
                // Initialize with first option if no user languages
                setLanguages([
                  {
                    language: data.languages[0],
                    proficiency: data.languageProficiencies[0],
                  },
                ]);
              }
            } else if (
              data.languages?.length > 0 &&
              data.languageProficiencies?.length > 0
            ) {
              setLanguages([
                {
                  language: data.languages[0],
                  proficiency: data.languageProficiencies[0],
                },
              ]);
            }
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching language data:", error);
        showSnackbar(translate("mp.langs-load-failed"), { type: "error" });
        setIsLoading(false);
      }
    };

    fetchLanguageData();
  }, [userId, setLanguages, showSnackbar]);

  // Handle new language adds
  const handleAddMore = () => {
    // Find a language that hasn't been selected yet
    const availableLanguage = languagesList.find(
      (language) =>
        !languages.some(
          (item: SingleLanguageType) => item.language._id === language._id
        )
    );

    if (availableLanguage && proficiencyList.length > 0) {
      setLanguages([
        ...languages,
        {
          language: availableLanguage,
          proficiency: proficiencyList[0],
        },
      ]);
    }
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Grid container spacing={4}>
        {languages.map((item, index) => (
          <Grid item xs={12} key={`lang-row-${index}`}>
            <SelectionRow
              index={index}
              languages={languages}
              setLanguages={setLanguages}
              languagesList={languagesList}
              proficiencyList={proficiencyList}
            />
          </Grid>
        ))}
      </Grid>
      {showAddMoreButton && (
        <Box sx={ShowAddMoreContainerStyle}>
          <AddMoreButton onClick={handleAddMore} />
        </Box>
      )}
    </Box>
  );
};

interface SelectionRowProps {
  languagesList: LanguageType[];
  proficiencyList: LanguageProficiencyType[];
  languages: langugaeStateTypes;
  setLanguages: React.Dispatch<React.SetStateAction<langugaeStateTypes>>;
  index: number;
}

const SelectionRow: React.FC<SelectionRowProps> = ({
  languagesList,
  proficiencyList,
  languages,
  setLanguages,
  index,
}) => {
  const selectedLanguage = languages[index]?.language?.name;
  const selectedLanguageProf = languages[index]?.proficiency?.pfLevel.en;
  const { translate } = useTranslate();

  return (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={6}>
        <Box sx={StyledSelectContainer}>
          <Typography variant="body1" sx={StyledLabel}>
            {translate("kyc.Language")}:
          </Typography>
          <SelectLanguage
            languages={languages}
            index={index}
            languagesList={languagesList}
            selectedLanguage={selectedLanguage}
            setLanguages={setLanguages}
          />
        </Box>
      </Grid>
      <Grid item xs={12} sm={6} sx={{ position: "relative" }}>
        <Box sx={StyledSelectContainer}>
          <Typography variant="body1" sx={StyledLabel}>
            {translate("kyc.Level")}:
          </Typography>
          <SelectProficiency
            index={index}
            languages={languages}
            proficiencyList={proficiencyList}
            setLanguages={setLanguages}
            selectedLanguageProf={selectedLanguageProf}
          />
        </Box>
        {languages.length > 1 && (
          <Box
            sx={{
              cursor: "pointer",
              position: "absolute",
              right: { xs: "10px", sm: "-20px" },
              top: "50%",
              transform: "translateY(-50%)",
              zIndex: 1,
            }}
          >
            <DeleteIcon
              onClick={() => {
                setLanguages((prev) => prev.filter((_, i) => i !== index));
              }}
              sx={{
                color: "#666",
                position: "relative",
                right: "-10px",
                top: "5px",
                transition: "all .2s ease-in",
                "&:hover": {
                  color: "#f44336",
                  transition: "all .3s ease-in-out",
                },
              }}
            />
          </Box>
        )}
      </Grid>
    </Grid>
  );
};

const SelectLanguage = ({
  languages,
  index,
  languagesList,
  selectedLanguage,
  setLanguages,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [open, setOpen] = useState(false);
  const { translate } = useTranslate();

  const handleSelectChange = (e) => {
    const selectedId = e.target.value;
    const findLanguage = languagesList.find((f) => f._id === selectedId);

    if (findLanguage) {
      const updatedLangList = [...languages];
      updatedLangList[index] = {
        ...updatedLangList[index],
        language: findLanguage,
      };
      setLanguages(updatedLangList);
    }

    setOpen(false);
    setSearchTerm(""); // Clear search after selection
  };

  const currentLanguageId = languages[index]?.language?._id || "";

  // Filter available languages (excluding already selected ones) and sort alphabetically
  const filteredAndSortedLanguages = useMemo(() => {
    return languagesList
      .filter((language) => {
        // Skip already selected languages from any other rows
        const isAlreadySelected = languages.some(
          (item, i) => item.language?._id === language._id && i !== index
        );

        // Include if not already selected OR if it's the current selection for this row
        const shouldInclude =
          !isAlreadySelected || language._id === currentLanguageId;

        // Also filter by search term
        const matchesSearch =
          language.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          language.nameInEnglish
            .toLowerCase()
            .includes(searchTerm.toLowerCase());

        return shouldInclude && matchesSearch;
      })
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [languagesList, languages, index, currentLanguageId, searchTerm]);

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSearchTerm(""); // Clear search when closing
  };

  return (
    <FormControl
      sx={{
        width: "100%",
        "& .MuiOutlinedInput-root": {
          backgroundColor: "transparent",
          borderRadius: "16px",
          height: "100%",
        },
        "& .MuiOutlinedInput-notchedOutline": {
          border: "none",
        },
        "& .MuiSelect-select": {
          padding: "12px 14px",
        },
      }}
    >
      <Select
        value={currentLanguageId}
        onChange={handleSelectChange}
        onOpen={handleOpen}
        onClose={handleClose}
        open={open}
        displayEmpty
        renderValue={() =>
          selectedLanguage || (
            <Typography sx={{ color: "rgba(0, 0, 0, 0.38)" }}>
              {translate("kyc.select-language")}
            </Typography>
          )
        }
        MenuProps={{
          PaperProps: {
            sx: {
              maxHeight: 300,
            },
          },
        }}
      >
        {/* Search Input */}
        <Box
          sx={{
            px: 2,
            pb: 1,
            pt: 1,
            position: "sticky",
            top: 0,
            backgroundColor: "white",
            zIndex: 1,
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
          }}
        >
          <TextField
            size="small"
            placeholder={translate("kyc.search-lang")}
            value={searchTerm}
            onChange={handleSearchChange}
            onClick={(e) => e.stopPropagation()} // Prevent dropdown from closing
            onKeyDown={(e) => e.stopPropagation()} // Prevent dropdown from closing
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{
              width: "100%",
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: "rgba(0, 0, 0, 0.23)",
                },
                "&:hover fieldset": {
                  borderColor: "rgba(0, 0, 0, 0.87)",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "primary.main",
                },
              },
            }}
          />
        </Box>

        {/* Placeholder disabled option */}
        <MenuItem value="" disabled>
          <Typography sx={{ color: "rgba(0, 0, 0, 0.38)" }}>
            {translate("kyc.select-language")}
          </Typography>
        </MenuItem>

        {/* Language Options */}
        {filteredAndSortedLanguages.length > 0 ? (
          filteredAndSortedLanguages.map((language) => (
            <MenuItem key={language._id} value={language._id}>
              {language.name}
            </MenuItem>
          ))
        ) : (
          <MenuItem disabled>
            <Box sx={{ color: "text.secondary", fontStyle: "italic" }}>
              {translate(
                searchTerm ? "kyc.nolangfound" : "mp.no-available-langs"
              )}
            </Box>
          </MenuItem>
        )}
      </Select>
    </FormControl>
  );
};

const SelectProficiency = ({
  index,
  languages,
  proficiencyList,
  setLanguages,
  selectedLanguageProf,
}) => {
  const { translate } = useTranslate();
  const onChange = (e) => {
    const selectedId = e.target.value;
    const findProficiency = proficiencyList.find((f) => f._id === selectedId);

    if (findProficiency) {
      const updatedLangList = [...languages];
      updatedLangList[index] = {
        ...updatedLangList[index],
        proficiency: findProficiency,
      };
      setLanguages(updatedLangList);
    }
  };

  const currentProficiencyId = languages[index]?.proficiency?._id || "";

  return (
    <FormControl
      sx={{
        width: "100%",
        "& .MuiOutlinedInput-root": {
          backgroundColor: "transparent",
          borderRadius: "16px",
          height: "100%",
        },
        "& .MuiOutlinedInput-notchedOutline": {
          border: "none",
        },
        "& .MuiSelect-select": {
          padding: "12px 14px",
        },
      }}
    >
      <Select
        value={currentProficiencyId}
        onChange={onChange}
        displayEmpty
        renderValue={() =>
          translate(getProficiencyId(selectedLanguageProf)) || (
            <Typography sx={{ color: "rgba(0, 0, 0, 0.38)" }}>
              {translate("mp.select-level")}
            </Typography>
          )
        }
      >
        <MenuItem value="" disabled>
          <Typography sx={{ color: "rgba(0, 0, 0, 0.38)" }}>
            {translate("mp.select-level")}
          </Typography>
        </MenuItem>
        {proficiencyList.map((proficiency) => (
          <MenuItem key={proficiency._id} value={proficiency._id}>
            {translate(getProficiencyId(proficiency.pfLevel.en))}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default LanguageProficiencyManager;
