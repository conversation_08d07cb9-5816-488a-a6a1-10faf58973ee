import Page from "@/components/Page";
import React, {
  ChangeEvent,
  ForwardedRef,
  PropsWithChildren,
  useEffect,
} from "react";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { waitlistValidator } from "@/api/validators";
import TextField from "@mui/material/TextField";

import {
  Button,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
} from "@mui/material";
import { LanguageType } from "@/api/mongoTypes";
import { useMutation, useQuery } from "react-query";
import { getCookies, setCookie, deleteCookie } from "cookies-next";
import { forwardRef, ReactNode } from "react";

interface Props {
  shouldShow?: boolean;
  isComplete?: boolean;
}
export type Ref = HTMLDivElement;

function SimpleWaitlistForm(
  { shouldShow = true, isComplete = false },
  ref: ForwardedRef<Ref>
) {
  const { data, error, isLoading } = useQuery("getLanguages", () =>
    fetch("/api/getLanguages").then((res) => res.json())
  );
  const [isShowing, setIsShowing] = React.useState(shouldShow);
  const [thanks, setThanks] = React.useState(isComplete);
  const addUserToWaitlist = useMutation(
    "addUserToWaitlist",
    async (data: { [key: string]: any }) => {
      const res = await fetch("/api/waitlist", {
        method: "POST",
        body: JSON.stringify(data),
        headers: { "Content-Type": "application/json" },
      });
      return await res.json();
    },
    {
      onSuccess: () => {
        setIsShowing(false);
        setThanks(true);
      },
    }
  );

  const onSubmit = (data: { [key: string]: any }) => {
    const { lname, fname, email, language } = data;
    addUserToWaitlist.mutate({ lname, fname, email, language });
  };
  const {
    handleSubmit,
    formState: { errors },
    control,
    watch,
  } = useForm({ resolver: zodResolver(waitlistValidator), mode: "all" });

  if (error) return <div>error loading languages</div>;
  if (isLoading) return <div>loading...</div>;
  if (!shouldShow) return null;
  if (thanks)
    return (
      <div className="mb-4 flex flex-col items-center">
        Thanks for joining the waitlist!
      </div>
    );
  return (
    <div ref={ref}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4 flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0">
          <Controller
            name="fname"
            control={control}
            render={({ field: { onChange, value } }) => (
              <TextField
                onChange={onChange}
                value={value || ""}
                label="Nombre/First Name*"
                className="basis-1/3"
                error={!!errors.fname}
                helperText={errors.fname?.message as string}
              />
            )}
          />
          <Controller
            name="lname"
            control={control}
            render={({ field: { onChange, value } }) => (
              <TextField
                onChange={onChange}
                value={value || ""}
                label="Apellido/Last Name*"
                className="basis-1/3"
                error={!!errors.lname}
                helperText={errors.lname?.message as string}
              />
            )}
          />
          <Controller
            name="email"
            control={control}
            render={({ field: { onChange, value } }) => (
              <TextField
                onChange={onChange}
                value={value || ""}
                label="Email*"
                className="basis-1/3"
                error={!!errors.email}
                helperText={errors.email?.message as string}
              />
            )}
          />
        </div>
        <div>
          <Controller
            name="language"
            control={control}
            render={({
              field: { onChange, value },
            }: {
              field: {
                onChange: (event: ChangeEvent<HTMLInputElement>) => void;
                value: string;
              };
            }) => (
              <FormControl
                fullWidth
                className="w-full "
                error={!!errors.language}
              >
                <InputLabel id="language-select-label">
                  Preferred language for correspondence*
                </InputLabel>
                <Select
                  onChange={onChange}
                  value={value ? value : ""}
                  label="Preferred language for correspondence*"
                  labelId="language-select-label"
                >
                  {data?.languages.map((language: LanguageType) => (
                    <MenuItem key={language._id} value={language._id}>
                      {language.name}
                    </MenuItem>
                  ))}
                </Select>
                {!!errors.language && (
                  <FormHelperText>
                    {errors.language.message as string}
                  </FormHelperText>
                )}
              </FormControl>
            )}
          />
        </div>
        <Button
          type="submit"
          variant="contained"
          color="primary"
          className="mt-4"
        >
          Submit
        </Button>
      </form>
    </div>
  );
}

export default forwardRef<Ref, Props>(SimpleWaitlistForm);
