import React, { useState } from "react";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import CardMedia from "@mui/material/CardMedia";
import IconButton from "@mui/material/IconButton";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { useSpring, animated } from "react-spring";
import Link from "next/link";
import Image from "next/image";
import SocialShareBtns from "../SocialShareBtns";
import Toast from "../toast";
import { CardActions } from "@mui/material";
import FavoriteIcon from "@mui/icons-material/Favorite";

const EventCard = ({ event }) => {
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFaveError, setIsFaveError] = useState(false);
  const favIconColor = isFavorite ? "red" : "gray";
  const handleClickFavorite = () => {
    console.log("clicked favorite");
  };
  return (
    <Card
      sx={{
        flexDirection: { xs: "column", sm: "row" },
        marginTop: 2,
        marginBottom: 2,
        boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
        transition: "all 0.3s",
        "&:hover": {
          transform: "scale(1.02)",
          boxShadow: "0 6px 8px rgba(0,0,0,0.1)",
        },
      }}
    >
      {event?.image && (
        <>
          <CardMedia sx={{ width: { xs: "100%", sm: 200 }, height: 200 }}>
            <Image
              src={event.image || ""}
              alt={event.title}
              width={200}
              height={200}
            />
          </CardMedia>
          <CardContent sx={{ padding: "1rem" }}>
            <Typography variant="h6" sx={{ marginBottom: "0.5rem" }}>
              {event.title}
            </Typography>
            <Typography variant="subtitle1" sx={{ marginBottom: "0.5rem" }}>
              {event.date} {event.time}
            </Typography>
            <Typography variant="body2">{event.description}</Typography>
          </CardContent>
          <CardActions disableSpacing>
            <IconButton aria-label="add to favorites">
              <FavoriteIcon
                style={{ color: favIconColor }}
                onClick={handleClickFavorite}
              />
            </IconButton>
            {isFaveError && (
              <Toast message={"you must be logged in to use favorites"} />
            )}
            <SocialShareBtns
              url={"https://www.npmjs.com/package/react-share"}
              img={""}
            />
          </CardActions>
        </>
      )}
    </Card>
  );
};

export default EventCard;
