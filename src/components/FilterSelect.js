import React from "react";
import TextField from "@mui/material/TextField";
import MenuItem from "@mui/material/MenuItem";

export default function MultilineTextFields({
  id,
  filterByCategory,
  categories,
  defaultValue = "",
  filterText = "",
  label = "",
  style = {},
}) {
  const handleChange = (event) => {
    filterByCategory(event.target.value);
  };

  return (
    <form className="" noValidate autoComplete="off" style={style}>
      <TextField
        id={id}
        select
        defaultValue={defaultValue}
        label={label}
        onChange={handleChange}
        helperText={filterText}
        style={style}
        sx={{ width: { xs: "300px", md: "400px" } }}
      >
        {categories.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </TextField>
    </form>
  );
}
