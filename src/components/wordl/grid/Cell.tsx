import React, { useEffect, useState } from "react";
import classnames from "classnames";
import { REVEAL_TIME_MS } from "@/wordl/constants/settings";
import { getStoredIsHighContrastMode } from "@/wordl/lib/localStorage";
import { CharStatus } from "@/wordl/lib/statuses";
import useIsDesktop from "@/hooks/useIsDesktop";

type Props = {
  value?: string;
  status?: CharStatus;
  isRevealing?: boolean;
  isCompleted?: boolean;
  position?: number;
};

export const Cell = ({
  value,
  status,
  isRevealing,
  isCompleted,
  position = 0,
}: Props) => {
  const [valueToRender, setValueToRender] = useState("");
  const [classesToRender, setClassesToRender] = useState("");
  const [animationDelayToRender, setAnimationDelayToRender] = useState(null);
  const isFilled = value && !isCompleted;
  const shouldReveal = isRevealing && isCompleted;
  const animationDelay = `${position * REVEAL_TIME_MS}ms`;
  const isHighContrast = getStoredIsHighContrastMode();
  const isDesktop = useIsDesktop();

  useEffect(() => {
    setValueToRender(value || "");
  }, [value]);

  useEffect(() => {
    const classes = classnames(
      `border-solid border-2 flex items-center justify-center mx-0.5 text-4xl font-bold rounded`,
      {
        "bg-white  border-slate-200 ": !status,
        "border-black ": value && !status,
        "absent shadowed bg-[#B2B5B7] text-white border-slate-400":
          status === "absent",
        "correct shadowed bg-orange-500 text-white border-orange-500":
          status === "correct" && isHighContrast,
        "present shadowed bg-cyan-500 text-white border-cyan-500":
          status === "present" && isHighContrast,
        "correct shadowed bg-[#ED7DA0] text-white border-green-500":
          status === "correct" && !isHighContrast,
        "present shadowed bg-[#B5DF9C] text-white border-yellow-500":
          status === "present" && !isHighContrast,
        "cell-fill-animation": isFilled,
        "cell-reveal": shouldReveal,
      }
    );
    setClassesToRender(classes);
  }, [isFilled, shouldReveal, status, isHighContrast, isDesktop, value]);

  useEffect(() => {
    animationDelayToRender && setAnimationDelayToRender(animationDelay);
  }, [animationDelay, animationDelayToRender]);

  return (
    <div
      className={classesToRender}
      style={{
        width: "3.5rem",
        height: "3.5rem",
        animationDelay: animationDelayToRender,
      }}
    >
      <div
        className="letter-container"
        style={{ animationDelay: animationDelayToRender }}
      >
        {valueToRender}
      </div>
    </div>
  );
};
