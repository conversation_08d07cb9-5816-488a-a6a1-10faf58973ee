import React from "react";
import { InformationCircleIcon } from "@heroicons/react/outline";
import { GAME_TITLE } from "@/wordl/constants/strings";

type Props = {
  setIsInfoModalOpen: (value: boolean) => void;
  setIsStatsModalOpen: (value: boolean) => void;
};

export const Navbar = ({ setIsInfoModalOpen }: Props) => {
  return (
    <div className="navbar flex justify-center items-center">
      {" "}
      <div className="navbar-content flex justify-center items-center px-5 short:h-auto">
        {" "}
        <p className="text-xl font-bold text-white mr-2">{GAME_TITLE}</p>
        <InformationCircleIcon
          className="h-6 w-6 cursor-pointer stroke-white"
          onClick={() => setIsInfoModalOpen(true)}
        />
      </div>
    </div>
  );
};
