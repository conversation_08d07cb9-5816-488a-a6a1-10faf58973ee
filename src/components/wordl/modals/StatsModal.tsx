import { ShareIcon } from "@heroicons/react/outline";
import {
  GUESS_DISTRIBUTION_TEXT,
  SHARE_TEXT,
  STATISTICS_TITLE,
} from "@/wordl//constants/strings";
import { GameStats } from "@/wordl//lib/localStorage";
import { shareStatus } from "@/wordl//lib/share";
import { Histogram } from "../stats/Histogram";
import { StatBar } from "../stats/StatBar";
import { BaseModal } from "@/components/Modals/BaseModal";

type Props = {
  isOpen: boolean;
  handleClose: () => void;
  solution: string;
  guesses: string[];
  gameStats: GameStats;
  isLatestGame: boolean;
  isGameLost: boolean;
  isGameWon: boolean;
  handleShareToClipboard: () => void;
  handleShareFailure: () => void;
  isHardMode: boolean;
  isHighContrastMode: boolean;
  numberOfGuessesMade: number;
};

export const StatsModal = ({
  isOpen,
  handleClose,
  solution,
  guesses,
  gameStats,
  isLatestGame,
  isGameLost,
  isGameWon,
  handleShareToClipboard,
  handleShareFailure,
  isHardMode,
  isHighContrastMode,
  numberOfGuessesMade,
}: Props) => {
  if (gameStats.totalGames <= 0) {
    return (
      <BaseModal
        title={STATISTICS_TITLE}
        isOpen={isOpen}
        handleClose={handleClose}
      >
        <StatBar gameStats={gameStats} />
      </BaseModal>
    );
  }

  return (
    <BaseModal
      title={isGameWon ? STATISTICS_TITLE : "¡Perdiste!"}
      isOpen={isOpen}
      handleClose={handleClose}
    >
      {isGameWon ? (
        <>
          <StatBar gameStats={gameStats} />
          <h4 className="text-lg font-medium leading-6 text-gray-900 ">
            {GUESS_DISTRIBUTION_TEXT}
          </h4>
          <Histogram
            isLatestGame={isLatestGame}
            gameStats={gameStats}
            isGameWon={isGameWon}
            numberOfGuessesMade={numberOfGuessesMade}
          />
        </>
      ) : (
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-500">
            ¡Perdiste! Inténtalo de nuevo
          </h1>
          <h2 className="text-2xl font-bold text-green-500">
            La palabra era: {solution}
          </h2>
        </div>
      )}
      {(isGameLost || isGameWon) && (
        <div className="mt-5 columns-2 items-center items-stretch justify-center text-center  sm:mt-6">
          <div className="inline-block w-full text-left"></div>
          <div>
            <button
              type="button"
              className="mt-2 inline-flex w-full items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-center text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-base"
              onClick={() => {
                shareStatus(
                  solution,
                  guesses,
                  isGameLost,
                  isHardMode,
                  isHighContrastMode,
                  handleShareToClipboard,
                  handleShareFailure
                );
              }}
            >
              <ShareIcon className="mr-2 h-6 w-6 cursor-pointer " />
              {SHARE_TEXT}
            </button>
          </div>
        </div>
      )}
    </BaseModal>
  );
};
