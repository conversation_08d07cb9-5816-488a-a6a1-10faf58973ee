import { Chip, SxProps, Theme } from "@mui/material";
import React from "react";

type TagProps = React.FC<{ text: string; sx?: SxProps<Theme> }>;

const Tag: TagProps = ({ text, sx = {} }) => {
  return (
    <Chip
      label={text}
      size="small"
      sx={{
        backgroundColor: "#D7F7F5",
        color: "#14A79C",
        fontSize: "0.7rem",
        fontWeight: { xs: "400", sm: "500" },
        marginRight: "5px",
        ...sx,
      }}
    />
  );
};

export default Tag;
