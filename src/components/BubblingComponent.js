import React from "react";
import Typography from "@mui/material/Typography";

const durationWord = 800;

const BubblingComponent = ({ strings, style = {}, permanent = true }) => {
  const bubbleLines = strings;
  const getWordsCountBeforeIndex = (lineIndex, wordIndex) => {
    let wordsCount = 0;
    for (let i = 0; i < lineIndex; i++) {
      wordsCount += bubbleLines[i].length;
    }
    wordsCount += wordIndex;
    return wordsCount;
  };

  return (
    <div>
      {bubbleLines.map((line, lineIndex) => (
        <div key={lineIndex}>
          {line.map((word, wordIndex) => (
            <Typography
              sx={{ fontSize: { xs: "30px", sm: "40px", md: "60px" } }}
              key={wordIndex}
              variant="body1"
              className="fade"
              style={{
                color: lineIndex === 0 ? "#02B199" : "#FD5E50",
                animationDelay: `${
                  getWordsCountBeforeIndex(lineIndex, wordIndex) * durationWord
                }ms`,
              }}
            >
              {word}&nbsp;
            </Typography>
          ))}
        </div>
      ))}
    </div>
  );
};

export default BubblingComponent;
