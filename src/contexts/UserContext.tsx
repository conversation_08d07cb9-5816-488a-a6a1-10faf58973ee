import React, { createContext, useContext, useEffect, useState } from "react";
import { useUser } from "@clerk/nextjs";
import axios from "axios";
import { useSnackbar } from "@/hooks/useSnackbar";
import { useRouter } from "next/router";
import __lang from "@/constant/translations";
import { handleReturnToCart, needToRedirect } from "@/utils/classes";
import { Maybe } from "@/types";
import { UserType } from "@/api/mongoTypes";
import useTranslate from "@/hooks/useTranslate";
import Cookies from "js-cookie";
import { getSiteLanguageBasedOnLearningLanguage } from "@/utils/common";
import { LEARN_COOKIE_NAME, USER_DATA_COOKIE_NAME } from "@/constant/classes";

const UserContext = createContext({
  isLoaded: false,
  dbUser: null,
  isfetching: false,
  isRetrying: false,
  user: null,
  isProfileCompeleted: true,
  setUserInfo: (user: Maybe<UserType>) => {},
  setProfileCompleted: (status: boolean) => {},
});

export const UserProvider = ({ children }) => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [isfetching, setIsfetching] = useState(true);
  const [isRetrying, setIsRetrying] = useState(true);
  const [isProfileCompeleted, setProfileCompleted] = useState(true);
  const [userInfo, setUserInfo] = useState<Maybe<UserType>>(null);
  const { setPreferredLanguage } = useTranslate();

  const { isLoaded, user } = useUser();

  const fetchUserInfo = async (retryCount = 0) => {
    try {
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/user/get-user`,
        {
          clerkId: user.id,
        }
      );

      if (data?.data) {
        setUserInfo(data.data);
        localStorage.setItem("user", JSON.stringify(data.data));
        Cookies.set(USER_DATA_COOKIE_NAME, JSON.stringify(data.data));
        const isProfileCompeleted = data?.data?.profileCreated;
        const completeItLater = data?.data?.completeLater;

        const languageOfInterest =
          data?.data?.languageOfInterest?.nameInEnglish;

        if (languageOfInterest) {
          Cookies.set(LEARN_COOKIE_NAME, languageOfInterest, { expires: 365 });
          const prefLang =
            getSiteLanguageBasedOnLearningLanguage(languageOfInterest);
          setPreferredLanguage(prefLang);
        }

        const profileCompleted =
          completeItLater === false && isProfileCompeleted;

        setProfileCompleted(profileCompleted);

        if (completeItLater || isProfileCompeleted) {
          const open = needToRedirect();
          if (open) {
            const wasRedirected = localStorage.getItem("was_redirected");
            if (!wasRedirected) {
              localStorage.setItem("was_redirected", "true");
              handleReturnToCart(router);
              showSnackbar("Please complete your transaction", {
                type: "info",
              });
            }
          }
        }

        setIsRetrying(false);
        setIsfetching(false);
      } else if (retryCount < 3) {
        setIsRetrying(true);
        setTimeout(() => fetchUserInfo(retryCount + 1), 2000);
      } else {
        setIsRetrying(false);
        setIsfetching(false);
      }
    } catch (error) {
      if (retryCount < 3) {
        setIsRetrying(true);
        setTimeout(() => fetchUserInfo(retryCount + 1), 2000);
      } else {
        setIsRetrying(false);
        setIsfetching(false);
      }
    }
  };

  const handleClearStorage = () => {
    // localStorage.removeItem("CART");
    // localStorage.removeItem("BUY_CLASS");
    // localStorage.removeItem("BUY_EVENT");
    localStorage.removeItem("CART_COUNT");
    localStorage.removeItem("user");
    localStorage.removeItem("was_redirected");
  };

  useEffect(() => {
    if (user?.id) {
      setIsRetrying(true);
      fetchUserInfo();
    } else {
      setIsfetching(false);
      setIsRetrying(false);
      setUserInfo(null);
      handleClearStorage();
    }
  }, [user]);

  const value = {
    isLoaded,
    dbUser: userInfo,
    isfetching,
    isRetrying,
    user,
    isProfileCompeleted,
    setProfileCompleted,
    setUserInfo,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUserContext = () => useContext(UserContext);
