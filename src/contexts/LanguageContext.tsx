import { LEARN_COOKIE_NAME } from "@/constant/classes";
import React, { createContext, useEffect } from "react";
import Cookies from "js-cookie";
import { getSiteLanguageBasedOnLearningLanguage } from "@/utils/common";

export const languageContext = createContext({
  language: "en",
  setLanguage: (lang: string) => {},
});

type LanguageContextProps = React.FC<{
  children: React.ReactNode;
}>;
const LanguageContext: LanguageContextProps = ({ children }) => {
  const [language, setLanguage] = React.useState("en");

  const setLanguageOption = (lang: string) => {
    setLanguage(lang);
  };

  const getPreferredLanguage = () => {
    const lang = Cookies.get(LEARN_COOKIE_NAME);
    return getSiteLanguageBasedOnLearningLanguage(lang);
  };

  useEffect(() => {
    setLanguageOption(getPreferredLanguage());
  }, []);

  return (
    <languageContext.Provider
      value={{ language, setLanguage: setLanguageOption }}
    >
      {children}
    </languageContext.Provider>
  );
};

export default LanguageContext;
