import React, { useState, useEffect } from "react";
import Head from "next/head";
import {
  Box,
  Container,
  TextField,
  Grid,
  Paper,
  styled,
  CircularProgress,
} from "@mui/material";
import { useUserContext } from "../../contexts/UserContext";
import CustomButton from "@/components/CustomButton";
import ManageProfileSkeleton from "@/components/ManageProfileSkeleton";
import {
  CountryType,
  GoalType,
  InterestType,
  LanguageProficiencyType,
  LanguageType,
} from "@/api/mongoTypes";
import axios from "axios";
import { useSnackbar } from "@/hooks/useSnackbar";
import { INTEREST_TYPES } from "@/constant/Enums";
import PhoneNumberInput from "@/components/PhoneNumberInput";
import { isPossiblePhoneNumber } from "react-phone-number-input";
import { langugaeStateTypes } from "@/types";
import GoalItem from "@/components/Profile/manage-profile/GoalItem";
import { useRouter } from "next/router";
import ManageHeader from "@/components/Profile/manage-profile/ManageHeader";
import SelectCountry from "@/components/Profile/SelectCountry";
import ProfileImageSection from "@/components/Profile/manage-profile/ProfileImageSection";
import InformationEditGroup from "@/components/Profile/manage-profile/InformationEditGroup";
import InterestSelection from "@/components/Profile/manage-profile/InterestSelection";
import LanguageEdit from "@/components/Profile/manage-profile/LanguageEdit";
import useTranslate from "@/hooks/useTranslate";
import { getLanguageName } from "@/utils/common";
import Cookies from "js-cookie";
import { getSiteLanguageBasedOnLearningLanguage } from "@/utils/common";
import { LEARN_COOKIE_NAME } from "@/constant/classes";

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(10),
  marginTop: theme.spacing(10),
  borderRadius: "20px",
  boxShadow: "none",
  border: "1px solid #D3D3D3",
  [theme.breakpoints.down("sm")]: {
    padding: theme.spacing(2),
    marginTop: theme.spacing(2),
    border: "none",
  },
}));

const ManageProfile = ({
  isStudentDashboard = false,
  studentInfo,
  setStudentInfo,
}) => {
  const { dbUser, setUserInfo } = useUserContext();
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(true);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [firstName, setFirstName] = useState("John");
  const [lastName, setLastName] = useState("Doe");
  const [country, setCountry] = useState("");
  const [studyLanguage, setStudyLanguage] = useState("");
  const [studyLanguageProficiency, setStudyLanguageProficiency] = useState("");
  const [selectedGoals, setSelectedGoals] = useState([]);
  const [whatsappNo, setWhatsappNo] = useState("");
  const [selectionData, setSelectionData] = useState<{
    languages?: LanguageType[];
    interests?: InterestType[];
    goals?: GoalType[];
    countries?: CountryType[];
    proficiencies?: LanguageProficiencyType[];
  }>({});
  const [availableInterests, setAvailableInterests] = useState<InterestType[]>(
    []
  );
  const [interestOptions, setInterestOptions] = useState<InterestType[]>([]);
  const [lovedInterests, setLovedInterests] = useState<InterestType[]>([]);
  const [neutralInterests, setNeutralInterests] = useState<InterestType[]>([]);
  const [hatedInterests, setHatedInterests] = useState<InterestType[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [languages, setLanguages] = useState<langugaeStateTypes>([]);
  const router = useRouter();
  const [level, setLevel] = useState("");
  const { translate, setPreferredLanguage } = useTranslate();

  const user = studentInfo ? studentInfo : dbUser;
  const setUser = setStudentInfo ? setStudentInfo : setUserInfo;

  const updateSelectedGoal = (id) => {
    if (selectedGoals.includes(id))
      setSelectedGoals(selectedGoals.filter((_id) => _id != id));
    else setSelectedGoals(selectedGoals.concat(id));
  };

  console.log("studyLanguage", studyLanguage);

  useEffect(() => {
    const loadProfileData = async () => {
      // Load selection data
      try {
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/utils/get-profile-options`
        );
        if (data?.data) {
          setSelectionData(data.data);
          if (Array.isArray(data.data?.interests)) {
            setInterestOptions(data.data.interests); // Set interests
            if (dbUser?.interest) {
              // Classify user's already selected interest items
              initializeUserInterests(dbUser.interest, data.data.interests);
            }
          }
        }
      } catch (error) {
        console.error("Error loading profile data:", error);
        setIsLoading(false);
      }

      // Load user profile information.
      setIsLoading(false);
    };

    loadProfileData();
  }, []);

  const initializeUserInterests = (interests, interestList: InterestType[]) => {
    const list = {
      loved: [],
      neutral: [],
      hate: [],
      default: [],
    };

    interestList
      .map((item) => {
        const found = interests.find(({ id }) => item._id == id);
        if (!found) {
          list.default.push(item);
          return null;
        }

        const { _id, image, name } = item;
        return { _id, image, name, rating: found.rating };
      })

      .filter((item) => !!item)
      .forEach((item) => {
        switch (item.rating) {
          case INTEREST_TYPES.LOVE:
            list.loved.push(item);
            break;
          case INTEREST_TYPES.NEUTRAL:
            list.neutral.push(item);
            break;
          case INTEREST_TYPES.HATE:
            list.hate.push(item);
            break;
        }

        setAvailableInterests(list.default);
        setLovedInterests(list.loved);
        setNeutralInterests(list.neutral);
        setHatedInterests(list.hate);
      });
  };

  // Classify the users already selected interest options.
  useEffect(() => {
    if (Array.isArray(user?.interest) && interestOptions.length > 0) {
      initializeUserInterests(user.interest, interestOptions);
    }
  }, [user, interestOptions]);

  useEffect(() => {
    setFirstName(user?.firstName || "");
    setLastName(user?.lastName || "");
    setCountry(user?.countryOfResidence || "");
    setStudyLanguage(user?.languageOfInterest || "");
    setSelectedGoals(user?.goals || []);
    setWhatsappNo(user?.whatsAppNo || "");
    setLevel(user?.level || "");
    setStudyLanguageProficiency(user?.proficiencyOfLanguageOfInterest || "");
    if (user && !isStudentDashboard) {
      const interests = user?.interest ?? [];
      if (interests.length === 0) {
        showSnackbar(translate("mp.select-interests"), {
          type: "error",
        });
        router.push("/profile");
      }
      if (user?.completeLater) {
        showSnackbar(translate("mp.complete-profile"), {
          type: "warning",
        });
        router.push("/profile");
      }
    }
  }, [user, isStudentDashboard]);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImage(e.target?.result as string);
      };
      reader.readAsDataURL(event.target.files[0]);
    }
  };

  const handlePreferredLanguage = () => {
    const languageName = (() => {
      if (studyLanguage) {
        if (typeof studyLanguage === "string") {
          const selectedLanguage = selectionData.languages.find(
            (f) => f._id === studyLanguage
          );
          return selectedLanguage.nameInEnglish;
        }
        if (
          studyLanguage &&
          typeof studyLanguage !== "string" &&
          "nameInEnglish" in studyLanguage
        ) {
          let studyName = studyLanguage as any;
          const nameOfStudyLanguage = String(studyName?.nameInEnglish) as any;
          return nameOfStudyLanguage;
        }
      }
      return null;
    })();
    if (languageName) {
      Cookies.set(LEARN_COOKIE_NAME, languageName, { expires: 365 });
      const prefLang = getSiteLanguageBasedOnLearningLanguage(languageName);
      setPreferredLanguage(prefLang);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);

    // Validate the phone number if provided
    if (whatsappNo && !isPossiblePhoneNumber(whatsappNo)) {
      showSnackbar(translate("mp.valid-whatsapp"), {
        type: "error",
      });
      setIsSaving(false);
      return;
    }

    const payload: Record<string, any> = {
      id: user?._id,
      profileCreated: user?.profileCreated,
      completeLater: user?.completeLater,
    };

    if (firstName) payload.firstName = firstName;
    if (lastName) payload.lastName = lastName;
    if (country) payload.location = country;
    if (level) payload.level = level;
    if (whatsappNo) payload.whatsAppNo = whatsappNo;
    if (studyLanguage) payload.languageOfInterest = studyLanguage;
    if (Array.isArray(selectedGoals) && selectedGoals.length > 0)
      payload.goals = selectedGoals;

    // Process selected proficiency languages...
    if (languages.length > 0) {
      payload.languages = languages.map((m) => [
        {
          languageId: m.language._id,
          languageProficiencyId: m.proficiency._id,
        },
      ]);
    }

    if (studyLanguageProficiency) {
      payload.proficiencyOfLanguageOfInterest = studyLanguageProficiency;
    }

    const interests = [];
    // create Interest payload
    lovedInterests.forEach(({ _id: id }) =>
      interests.push({ id, rating: INTEREST_TYPES.LOVE })
    );

    neutralInterests.forEach(({ _id: id }) =>
      interests.push({ id, rating: INTEREST_TYPES.NEUTRAL })
    );

    hatedInterests.forEach(({ _id: id }) =>
      interests.push({ id, rating: INTEREST_TYPES.HATE })
    );

    if (interests.length > 0) payload.interests = interests;

    try {
      const updateStatus = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/user/update`,
        payload
      );
      if (updateStatus.status == 200) {
        setUser(updateStatus?.data?.data);
        setIsSaving(false);
        if (isStudentDashboard) {
          showSnackbar(translate("mp.profile-info-success"), {
            type: "success",
          });
          // router.back();
        } else {
          showSnackbar(translate("mp.profile-info-updated"), {
            type: "success",
          });
          handlePreferredLanguage();
          router.push("/profile/dashboard");
        }
      }
    } catch (error) {
      // console.error(`Error: Unable to update user's profile. Reason: ${error}`);
      // TODO: Send error to Bug report
      showSnackbar(translate("mp.unexpected"), {
        type: "warning",
      });
    }
  };

  if (isLoading) {
    return <ManageProfileSkeleton />;
  }

  return (
    <>
      <Head>
        <title>{translate("mp.manage-profile")}</title>
      </Head>
      <Container maxWidth="md">
        <ManageHeader goBack={isStudentDashboard} />
        <StyledPaper>
          {/** Profile Picture actions UI */}
          {/* <ProfileImageSection profileImage={profileImage} /> */}

          <StyledPaper>
            {/** Name Group */}
            <InformationEditGroup index="1" title={translate("mp.name")}>
              <Grid container spacing={{ xs: 10, sm: 2 }}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label={translate("mp.first-name")}
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    required
                    sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label={translate("mp.last-name")}
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    required
                    sx={{ "& .MuiOutlinedInput-root": { borderRadius: "4px" } }}
                  />
                </Grid>
              </Grid>
            </InformationEditGroup>

            {/** Location Group */}
            <InformationEditGroup
              index="2"
              title={translate("mp.location")}
              isPending={!country}
            >
              <SelectCountry
                country={country}
                setCountry={setCountry}
                isManageProfile
                countriesList={selectionData.countries}
              />
            </InformationEditGroup>

            <LanguageEdit
              studyLanguage={studyLanguage}
              studyLanguageProficiency={studyLanguageProficiency}
              setStudyLanguage={setStudyLanguage}
              setStudyLanguageProficiency={setStudyLanguageProficiency}
              selectionData={selectionData}
              dbUser={user}
              languages={languages}
              setLanguages={setLanguages}
              isStudentDashboard={isStudentDashboard}
              level={level}
              setLevel={setLevel}
            />

            {/** Goals Selection Group */}
            <InformationEditGroup
              index="5"
              title={translate("dash.goals")}
              isPending={!selectedGoals.length}
            >
              <>
                <Grid container margin={0}>
                  {selectionData.goals.map(({ _id: id, image, name }) => (
                    <Grid item key={id}>
                      <GoalItem
                        id={id}
                        name={name}
                        image={image}
                        checked={selectedGoals.includes(id)}
                        setGoal={updateSelectedGoal}
                      />
                    </Grid>
                  ))}
                </Grid>
              </>
            </InformationEditGroup>

            {/** Interest Selection Group */}
            <InterestSelection
              lovedInterests={lovedInterests}
              neutralInterests={neutralInterests}
              hatedInterests={hatedInterests}
              availableInterests={availableInterests}
              setAvailableInterests={setAvailableInterests}
              setLovedInterests={setLovedInterests}
              setNeutralInterests={setNeutralInterests}
              setHatedInterests={setHatedInterests}
            />
            {/** Contact Info Section */}
            <InformationEditGroup
              index="7"
              title={translate("mp.contact-info")}
              isPending={!whatsappNo}
            >
              <>
                <PhoneNumberInput
                  value={whatsappNo}
                  onChange={setWhatsappNo}
                  label={translate("mp.whatsapp-no")}
                  placeholder={translate("mp.enter-whatsapp-no")}
                />
              </>
            </InformationEditGroup>
            {/** Edit Cancel or Save action */}
            <Box sx={{ display: "flex", gap: 2, justifyContent: "center" }}>
              <Grid
                container
                spacing={{ xs: 6, md: 10 }}
                justifyContent="center"
                sx={{ width: "100%" }}
              >
                <Grid
                  item
                  xs={6}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  <CustomButton
                    text={translate("mp.cancel")}
                    sx={{ width: "100%" }}
                  />
                </Grid>
                <Grid
                  item
                  xs={6}
                  sx={{ display: "flex", justifyContent: "center" }}
                >
                  {/* <Button variant='outlined' fullWidth loading>
                  Save
                </Button> */}
                  <CustomButton
                    text={translate("mp.save")}
                    disabled={isSaving}
                    endIcon={
                      isSaving ? (
                        <CircularProgress
                          sx={{ ml: 3, color: "#fff" }}
                          size={20}
                        />
                      ) : null
                    }
                    colortype="secondary"
                    onClick={handleSave}
                    sx={{ width: "100%" }}
                  />
                </Grid>
              </Grid>
            </Box>
          </StyledPaper>
        </StyledPaper>
      </Container>
    </>
  );
};

export default ManageProfile;
