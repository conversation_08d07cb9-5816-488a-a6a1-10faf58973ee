import "../styles/globals.css";
import "react-phone-number-input/style.css";
import { ClerkProvider } from "@clerk/nextjs";
import { ThemeProvider } from "@mui/material";
import { CssBaseline } from "@mui/material/";
import { theme } from "../styles/theme";
import createEmotionCache from "../utils/createEmotionCache";
import { CacheProvider } from "@emotion/react";
import { UserProvider, useUserContext } from "../contexts/UserContext";
import Header from "../components/header/Header";
import Footer from "../components/Footer";
import "../styles/fonts";
import "./index.css";
import { SnackbarProvider } from "@/contexts/SnackbarContext";
import { CartCountProvider } from "@/contexts/CartCountContext";
import InitialIdentificationModal from "@/components/home/<USER>";
import LanguageContext from "@/contexts/LanguageContext";
import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { LEARN_COOKIE_NAME } from "@/constant/classes";
import { getSiteLanguageBasedOnLearningLanguage } from "@/utils/common";
import useTranslate from "@/hooks/useTranslate";
import { enUS, esES } from "@clerk/localizations";

const clientSideEmotionCache = createEmotionCache();

function MyApp({
  Component,
  emotionCache = clientSideEmotionCache,
  pageProps,
}) {
  const { dbUser, user, isLoaded } = useUserContext();
  const [localization, setLocalization] = useState({});
  const { setPreferredLanguage } = useTranslate();

  useEffect(() => {
    const getLocalization = (lang) => {
      if (lang === "en") {
        return enUS;
      }
      return esES;
    };

    if (!user) {
      const lang = Cookies.get(LEARN_COOKIE_NAME);
      if (lang) {
        const siteLang = getSiteLanguageBasedOnLearningLanguage(lang);
        console.log({
          lang,
          siteLang,
          user,
          isLoaded,
        });
        setPreferredLanguage(siteLang);
        setLocalization(getLocalization(siteLang));
      }
    }
    if (dbUser) {
      const lang = dbUser.languageOfInterest?.nameInEnglish;
      if (lang) {
        const siteLang = getSiteLanguageBasedOnLearningLanguage(lang);
        console.log({
          lang,
          siteLang,
          dbUser,
        });
        setPreferredLanguage(siteLang);
        setLocalization(getLocalization(siteLang));
      }
    }
  }, [user, isLoaded, dbUser]);

  return (
    <ClerkProvider localization={localization}>
      <CacheProvider value={emotionCache}>
        <ThemeProvider theme={theme}>
          <SnackbarProvider>
            <UserProvider>
              <LanguageContext>
                <CartCountProvider>
                  <CssBaseline />
                  <main>
                    <Header />
                    <Component {...pageProps} />
                    <InitialIdentificationModal />
                  </main>
                  <Footer />
                </CartCountProvider>
              </LanguageContext>
            </UserProvider>
          </SnackbarProvider>
        </ThemeProvider>
      </CacheProvider>
    </ClerkProvider>
  );
}

export default MyApp;
