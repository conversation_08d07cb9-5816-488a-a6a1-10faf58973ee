import React, { useEffect, useState, Suspense } from "react";
import Head from "next/head";
const NodeChartDynamic = React.lazy(() =>
  import("@/components/Graph/3dWordChart")
);

export default function ThreeDTextChart() {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    setIsLoaded(true);
  }, []);
  return (
    <>
      <Head>
        <title>3D-Text</title>
      </Head>
      {isLoaded && (
        <Suspense fallback={<div>Loading...</div>}>
          <NodeChartDynamic />
        </Suspense>
      )}
    </>
  );
}
