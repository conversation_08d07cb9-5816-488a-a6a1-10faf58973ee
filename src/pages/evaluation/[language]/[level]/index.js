import React, { useState } from "react";
import { Container, Typography } from "@mui/material";
import MultipleChoiceSection from "../../../../components/Evaluation/MultipleChoiceSection";
import Head from "next/head";
export default function EvaluationPage({ language, level, data }) {
  const [curStage, setCurStage] = useState(0);
  const [outcome, setOutcome] = useState([]);
  const questions = data[curStage].questions;

  const saveToUser = (outcome) => {
    const date = new Date().toISOString();
    const outcomeHistory = {
      date,
      language,
      level,
      outcome,
      testVersion: "1.0",
    };
    console.log("save here", outcomeHistory);
  };

  const handleNextStage = (stageOutcome) => {
    if (curStage >= data.length - 1) {
      handleTestComplete([...outcome, stageOutcome]);
    } else {
      setOutcome((outcome) => [...outcome, stageOutcome]);
      setCurStage(curStage + 1);
    }
  };

  const handleTestComplete = (outcome) => {
    saveToUser(outcome);
  };

  return (
    <>
      <Head>
        <title>{language}</title>
      </Head>{" "}
      <Container
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          mt: { xs: "40px", md: "50px" },
          gap: 2,
          p: 2,
        }}
      >
        <Typography variant="h2" sx={{ fontSize: "2.75rem" }}>
          Evaluation
        </Typography>

        <MultipleChoiceSection
          key={curStage}
          data={questions}
          handleNextStage={handleNextStage}
        />
      </Container>
    </>
  );
}

import fs from "fs";
import path from "path";

export async function getStaticPaths() {
  const paths = [
    { params: { language: "english", level: "beginner" } },
    { params: { language: "english", level: "intermediate" } },
    { params: { language: "english", level: "advanced" } },
    { params: { language: "spanish", level: "beginner" } },
    { params: { language: "spanish", level: "intermediate" } },
    { params: { language: "spanish", level: "advanced" } },
  ];

  return { paths, fallback: false };
}

export async function getStaticProps({ params }) {
  const { language, level } = params;
  const filePath = path.resolve(
    "src",
    "data",
    "evaluations",
    `${language}-${level}.json`
  );
  const jsonData = await fs.promises.readFile(filePath, "utf8");
  const data = JSON.parse(jsonData);

  console.log("File path:", filePath);

  return {
    props: {
      language,
      level,
      data,
    },
  };
}
