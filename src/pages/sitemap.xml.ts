import axios from "axios";
import { GetServerSideProps } from "next";

const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;

const publicUrls = [
  "/",
  "/classes",
  "/classes/online-clubs",
  "/classes/clases-lineares",
  "/classes/experiencias-libres",
  "/classes/online-clases",
  "/about-us",
  "/about-us/why-patito-feo",
  "/about-us/team",
  "/about-us/testimonials",
];

const Sitemap = () => {};

const getLocalRoutes = () => {
  const domain = baseUrl.replace(/\/$/, "");
  return publicUrls.map((m) => `${domain}${m}`);
};

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
  const isProduction = process.env.NEXT_PUBLIC_ENVIRONMENT === "production";

  if (!isProduction) {
    res.setHeader("Content-Type", "application/xml");
    res.write("");
    res.end();
    return {
      props: {},
    };
  }

  const localRoutes = getLocalRoutes();
  console.log("localRoutes", localRoutes);

  let clubsAndEvents = [];

  try {
    const { data } = await axios.get(`${baseUrl}api/utils/get-sitemap-data`);
    clubsAndEvents = data?.data ?? [];
  } catch (error) {
    console.error("Something went wrong in get-sitemap-data", error);
  }

  console.log("clubsAndEvents", clubsAndEvents);

  const routesList = [...localRoutes, ...clubsAndEvents];

  const sitemap = `
  <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    ${routesList
      .map(
        (url) => `
      <url>
        <loc>${url}</loc>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
      </url>
    `
      )
      .join("\n")}
  </urlset>
`;

  // 3. Set headers and write the response
  res.setHeader("Content-Type", "application/xml");
  res.write(sitemap);
  res.end();
  return {
    props: {},
  };
};

export default Sitemap;
