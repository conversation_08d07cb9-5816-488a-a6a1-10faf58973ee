import React, { useEffect, useState } from "react";
import ProfileComponent from "@/components/Profile/ProfileComponent";
import InterestsComponent, {
  InterestCardStyle,
  InterestImgCont,
} from "@/components/Profile/InterestSection";
import { useUserContext } from "../../contexts/UserContext";
import axios from "axios";
import GoalsSection from "@/components/Profile/GoalsSection";
import LanguageSelection from "@/components/Profile/CompleteProfile/LanguageSelection";
import LocationSelection from "@/components/Profile/LocationSelection";
import {
  interestStateTypes,
  InterestTypeWithpreference,
  langugaeStateTypes,
  StudyingType,
} from "@/types";
import { useSnackbar } from "@/hooks/useSnackbar";
import WelcomeSection from "@/components/Profile/CompleteProfile/WelcomeSection";
import Congratulations from "@/components/Profile/Congratulations";
import Image from "next/image";
import { interest } from "data/interests";
import { Card, Container } from "@mui/material";
import { useRouter } from "next/router";
import NameSection from "@/components/Profile/NameSection";
import Head from "next/head";
import { needToRedirect } from "@/utils/classes";
import RedirectToCartModal from "@/components/Profile/RedirectToCartModal";
import {
  CountryType,
  GoalType,
  InterestType,
  LanguageType,
} from "@/api/mongoTypes";
import ProfileSkeleton from "@/components/Profile/ProfileSkeleton";
import useTranslate from "@/hooks/useTranslate";

//TODO: are you an individual or a business?
const ProfilePage = () => {
  const { showSnackbar } = useSnackbar();
  const { user, isLoaded, dbUser, setUserInfo, setProfileCompleted } =
    useUserContext();
  const [interests, setInterests] = useState<interestStateTypes>([]);
  const [selectedInterests, setSelectedInterests] = useState([]);
  const [location, setLocation] = useState<string>("");
  const [languages, setLanguages] = useState<langugaeStateTypes>([]);
  const [goals, setGoals] = useState<string[]>([]);
  const [stageIx, setStageIx] = useState(0);
  const [isUpdating, setIsUpdating] = useState(false);
  const router = useRouter();
  const completeProfile = router.query?.completeProfile === "true";
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [selectionData, setSelectionData] = useState<{
    languages?: LanguageType[];
    interests?: InterestType[];
    goals?: GoalType[];
    countries?: CountryType[];
  }>({});
  const [rediectToCart, setRedirectToCart] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [allowPageProgress, setAllowPageProgress] = useState(true);
  const [studying, setStudying] = useState<StudyingType>({
    language: "",
    proficiency: "",
  });
  const { translate } = useTranslate();

  const updateSelectedInterests = (interest: InterestTypeWithpreference) => {
    setSelectedInterests([...selectedInterests, interest]);
  };

  const checkIfNeedToRedirect = () => {
    try {
      const open = needToRedirect();
      setRedirectToCart(open);
    } catch (error) {
      setRedirectToCart(false);
      console.error(
        "Something went wrong in checkIfNeedToRedirect due to ",
        error
      );
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/utils/get-profile-options`
      );
      if (data?.data) {
        setSelectionData(data.data);
        setPageLoading(false);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (dbUser && !pageLoading) {
      setGoals(dbUser?.goals);
      setLocation(dbUser?.countryOfResidence);
      setLastName(dbUser.lastName);
      setFirstName(dbUser.firstName);
      setStudying({
        language: dbUser.languageOfInterest,
        proficiency: dbUser.proficiencyOfLanguageOfInterest,
      });
      // setSelectedInterests(dbUser?.interest);
      // setLanguages(dbUser?.languages);

      checkIfNeedToRedirect();

      const totalInterests = selectionData.interests.length;

      const stageIndex = (() => {
        if (!dbUser?.firstName || !dbUser?.lastName) {
          return 1;
        }
        if (!dbUser?.countryOfResidence) {
          return 2;
        }
        if (
          dbUser?.languages?.length === 0 ||
          !dbUser?.languageOfInterest ||
          !dbUser?.proficiencyOfLanguageOfInterest
        ) {
          return 3;
        }
        if (dbUser?.goals?.length === 0) {
          return 4;
        }
        if (
          dbUser?.interest?.length === 0 ||
          dbUser?.interest?.length < totalInterests
        ) {
          return 5;
        }
        return 6;
      })();

      setStageIx(stageIndex);
    }
  }, [dbUser, pageLoading, selectionData]);

  const handleSubmit = async (completeItLater = false, newInterest = null) => {
    try {
      setIsUpdating(true);
      const updatedInterests = (() => {
        if (newInterest?._id) {
          return [...selectedInterests, newInterest];
        } else {
          return selectedInterests;
        }
      })();
      const profileCreated = (() => {
        if (updatedInterests.length === 0 && dbUser?.interest?.length === 0) {
          return false;
        }
        if (languages.length === 0 && dbUser?.languages?.length === 0) {
          return false;
        }
        if (!goals && !dbUser?.goal) {
          return false;
        }
        if (!location && !dbUser?.countryOfResidence) {
          return false;
        }
        return true;
      })();
      const updatedUser = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/user/update`,
        {
          firstName,
          lastName,
          interests:
            stageIx === 5 && completeItLater
              ? []
              : updatedInterests.map((m) => {
                  return {
                    id: m._id,
                    rating: m.preference,
                  };
                }),
          location: location,
          languages: languages.map((m) => [
            {
              languageId: m.language._id,
              languageProficiencyId: m.proficiency._id,
            },
          ]),
          goals,
          completeLater: completeItLater,
          id: dbUser?._id,
          profileCreated,
          languageOfInterest: studying.language,
          proficiencyOfLanguageOfInterest: studying.proficiency,
        }
      );
      const isSucceeded = updatedUser.status === 200;
      if (isSucceeded) {
        if (updatedUser?.data?.data) {
          setUserInfo(updatedUser?.data?.data);
          if (!completeItLater) {
            setProfileCompleted(true);
          }
        }
        if (completeItLater || !profileCreated) {
          showSnackbar(translate("kyc.user-updated-success"), {
            type: "success",
          });
          router.push("/");
        } else {
          setStageIx(7);
          setAllowPageProgress(false);
          showSnackbar(translate("kyc.user-updated-success"), {
            type: "success",
          });
        }
      } else {
        showSnackbar(translate("kyc.user-updated-failure"), {
          type: "error",
        });
      }
      setIsUpdating(false);
    } catch (error) {
      setIsUpdating(false);
      showSnackbar(translate("kyc.user-updated-failure"), {
        type: "error",
      });
    }
  };

  useEffect(() => {
    if (completeProfile) {
      if (completeProfile) {
        setStageIx(1);
      } else {
        setStageIx(0);
      }
    }
  }, [completeProfile]);

  const stages = [
    <WelcomeSection key="welcome" stageIx={stageIx} setStageIx={setStageIx} />,
    <NameSection
      stageIx={stageIx}
      setStageIx={setStageIx}
      key="name"
      firstName={firstName}
      setFirstName={setFirstName}
      lastName={lastName}
      setLastName={setLastName}
    />,
    <LocationSelection
      key="location"
      location={location}
      setLocation={setLocation}
      stageIx={stageIx}
      data={selectionData.countries}
      setStageIx={setStageIx}
    />,
    <LanguageSelection
      key="languages"
      languages={languages}
      setLanguages={setLanguages}
      stageIx={stageIx}
      setStageIx={setStageIx}
      userId={dbUser?._id}
      completeProfile={completeProfile}
      studying={studying}
      setStudying={setStudying}
    />,
    <GoalsSection
      key="goals"
      goals={goals}
      stageIx={stageIx}
      data={selectionData.goals}
      setStageIx={setStageIx}
      setGoals={setGoals}
    />,
    <InterestsComponent
      key="interests"
      interests={interests}
      setInterests={setInterests}
      stageIx={stageIx}
      setSelectedInterests={updateSelectedInterests}
      selectedInterests={selectedInterests}
      setStageIx={setStageIx}
      handleSubmit={handleSubmit}
      isUpdating={isUpdating}
    />,
    <Congratulations key="congratulations" />,
  ];

  if (pageLoading) {
    return <ProfileSkeleton />;
  }

  return (
    <>
      <Head>
        <title>Profile</title>
      </Head>
      <ProfileComponent stageIx={stageIx} setStageIx={setStageIx}>
        {stages}
      </ProfileComponent>
      <PreloadInterestsImages />
      <RedirectToCartModal
        router={router}
        open={rediectToCart}
        setOpen={setRedirectToCart}
      />
    </>
  );
};

const PreloadInterestsImages = () => {
  return (
    <span style={{ position: "absolute", left: -100000 }}>
      {interest?.map((subject, i) => (
        <Card sx={InterestCardStyle} key={subject?.name}>
          <Container sx={InterestImgCont}>
            <Image
              src={subject?.image}
              alt={`Subject ${i + 1}`}
              key={subject?.name}
              quality={75}
              objectFit="cover"
              priority
              fill
            />
          </Container>
        </Card>
      ))}
    </span>
  );
};

export default ProfilePage;
