import { CartType } from "@/api/mongoTypes";
import PageHeaderAndContainer from "@/components/PageHeaderAndContainer";
import EmptyList from "@/components/Profile/EmptyInfo/EmptyList";
import MyPurchasesLoading from "@/components/Profile/mypurchases/MyPurchasesLoading";
import PurchasesHeader from "@/components/Profile/mypurchases/PurchasesHeader";
import PurchaseCard from "@/components/Profile/PurchaseCard";
import { useUserContext } from "@/contexts/UserContext";
import useDebounce from "@/hooks/useDebounce";
import useTranslate from "@/hooks/useTranslate";
import { purchaseDetailsType } from "@/types";
import { getEmptyPurchaseTitleAndDes } from "@/utils/dashboard";
import axiosInstance from "@/utils/interceptor";
import { Box } from "@mui/material";
import React, { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

const LIMIT = 10;

const Mypurchases = () => {
  const [data, setData] = useState<purchaseDetailsType[]>([]);
  const [skip, setSkip] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const [search, setSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [sort, setSort] = useState("asc");
  const [classesSelected, setClassesSelected] = useState([]);
  const { dbUser } = useUserContext();
  const userId = dbUser?._id;
  const { translate } = useTranslate();

  const fetchMyPurchases = async ({
    skip,
    sort,
    classesSelected,
    search,
    userId,
  }) => {
    try {
      if (skip === 0) {
        setIsLoading(true);
        setData([]);
      }
      const { data: respData } = await axiosInstance.get(
        "payment/purchases/bought-list",
        {
          params: {
            skip: skip,
            limit: LIMIT,
            isAscending: sort !== "asc",
            filters: classesSelected.join(","),
            search,
            isMyClassesOnly: true,
          },
          headers: {
            userid: userId,
          },
        }
      );
      if (respData.data && respData.success) {
        setData((prev) => [...prev, ...respData.data]);
        setSkip(+skip + LIMIT);
        setHasMore(+respData.data.length >= LIMIT);
        // setHasMore(+respData.nextSkipCount >= LIMIT);
      } else {
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      setData([]);
    }
  };

  const debouncedSearchTerm = useDebounce(search, 1000);

  const handleSearch = () => {
    fetchMyPurchases({
      skip: 0,
      sort,
      classesSelected,
      search: debouncedSearchTerm,
      userId,
    });
  };

  useEffect(() => {
    if (userId) {
      fetchMyPurchases({
        skip: 0,
        sort,
        classesSelected,
        search: debouncedSearchTerm,
        userId,
      });
    }
  }, [debouncedSearchTerm, sort, classesSelected, userId]);

  const { title, description } = getEmptyPurchaseTitleAndDes();

  return (
    <PageHeaderAndContainer title="My Purchases">
      <PurchasesHeader
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        sort={sort}
        setSort={setSort}
        classesSelected={classesSelected}
        setClassesSelected={setClassesSelected}
        search={search}
        setSearch={setSearch}
        handleSearch={handleSearch}
        isLoading={isLoading}
      />
      {data.length === 0 && !isLoading ? (
        <EmptyList
          isPurchase
          title={translate(title)}
          description={translate(description)}
        />
      ) : isLoading ? (
        <MyPurchasesLoading />
      ) : (
        <InfiniteScroll
          style={{
            width: "100%",
          }}
          dataLength={data.length}
          next={() =>
            fetchMyPurchases({ skip, sort, classesSelected, search, userId })
          }
          hasMore={hasMore}
          loader={<p style={{ textAlign: "center" }}>Loading...</p>}
          endMessage={
            <p style={{ textAlign: "center" }}>
              {search.length > 0 ? (
                <p>
                  {translate("dash.no-purchases-title")} <b>{search}</b>
                </p>
              ) : (
                <b>{translate("common.seen-all")}</b>
              )}
            </p>
          }
        >
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            flexWrap="wrap"
            p={1}
          >
            {data.map((m, i) => (
              <Box
                key={i}
                sx={{
                  width: {
                    xs: "100%",
                    sm: "48%",
                    md: "33%",
                  },
                  p: 1,
                }}
              >
                <PurchaseCard
                  index={i}
                  setPurchasesData={setData}
                  purchaseDetails={m}
                />
              </Box>
            ))}
          </Box>
        </InfiniteScroll>
      )}
    </PageHeaderAndContainer>
  );
};

export default Mypurchases;
