import React, { useState } from "react";
import Head from "next/head";
import { Container, Box, Button, Typography } from "@mui/material";
import HomeHeroSection from "../components/home/<USER>";
import HomeVideoSection from "@/components/home/<USER>";
import dynamic from "next/dynamic";
import { withSessionSSR } from "@/lib/auth/session";
import { useRouter } from "next/router";
import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";
import useVisitor from "@/hooks/useVisitor";
import useTranslate from "@/hooks/useTranslate";

const defaultMessage = "Hello, Patito Feo";

// Lazy load non-critical components
const CardContainerSection = dynamic(
  () => import("@/components/home/<USER>"),
  { loading: () => <p>Loading...</p>, ssr: false }
);

const CardsExperienceContainerSection = dynamic(
  () => import("@/components/home/<USER>"),
  { loading: () => <p>Loading...</p>, ssr: false }
);

const ActivitiesSection = dynamic(
  () => import("@/components/home/<USER>"),
  { loading: () => <p>Loading...</p>, ssr: false }
);

const CustomerFeedbackSection = dynamic(
  () => import("@/components/home/<USER>"),
  { loading: () => <p>Loading...</p>, ssr: false }
);

const FontAwesomeIcon = dynamic(
  () =>
    import("@fortawesome/react-fontawesome").then((mod) => mod.FontAwesomeIcon),
  { ssr: false }
);

export const getServerSideProps = withSessionSSR(
  async function getServerSideProps({ req }) {
    const user = req.session.user || null;
    const NEXT_PUBLIC_WHATSAPP_PHONE =
      process.env.NEXT_PUBLIC_WHATSAPP_PHONE || "";

    return {
      props: {
        user,
        NEXT_PUBLIC_WHATSAPP_PHONE,
      },
    };
  }
);

export default function Home({ user, NEXT_PUBLIC_WHATSAPP_PHONE }) {
  const [makeFormVisible, setMakeFormVisible] = useState(false);
  const [signIn, setSignIn] = useState(false);
  const router = useRouter();
  useVisitor();
  const { translate } = useTranslate();

  const goToSignup = () => {
    router.push("/sign-up");
  };

  return (
    <>
      <SEO
        title={Metadata.HOME_PAGE.title}
        description={Metadata.HOME_PAGE.description}
        keywords={Metadata.HOME_PAGE.keywords.join(",")}
      />

      <Container
        className="home-container"
        maxWidth={false}
        sx={{
          // width: "100vw",
          // minHeight: "100vh",
          objectFit: "cover",
          padding: "0 !important",
          margin: "0 !important",
        }}
      >
        {typeof NEXT_PUBLIC_WHATSAPP_PHONE !== "undefined" &&
          NEXT_PUBLIC_WHATSAPP_PHONE && (
            <a
              href={`https://wa.me/${NEXT_PUBLIC_WHATSAPP_PHONE}?text=${encodeURIComponent(
                defaultMessage
              )}`}
              className="float"
              target="_blank"
              aria-label="Whatsapp"
              rel="noopener noreferrer"
            >
              <FontAwesomeIcon
                icon={["fab", "whatsapp"]}
                className="my-float"
              />
            </a>
          )}

        <div className="home-wrapper">
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              width: "100%",
            }}
          >
            <HomeVideoSection />
            <HomeHeroSection goToSignup={goToSignup} user={user} />

            <CardContainerSection />
            <CardsExperienceContainerSection />
            <ActivitiesSection />
            <CustomerFeedbackSection />
          </div>

          {!user && makeFormVisible && (
            <Box
              sx={{
                background: "url('/assets/signup_bg.webp')",
                backgroundPosition: "center",
                backgroundSize: "cover",
                minHeight: "100vh",
                height: { xs: "100vh", md: "80vh" },
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  paddingTop: "4rem",
                }}
              >
                <Typography
                  component="span"
                  sx={{
                    fontSize: { xs: "1.2rem", sm: "2.5rem", md: "2.7rem" },
                    textAlign: "center",
                    flexShrink: 0,
                    userSelect: "none",
                  }}
                >
                  <Button
                    onClick={() => setSignIn(false)}
                    sx={{
                      fontWeight: "bold",
                      fontSize: { xs: "1.7rem", md: "2.7rem" },
                      outline: "none",
                      border: "none",
                      backgroundColor: "transparent",
                      cursor: "pointer",
                      color: signIn ? "#4DAA9F" : "black",
                      "&:hover": {
                        backgroundColor: "transparent",
                      },
                      width: "auto",
                      textTransform: "none",
                    }}
                  >
                    {translate("common.signup")}
                  </Button>
                  <span className="px-1" style={{ verticalAlign: "middle" }}>
                    {translate("common.or")}
                  </span>
                  <Button
                    onClick={() => setSignIn(true)}
                    sx={{
                      fontWeight: "bold",
                      fontSize: { xs: "1.7rem", md: "2.7rem" },
                      outline: "none",
                      border: "none",
                      backgroundColor: "transparent",
                      cursor: "pointer",
                      color: signIn ? "black" : "#4DAA9F",
                      "&:hover": {
                        backgroundColor: "transparent",
                      },
                      width: "auto",
                      textTransform: "none",
                    }}
                  >
                    {translate("common.signin")}
                  </Button>
                </Typography>
              </div>
            </Box>
          )}
        </div>
      </Container>
    </>
  );
}
