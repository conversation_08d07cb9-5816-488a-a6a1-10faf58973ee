import { useRouter } from "next/router";
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Button,
} from "@mui/material";
import fs from "fs";
import path from "path";
import Head from "next/head";

function BlogPostPage({ post }) {
  const router = useRouter();

  if (!post) {
    return (
      <Container maxWidth="sm">
        <Typography variant="h5" sx={{ my: 2 }}>
          Post not found.
        </Typography>
      </Container>
    );
  }

  return (
    <>
      <Head>
        <title>{post.title}</title>
      </Head>{" "}
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Card raised>
          <CardMedia
            component="img"
            height="400"
            image={post.image}
            alt={post.title}
          />
          <CardContent>
            <Typography gutterBottom variant="h4" component="div">
              {post.title}
            </Typography>
            <Box display="flex" flexWrap="wrap" sx={{ mb: 2 }}>
              {post.tags.map((tag, index) => (
                <Chip key={index} label={tag} sx={{ mr: 1, mb: 1 }} />
              ))}
            </Box>
            <Typography variant="subtitle1" color="textSecondary">
              {post.author} - {post.date}
            </Typography>
            <Typography variant="body1" sx={{ mt: 2 }}>
              {post.description}
            </Typography>
          </CardContent>
        </Card>
      </Container>
    </>
  );
}

export async function getStaticProps({ params }) {
  const filePath = path.resolve("src", "data", "blog", "blogPosts.json");
  const fileContent = fs.readFileSync(filePath, "utf8");
  const posts = JSON.parse(fileContent);
  const post = posts.find((p) => p.id.toString() === params.id);

  return {
    props: {
      post,
    },
  };
}

export async function getStaticPaths() {
  const filePath = path.resolve("src", "data", "blog", "blogPosts.json");
  const fileContent = fs.readFileSync(filePath, "utf8");
  const posts = JSON.parse(fileContent);
  const paths = posts.map((post) => ({ params: { id: post.id.toString() } }));

  return {
    paths,
    fallback: false,
  };
}

export default BlogPostPage;
