// pages/404.js
import React from "react";
import Image from "next/image";
import { Box, Typography, Button, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";

const Custom404 = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        textAlign: "center",
        bgcolor: "background.default",
        minHeight: { xs: "100vh", sm: "100vh", md: "80vh" },
      }}
    >
      <Box
        sx={{
          maxWidth: "100%", // Ensures the image is responsive
          height: isMobile ? "50vh" : "auto", // Adjusts size for mobile
        }}
      >
        <Image
          src="/images/Error.png"
          alt="Error in multiple languages"
          width={1200}
          height={700}
          layout="responsive" 
        />
      </Box>
      <Typography variant="subtitle1" color="textSecondary" gutterBottom>
        <strong>It seems we&apos;ve encountered a problem.</strong>
        <br />
        <em>Parece que hemos encontrado un problema.</em>
      </Typography>

      <Button
        variant="contained"
        color="primary"
        sx={{
          mt: 4,
          px: 2,
          py: 1,
          minWidth: { xs: "280px", sm: "280px" },
          textTransform: "none",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
          p: 3,
        }}
        onClick={() => (window.location.href = "/")}
      >
        Take me home / Llévame a casa
      </Button>
    </Box>
  );
};

export default Custom404;
