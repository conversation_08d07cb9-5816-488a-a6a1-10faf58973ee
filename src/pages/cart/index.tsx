import React, { useEffect, useMemo, useState } from "react";
import { Typo<PERSON>, Container, Grid, Button, Box } from "@mui/material";
import PlanSelector from "@/components/CheckoutModal/CheckoutClass/PlanSelector";
import CustomButton from "@/components/CustomButton";
import Image from "next/image";
import EmptyCart from "@/components/Cart/EmptyCart";
import CartLoading from "@/components/Cart/loading";
import axiosInstance from "@/utils/interceptor";
import { useSnackbar } from "@/hooks/useSnackbar";
import { CartType, ClubType, EventSchemaType } from "@/api/mongoTypes";
import Checkout from "@/components/Cart/Checkout";
import { useRouter } from "next/router";
import InfiniteScroll from "react-infinite-scroll-component";
import { getPricingTitle } from "@/utils/format";
import EventCartCard from "@/components/Cart/EventCartCard";
import { useUserContext } from "@/contexts/UserContext";
import useCart from "@/hooks/useCart";
import usePayment from "@/hooks/usePayment";
import {
  checkIfEveryCurrencySame,
  getLocalCarts,
  getPriceSymbol,
  getProductData,
} from "@/utils/classes";
import ClubCartCard from "@/components/Cart/ClubCartCard";
import { CURRENCY_ENUM } from "@/constant/Enums";
import { EventSchemaWithSingleEvent } from "@/types";
import { getSingleEventOn } from "@/utils/common";
import useTranslate from "@/hooks/useTranslate";

const containerStyles = { marginTop: "30px", height: "100%" };
const cartTitleStyles = {
  fontSize: "20px",
  fontWeight: "600",
  borderBottom: "1px solid var(--muted-foreground, #64748B)",
  marginBottom: "13px",
};
const buttonContainerStyles = {
  display: "flex",
  justifyContent: "center",
};

const buttonStyles = {
  whiteSpace: "nowrap",
  width: "320px",
  backgroundColor: "#14A79C",
  textTransform: "capitalize",
};

const LIMIT = 10;

export default function Cart() {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState<CartType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { dbUser } = useUserContext();
  const { translate } = useTranslate();

  const total = useMemo(() => {
    if (!data || data?.length === 0) {
      return 0;
    }

    const calculateTotal = (data: CartType) => {
      if (
        data?.eventId &&
        typeof data?.eventId === "object" &&
        "price" in data?.eventId
      ) {
        return data?.eventId?.price;
      }

      if (
        data?.clubId &&
        typeof data?.clubId === "object" &&
        "price" in data?.clubId
      ) {
        const totalMemberships = data.memberships.length;
        return +data?.clubId?.price * totalMemberships;
      }

      const classPlans = (() => {
        // if (
        //   (data?.classesId && typeof data?.classesId !== "object") ||
        //   !(data?.classesId && "plans" in data?.classesId)
        // ) {
        //   return [];
        // }
        return data.classesId.plans;
      })();

      const boughtPlans = data?.plans;

      // Match planId with _id in classesId.plans and sum up prices
      const totalPrice = boughtPlans?.reduce((total, boughtPlan) => {
        const matchingPlan = classPlans.find(
          (plan) => String(plan.planId) === String(boughtPlan.planId)
        );
        return matchingPlan ? total + matchingPlan.price : total;
      }, 0);

      return totalPrice;
    };

    return data.reduce((sum, current) => {
      return sum + calculateTotal(current);
    }, 0);
  }, [data]);

  const [amount, setAmount] = useState(total);
  const [discount] = useState(0);
  const { isMaking, makePayment } = usePayment();

  const finalAmount = amount - discount;

  useEffect(() => {
    setAmount(total);
  }, [total]);

  const { handleAddManyToCart } = useCart({
    onDeleteSuccess: () => {},
    onAddingSuccess: () => {},
  });

  const isLoggedIn = useMemo(() => {
    return !!dbUser;
  }, [dbUser]);

  const cartIds = useMemo(() => {
    if (data.length === 0) {
      return [];
    }
    return data.map((m) => m?._id);
  }, [data]);

  const classesIds = useMemo(() => {
    if (data?.length === 0) {
      return [];
    }
    return data
      .filter((m) => m?.classesId && !m.clubId)
      .map((m) => m.classesId);
  }, [data]);

  const clubIds = useMemo(() => {
    if (data?.length === 0) {
      return [];
    }
    return data.filter((m) => m?.clubId).map((m) => m.clubId._id);
  }, [data]);

  const memberships = useMemo(() => {
    if (data?.length === 0) {
      return [];
    }
    const clubList = data.filter((f) => f.clubId);
    return clubList.reduce(
      (prev, current) => [...prev, ...current.memberships],
      []
    );
  }, [data]);

  const eventIds = useMemo(() => {
    if (data?.length === 0) {
      return [];
    }
    return data.filter((m) => m?.eventId?._id).map((m) => m?.eventId?._id);
  }, [data]);

  const eventsPriceDetails = useMemo(() => {
    if (data?.length === 0) {
      return [];
    }
    return data
      .filter((m) => m?.eventId?._id)
      .map((m) => {
        const eventDetails = m.eventId as EventSchemaWithSingleEvent;
        const singleEventOn = getSingleEventOn(eventDetails.eventOn);
        return {
          eventInfo: eventDetails._id,
          price: +eventDetails?.price,
          currency: eventDetails?.currency ?? CURRENCY_ENUM.USD,
          eventOn: singleEventOn?._id,
        };
      });
  }, [data]);

  const productData = useMemo(() => {
    return getProductData(data);
  }, [data]);

  const flattenPlans = useMemo(() => {
    if (!data || !Array.isArray(data)) {
      return [];
    }
    return data.map((m) => (Array.isArray(m.plans) ? m.plans : [])).flat();
  }, [data]);

  const clubsDetails = useMemo(() => {
    const filteredClub = data.filter((f) => f?.clubId);
    return filteredClub.map((m) => ({
      clubInfo: String(m.clubId._id),
      memberships: m.memberships,
    }));
  }, [data]);

  const classesDetails = useMemo(() => {
    return data
      .filter((f) => f?.classesId?.title)
      .map((m) => ({
        classInfo: m.classesId,
        plans: m.plans,
      }));
  }, [data]);

  const { isEveryCurrencySame, allCurrencies } = useMemo(() => {
    const classesList = data
      .filter((m) => m?.classesId?.plans?.length > 0)
      .map((m) => CURRENCY_ENUM.USD);
    const eventList = data
      .filter((m) => m?.eventId)
      .map((m) => {
        const eventDetails = m.eventId as EventSchemaType;
        return eventDetails.currency;
      });
    const clubList = data
      .filter((m) => m?.clubId)
      .map((m) => {
        const clubDetails = m.clubId as ClubType;
        return clubDetails.currency;
      });
    const allCurrencies = [...eventList, ...clubList, ...classesList];
    return {
      isEveryCurrencySame: checkIfEveryCurrencySame({
        currencies: allCurrencies,
      }),
      allCurrencies,
    };
  }, [data]);

  const handlePayment = () => {
    if (isLoggedIn) {
      if (finalAmount === 0) {
        console.log("amount is zero");
      }
      makePayment({
        cartId: cartIds,
        price: finalAmount,
        productData,
        eventId: eventIds,
        clubsDetails,
        classesDetails,
        // classesId: classesIds,
        // clubIds,
        // memberships,
        // plans: flattenPlans,
        eventsPriceDetails,
        currency: allCurrencies[0],
      });
    } else {
      router.push("/sign-up");
    }
  };

  // useEffect(() => {
  //   if (cartIds && isLoggedIn && !isLoading) {
  //     const localCarts = getLocalCarts();
  //     if (localCarts.length > 0) {
  //       localStorage.removeItem("CART");
  //       // handlePayment();
  //     }
  //   }
  // }, [cartIds, isLoggedIn, isLoading]);

  const saveLocalCartsToServer = async () => {
    try {
      const localCarts = getLocalCarts();
      if (localCarts.length > 0) {
        setIsLoading(true);
        const payload = localCarts.map((m) => ({
          classesId: m?.classesId,
          eventId: m?.eventId?._id ?? null,
          plans: m?.plans,
          memberships: m?.memberships ?? [],
          clubId: m?.clubId?._id ?? null,
          eventOn: m?.eventOn?._id ?? null,
        }));
        await handleAddManyToCart({
          payload,
        });
        localStorage.removeItem("CART");
        // handlePayment();
      }
    } catch (error) {
      console.error(
        "Something went wrong in saveLocalCartsToServer due to ",
        error
      );
    }
  };

  const fetchCarts = async ({ skipCount = 0, isLoggedIn = false }) => {
    if (isLoggedIn) {
      await saveLocalCartsToServer();
    }
    if (!isLoggedIn) {
      const localCarts = getLocalCarts();
      setIsLoading(false);
      setData(localCarts);
      setHasMore(false);
      return;
    }

    const handleError = () => {
      showSnackbar(translate("cart.failed-fetch"), {
        type: "error",
      });
    };

    const showLoading = +skipCount === 0;
    if (skipCount === 0) {
      setData([]);
    }
    try {
      if (showLoading) {
        setIsLoading(true);
      }
      const params = {
        skip: skipCount,
        limit: LIMIT,
      };
      const { data } = await axiosInstance.get(`cart/all`, {
        params,
      });
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        handleError();
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.error(`Something went wrong in fetching carts `, error);
      handleError();
    }
  };

  useEffect(() => {
    fetchCarts({ skipCount: 0, isLoggedIn });
  }, [isLoggedIn]);

  if (isLoading) {
    return <CartLoading />;
  }

  if (data.length === 0) {
    return <EmptyCart />;
  }
  const handleDeleteSuccess = (item: CartType) => {
    setData((prev) => {
      const newData = prev.filter((f) => f._id !== item._id);
      return newData;
    });
  };

  console.log("data", data);

  const planText = (() => {
    const text = translate("cart.plan-added");
    if (data.length === 1) {
      return `1 ${text}`;
    }
    const newText = text.replace("plan", "plans");
    return `${data.length} ${newText}`;
  })();

  return (
    <>
      <Container maxWidth="lg" sx={containerStyles}>
        <Typography
          sx={{
            textAlign: "center",
            fontSize: "2.25rem",
            fontWeight: "800",
          }}
        >
          {translate("cart.shopping-cart")}
        </Typography>

        <Typography
          sx={{
            fontSize: "1.1rem",
            fontWeight: "600",
            marginBottom: "13px",
          }}
        >
          {planText}
        </Typography>
        <Grid container spacing={9}>
          <Grid item xs={12} md={7}>
            <InfiniteScroll
              style={{
                width: "100%",
              }}
              dataLength={data.length}
              next={() => fetchCarts({ skipCount: skip })}
              hasMore={hasMore}
              loader={<p style={{ textAlign: "center" }}>Loading...</p>}
              endMessage={
                <p style={{ textAlign: "center" }}>
                  {<b>{translate("common.seen-all")}</b>}
                </p>
              }
            >
              <Box
                sx={{
                  borderRadius: 1,
                  mb: 10,
                }}
              >
                {data.map((item, i) => {
                  if (item?.eventId) {
                    return (
                      <EventCartCard
                        isLoggedIn={isLoggedIn}
                        handleDeleteSuccess={() => {
                          handleDeleteSuccess(item);
                        }}
                        data={item}
                        key={item._id}
                      />
                    );
                  } else if (item?.clubId?._id) {
                    return (
                      <ClubCartCard
                        handleDeleteSuccess={() => {
                          handleDeleteSuccess(item);
                        }}
                        onUpdateCartSuccess={(data) => {
                          setData((prev) => {
                            const newData = prev.map((m) => {
                              if (m._id === data._id) {
                                return {
                                  ...m,
                                  memberships: data.memberships,
                                };
                              }
                              return m as any;
                            });
                            return newData;
                          });
                        }}
                        data={item}
                        key={item.clubId._id}
                      />
                    );
                  } else {
                    return (
                      <PlanSelector
                        data={item}
                        isLoggedIn={isLoggedIn}
                        key={item._id}
                        handleUpdateSuccess={(data) => {
                          setData((prev) => {
                            const newData = prev.map((f) => {
                              if (f._id === data._id) {
                                return data;
                              }
                              return f;
                            });
                            return newData;
                          });
                        }}
                        handleDeleteSuccess={() => {
                          handleDeleteSuccess(item);
                        }}
                      />
                    );
                  }
                })}
              </Box>
            </InfiniteScroll>
            <Box sx={buttonContainerStyles}>
              <Button
                sx={buttonStyles}
                onClick={() => {
                  router.push("/classes");
                }}
              >
                {translate("cart.browse-classes")}
              </Button>
            </Box>
          </Grid>
          <Grid item xs={12} md={5}>
            <Checkout
              currenyNotSame={!isEveryCurrencySame}
              total={total}
              handlePayment={handlePayment}
              isMaking={isMaking}
              discount={discount}
              finalAmount={finalAmount}
              currencySymbol={getPriceSymbol({
                currency: allCurrencies[0],
              })}
            />
          </Grid>
        </Grid>
      </Container>
    </>
  );
}
