import { VideoCollection } from "@/api/mongo";
import {
  handleCollectionProgress,
  handleVideoCollectionMap,
} from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const {
        name,
        desc,
        videoIds,
        imageId,
        imageKey,
        proficiencyLevel,
        videoDuration,
        categories,
      } = req.body;
      const newCollection = await VideoCollection.create({
        name,
        desc,
        imageId,
        imageKey,
        proficiencyLevel,
        videoCount: videoIds.length,
        videoDuration,
        categories,
      });
      if (newCollection) {
        if (videoIds?.length > 0) {
          await handleVideoCollectionMap({
            videoIds: videoIds,
            collectionId: newCollection._id,
          });
          await handleCollectionProgress({
            collectionId: newCollection._id,
            userId: String(req.headers.userid),
          });
        }
        res.status(201).json({
          data: newCollection,
          message: "Collection created successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Something went wrong while creating collection",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in collection/create due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
