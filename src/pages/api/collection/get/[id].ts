import { Video, VideoCollection, VideoCollectionMap } from "@/api/mongo";
import {
  getAllCollectionDetails,
  getAllVideoDetails,
} from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const {
    id,
    needCoverImages = false,
    needLikeStatus = false,
    needProgress = false,
    needVideos = false,
  } = req.query;
  const userId = req.headers.userid;

  if (!id) {
    res.status(400).json({
      data: null,
      message: "id cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "GET") {
      const singleCollection = await VideoCollection.findById(id).populate([
        {
          path: "proficiencyLevel",
        },
      ]);
      if (singleCollection) {
        const collectionInfo = await getAllCollectionDetails({
          data: singleCollection,
          userId: String(userId),
          needCoverImages: Boolean(needCoverImages),
          needLikeStatus: Boolean(needLikeStatus),
          needProgress: Boolean(needProgress),
        });
        let videos = [];
        if (needVideos) {
          const videosIdsList = await VideoCollectionMap.find({
            collectionId: id,
          }).select("videoId");
          const existingIds = videosIdsList.map((e) => e.videoId.toString());
          const videosList = await Video.find({
            _id: { $in: existingIds },
          }).populate({
            path: "proficiencyLevel",
          });
          videos = await Promise.all(
            videosList.map(async (m) => {
              const videoInfo = await getAllVideoDetails({
                needProgress: true,
                needLikeStatus: true,
                needCreator: true,
                userId: String(userId),
                data: m.toObject(),
                needThumbnail: true,
              });
              return videoInfo;
            })
          );
          videos = videos.filter((f) => f !== null);
        }
        res.status(200).json({
          data: {
            ...collectionInfo,
            videos,
          },
          message: "fetched collection successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: `Something went wrong while fetching collection using id ${id}`,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in collection/get/${id} due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
