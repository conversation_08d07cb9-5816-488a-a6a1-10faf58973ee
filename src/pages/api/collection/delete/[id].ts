import { deleteAwsFile } from "@/api/aws";
import {
  VideoCollection,
  VideoCollectionLikesMap,
  VideoCollectionMap,
  VideoCollectionProgress,
} from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "DELETE") {
      const { id } = req.query;
      const formattedId = String(id);
      const videoCollectionDetails = await VideoCollection.findByIdAndDelete(
        formattedId
      );
      if (videoCollectionDetails) {
        await deleteVideoCollectionMappings(formattedId);
        await deleteCoverImages(String(videoCollectionDetails.imageKey));
        return res.status(201).json({
          data: videoCollectionDetails,
          message: "Collection deleted successfully",
          success: true,
        });
      } else {
        return res.status(400).json({
          data: null,
          message: "Something went wrong while deleting collection",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["DELETE"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in collection/delete due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const deleteVideoCollectionMappings = async (id: string) => {
  if (id) {
    await VideoCollectionMap.deleteMany({
      collectionId: id,
    });
    await VideoCollectionLikesMap.deleteMany({
      collectionId: id,
    });
    await VideoCollectionProgress.deleteMany({
      collectionId: id,
    });
  }
};

const deleteCoverImages = async (awskey?: string) => {
  if (awskey) {
    await deleteAwsFile(awskey);
  }
};
