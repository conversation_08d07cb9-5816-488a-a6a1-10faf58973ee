import { deleteAwsFile } from "@/api/aws";
import { VideoCollection, VideoCollectionMap } from "@/api/mongo";
import {
  handleCollectionProgress,
  handleVideoCollectionMap,
} from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const {
        id,
        name,
        desc,
        imageId,
        imageKey,
        level,
        deletedImageKey,
        videoIds,
        proficiencyLevel,
        videoDuration,
        categories,
      } = req.body;

      const payload = {
        name,
        desc,
        imageId,
        imageKey,
        level,
        proficiencyLevel,
        videoCount: videoIds.length,
        videoDuration,
        categories,
      };

      if (!imageId) {
        delete payload.imageId;
        delete payload.imageKey;
      }

      const videoCollectionDetails = await VideoCollection.findById(id);
      const existing = await VideoCollectionMap.find({
        collectionId: id,
      }).select("videoId");

      const existingIds = existing.map((e) => e.videoId.toString());
      const newVideoIds = videoIds.filter(
        (f: string) => !existingIds.includes(f)
      );
      const deleteIds = existingIds.filter((f) => !videoIds.includes(f));

      if (videoCollectionDetails) {
        const updatedVideo = await VideoCollection.findByIdAndUpdate(
          id,
          payload,
          {
            new: true,
          }
        );
        if (deletedImageKey) {
          await deleteAwsFile(deletedImageKey);
        }
        if (deleteIds.length > 0) {
          await deleteTheVideoCollectionMap({ id, deleteIds });
        }
        if (newVideoIds.length > 0) {
          await handleVideoCollectionMap({
            videoIds: newVideoIds,
            collectionId: id,
          });
        }
        await handleCollectionProgress({
          collectionId: id,
          userId: String(req.headers.userid),
        });
        return res.send({
          data: updatedVideo,
          message: "Updated the collection successfully",
          success: true,
        });
      }

      res.send({
        data: null,
        message: "Failed to update the collection",
        success: false,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in collection/update due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

type deleteTheVideoCollectionMapProps = {
  id: string;
  deleteIds: string[];
};
const deleteTheVideoCollectionMap = async ({
  id,
  deleteIds,
}: deleteTheVideoCollectionMapProps) => {
  await VideoCollectionMap.deleteMany({
    collectionId: id,
    videoId: { $in: deleteIds },
  });
};
