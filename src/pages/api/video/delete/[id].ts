import { deleteAwsFile } from "@/api/aws";
import {
  Video,
  VideoCollectionMap,
  VideoLike,
  VideoProgress,
} from "@/api/mongo";
import { handleCollectionProgress } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "DELETE") {
      const { id } = req.query;
      const userId = String(req?.headers?.userid ?? "");
      const formattedId = String(id);
      const videoDetails = await Video.findByIdAndDelete(formattedId);
      if (videoDetails) {
        await deleteAwsAssets({
          en_subtitleKey: String(videoDetails.en_subtitleKey),
          es_subtitleKey: String(videoDetails.es_subtitleKey),
          videoKey: String(videoDetails.videoKey),
          thumbnailKey: String(videoDetails.thumbnailKey),
        });
        await deleteMappings(formattedId, userId);
        return res.status(201).json({
          data: videoDetails,
          message: "Collection deleted successfully",
          success: true,
        });
      } else {
        return res.status(400).json({
          data: null,
          message: "Something went wrong while deleting collection",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["DELETE"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in collection/delete due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const deleteAwsAssets = async ({
  en_subtitleKey,
  es_subtitleKey,
  videoKey,
  thumbnailKey,
}: {
  en_subtitleKey: string;
  es_subtitleKey: string;
  videoKey: string;
  thumbnailKey: string;
}) => {
  if (en_subtitleKey) {
    await deleteAwsFile(en_subtitleKey);
  }
  if (es_subtitleKey) {
    await deleteAwsFile(es_subtitleKey);
  }
  if (videoKey) {
    await deleteAwsFile(videoKey);
  }
  if (thumbnailKey) {
    await deleteAwsFile(thumbnailKey);
  }
};

const deleteMappings = async (id: string, userid: string) => {
  try {
    if (id) {
      const collectionIds = await VideoCollectionMap.find({
        videoId: id,
      }).select("collectionId");
      await VideoCollectionMap.deleteMany({
        videoId: id,
      });
      await VideoLike.deleteMany({
        videoId: id,
      });
      await VideoProgress.deleteMany({
        videoId: id,
      });
      if (collectionIds.length > 0) {
        await Promise.all(
          collectionIds.map(async (m) => {
            await handleCollectionProgress({
              collectionId: String(m.collectionId),
              userId: String(userid),
            });
          })
        );
      }
    }
  } catch (error) {
    throw error;
  }
};
