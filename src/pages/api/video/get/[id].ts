import { Video } from "@/api/mongo";
import { getAllVideoDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const {
    id,
    needCreator = false,
    needLikeStatus = false,
    needProgress = false,
    needCategoryIds = false,
    needCollectionIds = false,
    needThumbnail = false,
    needEsSubtitleUrl = false,
    needEnSubtitleUrl = false,
    needVideoUrl = false,
  } = req.query;
  const userId = req.headers.userid;

  if (!id) {
    res.status(400).json({
      data: null,
      message: "id cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "GET") {
      let video = null;
      if (needCreator) {
        video = await Video.findById(id).populate("creator");
      } else {
        video = await Video.findById(id);
      }
      if (video) {
        const data = await getAllVideoDetails({
          needProgress: Boolean(needProgress),
          needLikeStatus: Boolean(needLikeStatus),
          needCreator: Boolean(needCreator),
          needCategoryIds: Boolean(needCategoryIds),
          needCollectionIds: Boolean(needCollectionIds),
          needThumbnail: Boolean(needThumbnail),
          needEsSubtitleUrl: Boolean(needEsSubtitleUrl),
          needEnSubtitleUrl: Boolean(needEnSubtitleUrl),
          needVideoUrl: Boolean(needVideoUrl),
          userId: String(userId),
          data: video.toObject(),
        });

        res.status(200).json({
          data,
          message: "fetched video successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: `Something went wrong while fetching video using id ${id}`,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in video/get/${id} due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
