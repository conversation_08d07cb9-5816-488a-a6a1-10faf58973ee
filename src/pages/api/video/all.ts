import { Video } from "@/api/mongo";
import { getAllVideoDetails } from "@/api/mongoHelpers";
import { VIDEO_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        needCreator = false,
        needLikeStatus = false,
        needProgress = false,
        isFeatured = false,
        needCategoryIds = false,
        needCollectionIds = false,
        needThumbnail = false,
        needEsSubtitleUrl = false,
        needEnSubtitleUrl = false,
        needVideoUrl = false,
        needProficiencyLevel = false,
        search,
      } = req.query;
      const userId = req.headers.userid;

      const payload = {
        isFeatured,
        title: { $regex: search, $options: "i" },
      };

      if (!search) {
        delete payload.title;
      }

      if (!isFeatured) {
        delete payload.isFeatured;
      }

      const populate: any[] = [];

      if (needProficiencyLevel) {
        populate.push({
          path: "proficiencyLevel",
        });
      }
      if (needCreator) {
        populate.push({
          path: "creator",
          select: "firstName lastName profileImageKey",
        });
      }

      let data = await Video.find({
        status: VIDEO_STATUS.READY,
        ...payload,
      })
        .skip(+skip)
        .limit(+limit)
        .populate(populate);

      data = await Promise.all(
        data.map(async (m) => {
          const data = await getAllVideoDetails({
            needProgress: Boolean(needProgress),
            needLikeStatus: Boolean(needLikeStatus),
            needCreator: Boolean(needCreator),
            needCategoryIds: Boolean(needCategoryIds),
            needCollectionIds: Boolean(needCollectionIds),
            needThumbnail: Boolean(needThumbnail),
            needEsSubtitleUrl: Boolean(needEsSubtitleUrl),
            needEnSubtitleUrl: Boolean(needEnSubtitleUrl),
            needVideoUrl: Boolean(needVideoUrl),
            userId: String(userId),
            data: m.toObject(),
          });
          return data;
        })
      );

      res.status(200).json({
        data,
        message: "Videos fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["PUT"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-user due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
