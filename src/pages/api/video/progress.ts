import { VideoProgress } from "@/api/mongo";
import { handleCollectionProgress } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const userId = req.headers.userid;
      const { state, videoId, progress = 0, collectionId } = req.body;

      let updatedProgress: any = null;
      const existing = await VideoProgress.findOne({ videoId, userId });

      if (existing) {
        if (existing.progress === 100) {
          updatedProgress = existing;
        } else {
          updatedProgress = await VideoProgress.findOneAndUpdate(
            { videoId, userId },
            { userId, state: +state, videoId, progress },
            { new: true }
          );
        }
      } else {
        updatedProgress = await VideoProgress.create({
          userId,
          state: +state,
          videoId,
          progress,
        });
      }

      if (updatedProgress) {
        if (collectionId) {
          await handleCollectionProgress({
            collectionId,
            userId: String(userId),
          });
        }

        res.status(200).json({
          data: updatedProgress,
          message: "Updated the progress successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Failed to update progress",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in /api/video/progress due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
