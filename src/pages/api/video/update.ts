import { awsDownload, deleteAwsFile } from "@/api/aws";
import { Video, VideoCategory } from "@/api/mongo";
import { VideoType } from "@/api/mongoTypes";
import { Maybe } from "@/types";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const { getPopulated } = req.query;
      const {
        id,
        thumbnailKey,
        subtitleEnglishKey,
        subtitleSpanishKey,
        videoKey,
        status,
        title,
        description,
        language,
        proficiencyLevel,
        categoriesId,
        categoriesName,
        userId,
        duration,
        collectionId,
        isFeatured,
        level,
        deletedIds = {},
      } = req.body;

      const payload = {
        thumbnailKey,
        en_subtitleKey: subtitleEnglishKey,
        es_subtitleKey: subtitleSpanishKey,
        videoKey,
        status,
        title,
        description,
        language,
        proficiencyLevel,
        categories: categoriesName,
        creator: userId,
        duration,
        collectionId,
        isFeatured,
        level,
      };

      if (!thumbnailKey) {
        delete payload.thumbnailKey;
      }
      if (!subtitleEnglishKey) {
        delete payload.en_subtitleKey;
      }
      if (!subtitleSpanishKey) {
        delete payload.es_subtitleKey;
      }
      if (!videoKey) {
        delete payload.videoKey;
      }

      if (getPopulated) {
        const updatedVideo = await Video.findByIdAndUpdate(id, payload, {
          new: true,
        }).populate("creator");

        let profileImageUrl;

        if (
          updatedVideo?.creator &&
          typeof updatedVideo.creator !== "string" &&
          "profileImageKey" in updatedVideo.creator
        ) {
          const awsImgKey = updatedVideo.creator.profileImageKey;

          if (awsImgKey) {
            const { data } = await awsDownload(awsImgKey);
            profileImageUrl = data;
          }
        }

        const updatedVideoObject = updatedVideo?.toObject();
        const creatorObject =
          updatedVideo?.creator &&
          typeof updatedVideo.creator !== "string" &&
          "profileImageKey" in updatedVideo.creator
            ? updatedVideo.creator.toObject()
            : null;

        const updatedData = {
          creator: creatorObject
            ? {
                profileImageUrl,
                ...creatorObject,
              }
            : null,
          ...updatedVideoObject,
        };
        await handleClean({
          deletedIds,
          id: updatedVideo._id,
          categoriesId,
          title,
        });
        res.send(updatedData);
      } else {
        const updatedVideo = await Video.findByIdAndUpdate(id, payload);
        await handleClean({
          deletedIds,
          id: updatedVideo._id,
          categoriesId,
          title,
        });
        res.send(updatedVideo);
      }
    } else {
      res.setHeader("Allow", ["PUT"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-video due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

type handleCleanProps = {
  id: string;
  title: string;
  categoriesId: string[];
  deletedIds: {
    deletedFileId: Maybe<string>;
    deletedThumnailId: Maybe<string>;
    deletedEsSubtitleId: Maybe<string>;
    deletedEnSubtitleId: Maybe<string>;
  };
};
const handleClean = async ({
  deletedIds,
  id,
  title,
  categoriesId,
}: handleCleanProps) => {
  try {
    const {
      deletedFileId,
      deletedThumnailId,
      deletedEsSubtitleId,
      deletedEnSubtitleId,
    } = deletedIds;
    if (deletedFileId) {
      await deleteAwsFile(deletedFileId);
    }
    if (deletedThumnailId) {
      await deleteAwsFile(deletedThumnailId);
    }
    if (deletedEsSubtitleId) {
      await deleteAwsFile(deletedEsSubtitleId);
    }
    if (deletedEnSubtitleId) {
      await deleteAwsFile(deletedEnSubtitleId);
    }
    if (categoriesId.length > 0) {
      await VideoCategory.deleteMany({
        videoId: id,
      });
      await VideoCategory.insertMany(
        categoriesId.map((m) => ({
          name: title,
          videoId: id,
          categoryId: m,
        }))
      );
    }
  } catch (e) {
    console.error("Something went wrong in handleClean due to", e);
  }
};
