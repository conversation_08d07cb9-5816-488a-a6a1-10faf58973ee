import {
  Video,
  VideoCategory,
  VideoCollection,
  VideoCollectionMap,
} from "@/api/mongo";
import { ENVIRONMENT } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import { v4 as uuidv4 } from "uuid";

const { NEXT_PUBLIC_ENVIRONMENT } = process.env;
const environment =
  NEXT_PUBLIC_ENVIRONMENT === "development" ||
  NEXT_PUBLIC_ENVIRONMENT === "staging"
    ? ENVIRONMENT.SANDBOX
    : ENVIRONMENT.PUBLISHED;

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const {
        title,
        description,
        language,
        proficiencyLevel,
        userId,
        duration,
        isFeatured,
        level,

        categoriesId,
        categoriesName,
        collectionId,
      } = req.body;
      const id = uuidv4();
      const newVideo = new Video({
        id,
        title,
        description,
        language,
        proficiencyLevel,
        categories: categoriesName,
        creator: userId,
        environment,
        duration,
        isFeatured,
        level,
      });
      const saved = await newVideo.save();
      const newVideoId = saved._id;
      if (categoriesId.length > 0) {
        handleCategories({
          categories: categoriesId,
          name: title,
          videoId: newVideoId,
        });
      }
      if (newVideoId && collectionId) {
        await handleCollections({
          videoId: newVideoId,
          collectionId,
          duration,
        });
      }
      res.send(saved);
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in create-video due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const handleCollections = async ({
  videoId,
  collectionId,
  duration,
  categories = [],
}) => {
  try {
    await VideoCollectionMap.create({
      videoId,
      collectionId,
    });
    await VideoCollection.findByIdAndUpdate(collectionId, {
      $inc: { videoCount: 1, videoDuration: duration },
      $addToSet: { categories: { $each: categories } },
    });
  } catch (error) {
    console.error(`Something went wrong in handleCollections due to`, error);
  }
};

type handleCategoriesProps = {
  videoId: string;
  name: string;
  categories: string[];
};
const handleCategories = async ({
  videoId,
  categories = [],
  name,
}: handleCategoriesProps) => {
  try {
    const payload = categories.map((m) => ({
      name,
      videoId,
      categoryId: m,
    }));
    await VideoCategory.insertMany(payload);
  } catch (error) {
    console.error(`Something went wrong in handleCategories due to`, error);
  }
};
