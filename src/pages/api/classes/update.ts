import { ClassDetails } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const { _id, plans, isPopular, isLimitedDeal } = req.body;

  if (!_id) {
    res.status(400).json({
      data: null,
      message: "_id cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "POST") {
      const payload = {
        _id,
        plans,
        isPopular,
        isLimitedDeal,
      };
      const updatedClass = await ClassDetails.findByIdAndUpdate(_id, payload, {
        new: true,
      });
      res.status(200).json({
        data: updatedClass,
        message: "Updated classDetails successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in classes/update due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
