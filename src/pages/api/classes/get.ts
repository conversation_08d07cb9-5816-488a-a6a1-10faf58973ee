import { ClassDetails, Event } from "@/api/mongo";
import { getAllEventDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const { type, subType } = req.query;

  if (!type) {
    res.status(400).json({
      data: null,
      message: "type cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "GET") {
      const payload = {
        type,
        subType,
      };
      if (!payload.subType) {
        delete payload.subType;
      }
      const classes = await ClassDetails.find(payload);
      res.status(200).json({
        data: classes,
        message: "fetched event successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in classes/get/${type} due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
