import { Transaction, Cart } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

function updatePlans(plans) {
  return plans.map((plan) => {
    if (plan.isDateEnabled) {
      const startDate = new Date(plan.startDate);

      if (!startDate) {
        return plan;
      }

      const mondayUTC = new Date(
        Date.UTC(
          startDate.getUTCFullYear(),
          startDate.getUTCMonth(),
          startDate.getUTCDate() - startDate.getUTCDay() + 1,
          0,
          0,
          0,
          0
        )
      );

      const thursdayUTC = new Date(
        Date.UTC(
          mondayUTC.getUTCFullYear(),
          mondayUTC.getUTCMonth(),
          mondayUTC.getUTCDate() + 3,
          0,
          0,
          0,
          0
        )
      );

      return {
        ...plan,
        startDate: mondayUTC.toISOString(),
        endDate: thursdayUTC.toISOString(),
      };
    }
    return plan;
  });
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const carts = await Cart.find({ "plans.isDateEnabled": true });
      for (const cart of carts) {
        cart.plans = updatePlans(cart.plans);
        await cart.save();
      }

      const transactions = await Transaction.find({
        "classesDetails.plans.isDateEnabled": true,
      });
      for (const transaction of transactions) {
        transaction.classesDetails = transaction.classesDetails.map(
          (detail) => ({
            ...detail,
            plans: updatePlans(detail.plans),
          })
        ) as any;
        await transaction.save();
      }

      res.status(200).json({
        data: null,
        message:
          "Successfully updated plan dates to next Monday and Thursday UTC",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error("Error updating plan dates:", error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
