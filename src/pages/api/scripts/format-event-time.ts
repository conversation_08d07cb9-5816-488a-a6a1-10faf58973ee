import { Event } from "@/api/mongo";
import { combineDateNTime } from "@/utils/dateTime";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const events = await Event.find();

      let successCount = 0;
      let failureCount = 0;

      await Promise.all(
        events.map(async (event) => {
          try {
            const startDateTime = combineDateNTime({
              date: event.startDate,
              time: event.startTime,
              timezone: event.timezone,
            });

            const endDateTime = combineDateNTime({
              date: event.endDate,
              time: event.endTime,
              timezone: event.timezone,
            });

            if (!startDateTime || !endDateTime) {
              failureCount++;
              return;
            }

            event.startDateTime = startDateTime;
            event.endDateTime = endDateTime;
            await event.save();
            successCount++;
          } catch (err) {
            failureCount++;
          }
        })
      );

      res.status(200).json({
        success: true,
        message: "Events updated successfully",
        updated: successCount,
        failed: failureCount,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in scripts/format-event-time due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
