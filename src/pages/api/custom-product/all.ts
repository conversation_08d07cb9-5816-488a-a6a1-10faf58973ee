import { CustomProduct } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const { skip = 0, limit = 10, isArchieved, search } = req.query;
      const userId = req.headers.userid;

      const payload = {
        isExpired: isArchieved === "true",
        title: { $regex: search, $options: "i" },
      };

      if (!search) {
        delete payload.title;
      }

      let customProducts = await CustomProduct.find(payload)
        .skip(+skip)
        .limit(+limit)
        .populate([
          {
            path: "targetLanguage",
          },
          {
            path: "transactionId",
            select: "mode status",
          },
        ]);

      res.status(200).json({
        data: customProducts,
        message: "Custom products fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(
      `Something went wrong in /api/custom-product/all due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
