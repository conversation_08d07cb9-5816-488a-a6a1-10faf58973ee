import { CustomProduct, Transaction, User } from "@/api/mongo";
import { sendEmail } from "@/api/sendEmail";
import { getPaymentEmailBody } from "@/constant/email/paymentEmail";
import { PAYMENT_MODE, PAYMENT_STATUS } from "@/constant/Enums";
import {
  AMOUNT_CONVERTOR,
  getItemsDetailsFromTransaction,
} from "@/utils/classes";
import { handleTranslate } from "@/utils/common";
import { getUserFullName, isEnglishLanguage } from "@/utils/format";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const userId = req.headers.userid;
      const { code, productId, isPayByCash = false } = req.body;

      const productDetails = await CustomProduct.findOne({
        code,
        _id: productId,
      });

      if (productDetails?.isExpired) {
        return res.status(409).json({
          data: null,
          message: "Product is expired",
          success: false,
        });
      }

      if (!productDetails) {
        return res.status(409).json({
          data: null,
          message: "Invalid code",
          success: false,
        });
      }
      const finalAmount = productDetails.price * AMOUNT_CONVERTOR;
      const createdTransaction = await Transaction.create({
        userId,
        classesDetails: [],
        clubsDetails: [],
        eventIds: [],
        eventsPriceDetails: [],
        customProductsDetails: {
          price: productDetails.price,
          currency: productDetails.currency,
          customProductsInfo: productDetails._id,
        },
        cartId: [],
        status: PAYMENT_STATUS.SUCCESS,
        mode: isPayByCash ? PAYMENT_MODE.CASH : PAYMENT_MODE.BANK_TRANSFER,
        price: finalAmount,
        discount: 0,
        finalAmount: finalAmount - 0,
        transactionDate: new Date(),
      });
      await productDetails.updateOne({
        isExpired: true,
        transactionId: createdTransaction._id,
        productFor: userId,
      });
      await handleMail({
        id: userId,
        transactionId: createdTransaction._id,
      });
      res.status(200).json({
        data: createdTransaction._id,
        message: "Custom Product transferred successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in custom-product/transfer due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const handleMail = async ({ id, transactionId }) => {
  try {
    const userDetails = await User.findById(id).populate("languageOfInterest");
    const isEnglish = isEnglishLanguage(userDetails?.languageOfInterest);
    console.log({
      userDetails,
      isEnglish,
    });
    const transactionDetails = await Transaction.findById(transactionId)
      .populate("eventIds")
      .populate("customProductsDetails.customProductsInfo")
      .populate([
        {
          path: "clubsDetails",
          populate: {
            path: "clubInfo",
          },
        },
      ]);
    const paymentDetails = getItemsDetailsFromTransaction(transactionDetails);
    const mailBody = getPaymentEmailBody({
      name: getUserFullName(userDetails),
      receiptUrl: "",
      isError: false,
      transactionId: transactionDetails._id,
      paymentDetails,
      totalAmount: +transactionDetails.price,
      isSubscription: false,
      isEnglish,
    });
    await sendEmail({
      body: mailBody,
      subject: handleTranslate("email.pe.success", isEnglish),
      to: [userDetails.email],
    });
  } catch (error) {
    throw error;
  }
};
