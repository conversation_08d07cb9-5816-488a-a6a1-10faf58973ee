import { CustomProduct } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const {
        title,
        description,
        internalNotes,
        currency,
        targetLanguage,
        price,
        startDate,
        instructor,
        timezone,
        id,
        expirationDate,
        extendTwoDays,
      } = req.body;

      const payload = {
        title,
        description,
        internalNotes,
        currency,
        targetLanguage,
        price,
        startDate,
        instructor,
        timezone,
        expirationDate,
      };

      if (!payload.expirationDate) {
        delete payload.expirationDate;
      }
      if (!payload.timezone) {
        delete payload.timezone;
      }
      if (!payload.instructor) {
        delete payload.instructor;
      }
      if (!payload.startDate) {
        delete payload.startDate;
      }
      if (!payload.price) {
        delete payload.price;
      }
      if (!payload.targetLanguage) {
        delete payload.targetLanguage;
      }
      if (!payload.currency) {
        delete payload.currency;
      }
      if (!payload.internalNotes) {
        delete payload.internalNotes;
      }
      if (!payload.description) {
        delete payload.description;
      }
      if (!payload.title) {
        delete payload.title;
      }
      if (extendTwoDays) {
        const expiration = new Date(expirationDate);
        expiration.setDate(expiration.getDate() + 2);
        payload.expirationDate = expiration;
      }

      const createCustomProduct = await CustomProduct.findByIdAndUpdate(
        id,
        payload
      );

      if (createCustomProduct) {
        res.status(201).json({
          data: createCustomProduct,
          message: "Custom Product updated successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Something went wrong while creating Custom product",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in api/custom-product/update due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
