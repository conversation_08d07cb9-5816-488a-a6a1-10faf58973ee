import { CustomProduct } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import { v4 as uuidv4 } from "uuid";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const {
        title,
        description,
        internalNotes,
        currency,
        targetLanguage,
        price,
        startDate,
        instructor,
        timezone,
      } = req.body;

      let code: string;
      let isUnique = false;

      while (!isUnique) {
        code = uuidv4().slice(0, 4);
        const exists = await CustomProduct.findOne({ code });
        if (!exists) {
          isUnique = true;
        }
      }

      const expiration = new Date(startDate);
      expiration.setDate(expiration.getDate() + 5);

      const createCustomProduct = await CustomProduct.create({
        title,
        description,
        internalNotes,
        currency,
        targetLanguage,
        price,
        startDate,
        instructor,
        code,
        timezone,
        expirationDate: expiration,
      });

      if (createCustomProduct) {
        res.status(201).json({
          data: createCustomProduct,
          message: "Custom Product created successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Something went wrong while creating Custom product",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in event/custom-product/create due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
