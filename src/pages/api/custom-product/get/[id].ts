import { CustomProduct } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const { id, isAdmin = false } = req.query;
      const userId = req.headers.userid;
      const projection =
        isAdmin === "true"
          ? {}
          : {
              instructor: 0,
              transactionId: 0,
              code: 0,
              internalNotes: 0,
            };
      let customProduct = await CustomProduct.findById(id, projection).populate(
        "targetLanguage"
      );
      const isExpired = customProduct?.isExpired;
      res.status(200).json({
        data: isExpired ? { isExpired, _id: customProduct._id } : customProduct,
        message: "Custom product fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(
      `Something went wrong in /api/custom-product/get/:id due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
