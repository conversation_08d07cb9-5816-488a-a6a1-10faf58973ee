import { CustomProduct } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { id } = req.body;

      const updatedCustomProduct = await CustomProduct.findByIdAndUpdate(id, {
        isExpired: true,
      });

      if (updatedCustomProduct) {
        res.status(200).json({
          data: updatedCustomProduct,
          message: "Custom Product archieved successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Something went wrong while archiving Custom product",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in api/custom-product/archieve due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
