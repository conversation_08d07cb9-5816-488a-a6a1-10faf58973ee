import { awsDownload } from "@/api/aws";
import { Role, User } from "@/api/mongo";
import { ROLE_TYPES } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { skip = 0, limit = 0, search, isStudentDashboard } = req.query;
      const role = req?.body?.role;
      let users = [];

      const typedIsStudentDashboard = isStudentDashboard === "true";

      const searchPayload = {};
      if (search) {
        searchPayload["$or"] = [
          { firstName: { $regex: search, $options: "i" } },
          { lastName: { $regex: search, $options: "i" } },
          { email: { $regex: search, $options: "i" } },
        ];
      }

      if (role) {
        const studentRoles = await Role.find({
          type: role,
        }).select("_id");
        if (!studentRoles || studentRoles.length === 0) {
          users = [];
        } else {
          const populateArray: any = [
            {
              path: "allowedRoles",
            },
          ];

          if (typedIsStudentDashboard) {
            populateArray.push({
              path: "countryOfResidence",
            });
            populateArray.push({
              path: "languages",
              populate: [{ path: "language" }, { path: "proficiency" }],
            });
            populateArray.push({
              path: "languageOfInterest",
            });
          }

          users = await User.find({
            allowedRoles: { $in: studentRoles.map((r) => r._id) },
            ...searchPayload,
          })
            .populate(populateArray)
            .skip(+skip)
            .limit(+limit)
            .sort({ createdAt: -1 });
        }
      } else {
        users = await User.find({
          ...searchPayload,
        })
          .populate("allowedRoles")
          .skip(+skip)
          .limit(+limit);
      }

      const usersWithImageUrl = await Promise.all(
        users.map(async (m) => {
          if (m?.profileImageId && m?.profileImageKey) {
            const profileImageUrl = await awsDownload(m?.profileImageKey);
            return {
              ...m.toObject(),
              profileImageUrl: profileImageUrl?.data ?? "",
            };
          }
          return {
            ...m.toObject(),
            profileImageUrl: "",
          };
        })
      );

      res.status(200).json({
        data: usersWithImageUrl,
        message: "users fetched sucessfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in get-users due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
