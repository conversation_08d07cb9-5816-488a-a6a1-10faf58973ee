import { Club, Event, Schedule, User } from "@/api/mongo";
import { getAllClubDetails, getAllEventDetails } from "@/api/mongoHelpers";
import { TransactionType, UserLanguageType } from "@/api/mongoTypes";
import { CLASSES_TYPE, IN_PERSON_TYPE } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import dayjs from "dayjs";
import { GROUP_INPERSON } from "data/pricing";
import { isInPersonGroupClassLive } from "@/utils/classes";
import { getDateAsPerUTC } from "@/utils/dateTime";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const userId = req.headers.userid;
      const { isUpcoming, currentDate } = req.query;
      const formattedCurrentDate = getDateAsPerUTC(
        new Date(String(currentDate))
      );

      const showUpcoming = isUpcoming === "true";

      const today = dayjs().startOf("day");

      // Calculate the start and end of "this week" (Monday to Sunday)
      const startOfThisWeek = today.startOf("isoWeek"); // Monday of this week
      const endOfThisWeek = today.endOf("isoWeek"); // Sunday of this week

      // Calculate the start and end of "next week" (Monday to Sunday)
      const startOfNextWeek = today.add(1, "week").startOf("isoWeek"); // Monday of next week
      const endOfNextWeek = today.add(1, "week").endOf("isoWeek"); // Sunday of next week
      const dateRange = showUpcoming
        ? { $gte: startOfNextWeek.toDate(), $lte: endOfNextWeek.toDate() }
        : { $gte: startOfThisWeek.toDate(), $lte: endOfThisWeek.toDate() };

      // - A minimum of 4 suggested
      // - Services equal to the **language proficiency** level - matching criteria
      // - Always include at least a 30 minute online private class, one experience, and one club
      // - Clubs and Experiences that match **interests**. If no direct interest match, include tattoo class and xochimilco experience - matching criteria
      // - Always start results w clubs, experiences

      const userDetails = await User.findById(userId).populate([
        {
          path: "languages",
          populate: {
            path: "language proficiency",
          },
        },
      ]);
      console.log("userId", userId);
      console.log("userDetails", userDetails);
      const langauges = userDetails.languages.map((m) => m as UserLanguageType);
      const selectedProficiencies = langauges.map((m) => m.proficiency._id);

      // clubs
      let clubs = [];
      const defaultClubsIds = (() => {
        const envString = process.env.DEFAULT_CLUBS_IDS;
        if (envString) {
          return envString.split(",");
        } else {
          return [];
        }
      })();
      const clubsBasedOnproficiencies = await Club.find({
        proficiencyLevel: { $in: selectedProficiencies },
      })
        .populate("proficiencyLevel")
        .populate({
          path: "teachers",
          select:
            "firstName lastName profileImageKey profileImageId profileImageUrl",
        })
        .limit(2);
      const fetchedClubsLength = clubsBasedOnproficiencies.length;
      if (fetchedClubsLength < 2) {
        if (defaultClubsIds.length > 0) {
          await Promise.all(
            defaultClubsIds.map(async (m) => {
              const clubDetails = await Club.findById(m)
                .populate("proficiencyLevel")
                .populate({
                  path: "teachers",
                  select:
                    "firstName lastName profileImageKey profileImageId profileImageUrl",
                });
              if (clubDetails) {
                clubs.push(clubDetails);
              }
            })
          );
        } else {
          const randomClubs = await Club.find()
            .populate("proficiencyLevel")
            .populate({
              path: "teachers",
              select:
                "firstName lastName profileImageKey profileImageId profileImageUrl",
            })
            .limit(2);
          clubs.push(...randomClubs);
        }
      } else {
        clubs.push(...clubsBasedOnproficiencies);
      }

      // events
      let events = [];
      const eventsOnProficiency = await Event.find({
        proficiencyLevel: { $in: selectedProficiencies },
        endDateTime: { $gte: formattedCurrentDate },
      })
        .populate("proficiencyLevel")
        .limit(2);
      if (eventsOnProficiency.length === 1) {
        events.push(...eventsOnProficiency);
      } else {
        const eventsWithoutProficiency = await Event.find({
          endDateTime: { $gte: formattedCurrentDate },
        })
          .populate("proficiencyLevel")
          .limit(2);
        events.push(...eventsWithoutProficiency);
      }

      events = await Promise.all(
        events.map(async (m) => {
          const eventsInfo = await getAllEventDetails({
            needEventImage: true,
            data: m,
          });
          return eventsInfo;
        })
      );

      clubs = (await Promise.all(
        clubs.map(async (m) =>
          getAllClubDetails({
            data: m,
            needClubImage: true,
            needCreatorImage: true,
          })
        )
      )) as any;

      // get schedukes classess
      let scheduleClassList = [];
      const shouldWeShowGroupClasses =
        isInPersonGroupClassLive(formattedCurrentDate);

      const showScheduledClasses =
        (shouldWeShowGroupClasses && !showUpcoming) || showUpcoming;

      if (showScheduledClasses) {
        const sheduledClass = await Schedule.findOne({
          type: CLASSES_TYPE.IN_PERSON,
          subType: IN_PERSON_TYPE.GROUP,
          students: { $ne: [] },
          teachers: { $ne: [] },
          startDate: dateRange,
        }).populate([
          {
            path: "students",
            populate: "transaction",
          },
        ]);

        if (sheduledClass?._id) {
          const transactionDetails = sheduledClass.students.map(
            (m) => m.transaction
          ) as TransactionType[];

          const classDetails =
            transactionDetails.map((m) => m?.classesDetails ?? []).flat() ?? [];
          scheduleClassList = classDetails;
        }

        if (scheduleClassList.length === 0) {
          scheduleClassList.push({
            classInfo: GROUP_INPERSON[0],
          });
        }
      }

      const suggestions = [
        ...clubs.map((club) => ({
          userId,
          eventId: null,
          clubId: club,
        })),
        ...events
          .slice(0, scheduleClassList.length === 1 ? 1 : 2)
          .map((event) => ({
            userId,
            eventId: event,
            clubId: null,
          })),
        ...scheduleClassList.map((classD) => ({
          userId,
          eventId: null,
          clubId: null,
          classesId: classD.classInfo,
        })),
      ];

      res.status(200).json({
        data: suggestions,
        message: "Suggestions fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in user/get-dashboard-suggested due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
