import { Role, User, UserLanguage } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import { ROLE_TYPES } from "@/constant/Enums";
import { clerkClient } from "@clerk/nextjs/server";
import { RoleType } from "@/api/mongoTypes";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { id, updatedRoles } = req.body;

      if (!id) {
        return res
          .status(400)
          .json({ message: "User ID is required", success: false });
      }
      try {
        const user = await User.findById(id).populate("allowedRoles");
        if (!user) {
          return res
            .status(404)
            .json({ message: "User not found", success: false });
        }

        // checking if the roles is of creator/admin
        // if its we should only allow if the user email ends pf domain
        const isCreatorOrAdmin =
          updatedRoles.includes(ROLE_TYPES.CREATOR) ||
          updatedRoles.includes(ROLE_TYPES.ADMIN);

        if (isCreatorOrAdmin) {
          const isPatitoEmail = user.email.includes("@patitofeo.com");
          if (!isPatitoEmail) {
            return res.status(409).json({
              message: "Only patitofeo.com email is allowed for this role",
              success: false,
            });
          }
        }

        const userRoles = user.allowedRoles as RoleType[];
        const typedUpdatedRoles = updatedRoles as ROLE_TYPES[];

        const currentRoleTypes = userRoles.map((role) => role.type);
        const rolesToCreate = typedUpdatedRoles.filter(
          (roleType) => !currentRoleTypes.includes(roleType)
        );
        const rolesToDelete = userRoles.filter(
          (role) => !typedUpdatedRoles.includes(role.type)
        );

        console.log({
          currentRoleTypes,
          typedUpdatedRoles,
          rolesToCreate,
          rolesToDelete: rolesToDelete.map((r) => r.type),
        });

        const createdRoleIds = [];
        for (const roleType of rolesToCreate) {
          const newRole = new Role({
            type: roleType,
            user: id,
          });
          const savedRole = await newRole.save();
          createdRoleIds.push(savedRole._id);
        }

        const roleIdsToDelete = rolesToDelete.map((role) => role._id);
        if (roleIdsToDelete.length > 0) {
          await Role.deleteMany({ _id: { $in: roleIdsToDelete } });
        }

        const finalAllowedRoleIds = [
          ...userRoles
            .filter((role) => typedUpdatedRoles.includes(role.type))
            .map((role) => role._id),
          ...createdRoleIds,
        ];

        const payload = {
          allowedRoles: finalAllowedRoleIds,
        };

        const updatedUser = await User.findByIdAndUpdate(id, payload, {
          new: true,
        }).populate("allowedRoles");

        if (!updatedUser) {
          return res
            .status(404)
            .json({ message: "User not found", success: false });
        }

        await updateClerkMetaData({
          clerkId: updatedUser.clerkId,
          publicMetadata: {
            role: updatedRoles.join(","),
          },
        });
        return res.status(200).json({
          message: "User updated successfully",
          data: updatedUser,
          success: true,
        });
      } catch (error) {
        console.error("Error updating user:", error);
        return res
          .status(500)
          .json({ message: "Failed to update user", success: false });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-user-role due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const updateClerkMetaData = async ({ clerkId, publicMetadata }) => {
  try {
    // fore the users created from internal tools dont have clerk id
    // incase of such user we are doing nothing
    if (clerkId) {
      const clerk = await clerkClient();
      await clerk.users.updateUser(clerkId, {
        publicMetadata,
      });
    }
  } catch (error) {
    console.error("Something went wrong in updateClerkMetaData due to ", error);
    throw error;
  }
};
