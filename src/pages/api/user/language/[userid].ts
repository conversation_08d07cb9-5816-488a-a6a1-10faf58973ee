import { UserLanguage } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { userid } = req.query;
      const languages = await UserLanguage.find({
        user: userid,
        language: { $exists: true },
        proficiency: { $exists: true },
      })
        .populate("language")
        .populate("proficiency");
      if (languages) {
        res.status(200).json({
          message: "Languages fetched successfully",
          data: languages,
        });
      } else {
        res.status(404).json({
          message: "languages doesnt exists",
          data: null,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in user/language/:userId due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
