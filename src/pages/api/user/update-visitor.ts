import { User, Visitor } from "@/api/mongo";
import { FormatResponse, getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const { latitude, longitude } = req.body;
      const userId = req.headers.userid;

      if (!latitude || !longitude) {
        return res.status(400).json({
          data: null,
          message: "Latitude and Longitude are required",
          success: false,
        });
      }

      const userDetails = await User.findById(userId);
      if (userDetails) {
        const visitorId = userDetails?.visitorId;
        if (visitorId) {
          await Visitor.findByIdAndUpdate(visitorId, {
            latitude,
            longitude,
          });
        } else {
          const createdVisitor = await Visitor.create({
            latitude,
            longitude,
            userId,
          });
          await userDetails.updateOne({
            visitorId: createdVisitor._id,
          });
        }
        return res.status(200).json(
          FormatResponse({
            data: null,
            message: "Visitor location updated successfully",
            success: true,
          })
        );
      }
      return res.status(404).json(
        FormatResponse({
          data: null,
          message: "User not found",
          success: false,
        })
      );
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-visitor due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
