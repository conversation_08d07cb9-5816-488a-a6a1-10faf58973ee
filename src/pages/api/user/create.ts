import { Role, User } from "@/api/mongo";
import { ROLE_TYPES } from "@/constant/Enums";
import { FormatResponse, getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const user = await User.create(req.body);
      if (user) {
        const userId = user._id;
        const role = req.body.role ?? ROLE_TYPES.CREATOR;
        const createdRole = await Role.create({
          type: role,
          description: "",
          user: userId,
        });
        const createdRoleId = createdRole?._id;
        let updatedUser = null;
        if (createdRole) {
          updatedUser = await User.findOneAndUpdate(
            userId,
            { $push: { allowedRoles: createdRoleId } },
            { new: true }
          );
        }
        res.status(201).json(
          FormatResponse({
            data: updatedUser,
            message: "user created successfully",
            success: true,
          })
        );
      } else {
        res.status(400).json(
          FormatResponse({
            data: null,
            message: "Failed to create user",
            success: false,
          })
        );
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-user due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
