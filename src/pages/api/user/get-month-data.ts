import { Schedule } from "@/api/mongo";
import { getFirstAndLastDateOfMonth } from "@/utils/classes";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import mongoose from "mongoose";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { monthNumber, isSuggestion = false } = req.query;

      const userId = req.headers.userid;
      const sanitizedMonth = +monthNumber;

      const typedUserId = new mongoose.Types.ObjectId(String(userId));
      const typedIsSuggestion = isSuggestion === "true";

      const { firstDate, lastDate } =
        getFirstAndLastDateOfMonth(sanitizedMonth);

      console.log({
        firstDate,
        lastDate,
        typedUserId,
        typedIsSuggestion,
      });

      const myClassesPipeline: any[] = [
        // Schedule branch
        {
          $match: {
            $and: [
              { students: { $elemMatch: { userId: typedUserId } } },
              {
                $expr: {
                  $and: [
                    { $lte: ["$startDate", lastDate] },
                    { $gte: ["$endDate", firstDate] },
                  ],
                },
              },
            ],
          },
        },
        {
          $project: {
            type: "$type",
            startDate: "$startDate",
            endDate: "$endDate",
            timezone: "",
            id: "$_id",
          },
        },
        // Event branch
        {
          $unionWith: {
            coll: "transactions",
            pipeline: [
              {
                $match: {
                  userId: typedUserId,
                  status: "SUCCESS",
                },
              },
              { $unwind: "$eventIds" },
              {
                $lookup: {
                  from: "events",
                  let: { eventId: "$eventIds" },
                  pipeline: [
                    {
                      $match: {
                        $and: [
                          { $expr: { $eq: ["$_id", "$$eventId"] } },
                          {
                            $expr: {
                              $and: [
                                {
                                  $lte: ["$startDateTime", lastDate],
                                },
                                {
                                  $gte: ["$endDateTime", firstDate],
                                },
                                // {
                                //   $lte: [{ $toDate: "$startDate" }, lastDate],
                                // },
                                // {
                                //   $gte: [{ $toDate: "$endDate" }, firstDate],
                                // },
                              ],
                            },
                          },
                        ],
                      },
                    },
                  ],
                  as: "eventInfo",
                },
              },
              {
                $unwind: {
                  path: "$eventInfo",
                  preserveNullAndEmptyArrays: true,
                },
              },
              { $match: { eventInfo: { $exists: true } } },
              {
                $project: {
                  type: { $literal: "event" },
                  startDate: "$eventInfo.startDateTime",
                  endDate: "$eventInfo.endDateTime",
                  timezone: "$eventInfo.timezone",
                  id: "$eventInfo._id",
                },
              },
            ],
          },
        },
        {
          $sort: {
            startDate: 1,
          },
        },
      ];

      const mySuggestionPipeline: any[] = (() => {
        const schedulePipeline = [
          {
            $match: {
              $and: [
                {
                  $expr: {
                    $and: [
                      { $lte: ["$startDate", lastDate] },
                      { $gte: ["$endDate", firstDate] },
                    ],
                  },
                },
                {
                  students: { $exists: true, $not: { $size: 0 } },
                },
              ],
            },
          },
          {
            $addFields: {
              student: {
                $first: {
                  $filter: {
                    input: "$students",
                    as: "s",
                    cond: { $eq: ["$$s.userId", typedUserId] },
                  },
                },
              },
              isBought: {
                $gt: [
                  {
                    $size: {
                      $filter: {
                        input: "$students",
                        as: "s",
                        cond: { $eq: ["$$s.userId", typedUserId] },
                      },
                    },
                  },
                  0,
                ],
              },
            },
          },
          {
            $lookup: {
              from: "transactions",
              let: { transactionId: "$student.transaction" },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ["$_id", "$$transactionId"] },
                  },
                },
                {
                  $unwind: {
                    path: "$classesDetails",
                    preserveNullAndEmptyArrays: true,
                  },
                },
                {
                  $replaceRoot: {
                    newRoot: { $ifNull: ["$classesDetails", {}] },
                  },
                },
              ],
              as: "classDetails",
            },
          },
          {
            $addFields: {
              type: "schedule",
              data: {
                $mergeObjects: ["$$ROOT", { classDetails: "$classDetails" }],
              },
              transactionId: "$student.transaction",
              userId: "$student.userId",
            },
          },
          {
            $replaceRoot: {
              newRoot: {
                type: "$type",
                // data: "$data",
                // transactionId: "$transactionId",
                // userId: "$userId",
                isBought: "$isBought",
                startDate: "$data.startDate",
                endDate: "$data.endDate",
                timezone: "",
                id: "$data._id",
              },
            },
          },
          { $match: { isBought: false } },
        ];

        const eventPipeline = [
          {
            $unionWith: {
              coll: "events",
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $lte: ["$startDateTime", lastDate] },
                        { $gte: ["$endDateTime", firstDate] },
                      ],
                    },
                  },
                },
                {
                  $lookup: {
                    from: "transactions",
                    localField: "_id",
                    foreignField: "eventIds",
                    pipeline: [
                      {
                        $match: {
                          userId: typedUserId,
                          status: "SUCCESS",
                        },
                      },
                    ],
                    as: "transactionInfo",
                  },
                },
                {
                  $addFields: {
                    transactionId: {
                      $first: "$transactionInfo._id",
                    },
                    userId: {
                      $first: "$transactionInfo.userId",
                    },
                    isBought: {
                      $gt: [{ $size: "$transactionInfo" }, 0],
                    },
                  },
                },
                {
                  $replaceRoot: {
                    newRoot: {
                      type: "event",
                      //   data: "$$ROOT",
                      //   transactionId: "$transactionId",
                      //   userId: "$userId",
                      isBought: "$isBought",
                      startDate: "$startDateTime",
                      endDate: "$endDateTime",
                      timezone: "$timezone",
                      id: "$_id",
                    },
                  },
                },
                { $match: { isBought: false } },
              ],
            },
          },
        ];

        return [...schedulePipeline, ...eventPipeline];
      })();

      const pipeline: any[] = (() => {
        if (typedIsSuggestion) {
          return mySuggestionPipeline;
        } else {
          return myClassesPipeline;
        }
      })();

      const results = await Schedule.aggregate(pipeline);

      res.status(200).json({
        message: "Scheduled classes fetched successfully",
        data: results,
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in user/get-month-data due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
