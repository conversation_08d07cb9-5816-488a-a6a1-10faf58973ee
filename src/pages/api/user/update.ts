import { User, UserLanguage } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const {
        interests,
        location,
        languages,
        goals,
        id,
        profileImageKey,
        profileCreated = false,
        firstName,
        lastName,
        languageOfInterest,
        completeLater,
        whatsAppNo,
        proficiencyOfLanguageOfInterest,
        level,
      } = req.body;

      if (!id) {
        res.status(400).json({ message: `id cannot be empty` });
      }

      const languagesIds = [];
      if (Array.isArray(languages)) {
        for (let l of languages) {
          const currentLanguage = l[0];
          const newId = await saveUserLanguageInfo({
            languageProficiency: currentLanguage.languageProficiencyId,
            langugaeId: currentLanguage.languageId,
            userId: id,
          });
          languagesIds.push(newId);
        }
      }

      if (languagesIds.length > 0 && Array.isArray(languagesIds)) {
        await deleteLanguageInfo({
          userId: id,
          newlySavedIds: languagesIds?.map((m) => m.toString()),
        });
      }

      const payload = {
        interest: interests,
        countryOfResidence: location,
        languages: languagesIds,
        goals: goals,
        profileImageKey,
        profileCreated,
        languageOfInterest,
        firstName,
        lastName,
        completeLater,
        whatsAppNo,
        proficiencyOfLanguageOfInterest,
        level,
      };

      if (!goals || goals.length === 0) {
        delete payload.goals;
      }
      if (!languagesIds || languagesIds.length === 0) {
        delete payload.languages;
      }
      if (!location) {
        delete payload.countryOfResidence;
      }
      if (!interests || interests.length === 0) {
        delete payload.interest;
      }
      if (!profileImageKey) {
        delete payload.profileImageKey;
      }
      if (!firstName) {
        delete payload.firstName;
      }
      if (!lastName) {
        delete payload.lastName;
      }
      if (!languageOfInterest) {
        delete payload.languageOfInterest;
      }
      if (!whatsAppNo) {
        delete payload.whatsAppNo;
      }
      if (!proficiencyOfLanguageOfInterest) {
        delete payload.proficiencyOfLanguageOfInterest;
      }
      if (!level) {
        delete payload.level;
      }

      const updatedUser = await User.findByIdAndUpdate(id, payload, {
        new: true,
      });
      if (updatedUser) {
        res.status(200).json({
          message: `User updated sucessfully`,
          data: updatedUser,
          success: true,
        });
      } else {
        res.status(400).json({
          message: `Something went wrong while updating user`,
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST", "PUT"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in update-user due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

type saveUserLanguageInfoProps = {
  langugaeId: string;
  languageProficiency: string;
  userId: string;
};
const saveUserLanguageInfo = async ({
  langugaeId,
  languageProficiency,
  userId,
}: saveUserLanguageInfoProps) => {
  const updatedDocument = await UserLanguage.findOneAndUpdate(
    { user: userId, language: langugaeId },
    { user: userId, language: langugaeId, proficiency: languageProficiency },
    { upsert: true, returnDocument: "after", new: true }
  );

  return updatedDocument._id;
};

type deleteLanguageInfoProps = {
  userId: string;
  newlySavedIds: string[];
};
const deleteLanguageInfo = async ({
  userId,
  newlySavedIds,
}: deleteLanguageInfoProps) => {
  try {
    const currentLanguages = await UserLanguage.find({
      user: userId,
      language: { $exists: true },
      proficiency: { $exists: true },
    });
    const currentIds = currentLanguages.map((language) =>
      language._id.toString()
    );
    const idsToDelete = currentIds.filter((id) => !newlySavedIds.includes(id));
    if (idsToDelete.length > 0) {
      await UserLanguage.deleteMany({ _id: { $in: idsToDelete } });
    }
  } catch (error) {
    console.error("Error in deleteLanguageInfo due to", error);
  }
};
