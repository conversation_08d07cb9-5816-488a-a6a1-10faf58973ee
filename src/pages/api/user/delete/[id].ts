import {
  Cart,
  Schedule,
  Transaction,
  User,
  UserLanguage,
  VideoCollectionProgress,
  VideoProgress,
  Visitor,
} from "@/api/mongo";
import { FormatResponse, getMessageAndStatusCode } from "@/utils/responseUtil";
import { clerkClient } from "@clerk/nextjs/server";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "DELETE") {
      const userId = req.headers.userid;
      const { id } = req.query;

      // so what i want to do is delete the user and all the related data
      // 1. delete the user
      // 2. delete the user language
      // 3. delete the user interest
      // 4. delete the user goal
      // 5. delete the user cart
      // 6. delete the user schedule
      // 8. delete the user transaction
      // 9. delete the stripe customer from stripe
      // 10. delete the clerk user

      const userDetails = await User.findById(String(id));
      if (userDetails) {
        await UserLanguage.deleteMany({ user: id });
        await Visitor.deleteMany({ userId: id });
        // await VideoProgress.deleteMany({ userId: id });
        // await VideoCollectionProgress.deleteMany({ userId: id });
        await Cart.deleteMany({ userId: id });
        await Schedule.updateMany({ userId: id }, { $pull: { students: id } });
        await deleteClerkuser({ clerkId: userDetails.clerkId });
        await User.deleteOne({ _id: id });

        res.status(200).json(
          FormatResponse({
            data: null,
            message: "User deleted successfully",
            success: true,
          })
        );
      } else {
        res.status(404).json(
          FormatResponse({
            data: null,
            message: "User does not exists.Please try again",
            success: false,
          })
        );
      }
    } else {
      res.setHeader("Allow", ["DELETE"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in delete-user due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const deleteClerkuser = async ({ clerkId }) => {
  try {
    const clerk = await clerkClient();
    await clerk.users.deleteUser(clerkId);
  } catch (error) {
    console.error("Something went wrong in deleteClerkuser due to ", error);
    throw error;
  }
};
