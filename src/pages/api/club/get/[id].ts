import { Club } from "@/api/mongo";
import { getAllClubDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const { id, needClubImage = false, needCreators = false } = req.query;
  const userId = req.headers.userid;

  if (!id) {
    res.status(400).json({
      data: null,
      message: "id cannot be empty",
      success: false,
    });
    return;
  }
  try {
    if (req.method === "GET") {
      const singleClub = await Club.findById(id)
        .populate("proficiencyLevel")
        .populate("targetLanguage")
        .populate({
          path: "teachers",
          select:
            "firstName lastName profileImageKey profileImageId profileImageUrl",
        });
      if (singleClub) {
        const clubInfo = await getAllClubDetails({
          data: singleClub,
          needClubImage: Boolean(needClubImage),
          needCreatorImage: !needCreators ? false : true,
        });
        res.status(200).json({
          data: clubInfo,
          message: "fetched club successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: `Something went wrong while fetching club using id ${id}`,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in club/get/${id} due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
