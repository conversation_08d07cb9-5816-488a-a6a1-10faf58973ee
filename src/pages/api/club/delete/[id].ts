import { Club } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "DELETE") {
      const { id } = req.query;

      const clubDetails = await Club.findById(id);

      if (clubDetails.noOfStudentsEnrolled > 0) {
        return res.status(409).json({
          data: null,
          message: `You cannot delete this club as it has been enrolled by ${clubDetails.noOfStudentsEnrolled} users`,
          success: false,
        });
      }

      const deletedClub = await Club.findByIdAndDelete(id);
      if (deletedClub) {
        res.status(201).json({
          data: deletedClub,
          message: "club deleted successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Something went wrong while deleting club",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["DELETE"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in club/delete due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
