import { Club } from "@/api/mongo";
import { getAllClubDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        needClubImage = false,
        search,
        targetLanguage,
      } = req.query;
      const userId = req.headers.userid;

      const typedSearch = new String(search).trim();
      const pipeline: any[] = [
        {
          $lookup: {
            from: "languages",
            localField: "targetLanguage",
            foreignField: "_id",
            as: "targetLanguage",
          },
        },
        {
          $unwind: {
            path: "$targetLanguage",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "proficiencylevels",
            localField: "proficiencyLevel",
            foreignField: "_id",
            as: "proficiencyLevel",
          },
        },
        {
          $unwind: {
            path: "$proficiencyLevel",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "teachers",
            foreignField: "_id",
            as: "teachers",
            pipeline: [
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  profileImageKey: 1,
                  profileImageId: 1,
                  profileImageUrl: 1,
                },
              },
            ],
          },
        },
      ];

      const matchConditions: any = {};

      if (typedSearch) {
        matchConditions.title = { $regex: typedSearch, $options: "i" };
      }

      if (targetLanguage) {
        matchConditions["targetLanguage.nameInEnglish"] = {
          $regex: new RegExp(`^${targetLanguage}$`, "i"),
        };
      }

      if (Object.keys(matchConditions).length > 0) {
        pipeline.push({ $match: matchConditions });
      }

      pipeline.push(
        { $sort: { createdAt: -1 } },
        { $skip: +skip },
        { $limit: +limit }
      );

      let clubs = await Club.aggregate(pipeline);

      if (needClubImage) {
        clubs = (await Promise.all(
          clubs.map(async (m) =>
            getAllClubDetails({
              data: m,
              needClubImage: true,
              needCreatorImage: true,
            })
          )
        )) as any;
      }

      if (clubs) {
        res.status(200).json({
          message: "Clubs fetched successfully",
          data: clubs,
          success: true,
        });
      } else {
        res.status(404).json({
          message: "Something went wrong while fetching clubs",
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in club/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
