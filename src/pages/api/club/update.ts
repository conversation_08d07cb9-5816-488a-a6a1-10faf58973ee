import { deleteAwsFile } from "@/api/aws";
import { Club } from "@/api/mongo";
import { handleClubPricesInfoAndUpdate } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const {
        id,
        title,
        about,
        price,
        theme,
        proficiencyLevel,
        categories,
        highlights,
        imagesKeysAndIds,
        deletedImageKeys,
        teachers,
        currency,
        targetLanguage,
      } = req.body;

      const filteredImagesKeyIds = imagesKeysAndIds.filter(
        (f) => !deletedImageKeys.includes(f.key)
      );

      const payload = {
        title,
        about,
        price,
        theme,
        proficiencyLevel,
        categories,
        highlights,
        imagesKeysAndIds: filteredImagesKeyIds,
        teachers,
        currency,
        targetLanguage,
      };

      if (!filteredImagesKeyIds || filteredImagesKeyIds.length === 0) {
        delete payload.imagesKeysAndIds;
      }
      if (!title) {
        delete payload.title;
      }
      if (!about) {
        delete payload.about;
      }
      if (!price) {
        delete payload.price;
      }
      if (!theme) {
        delete payload.theme;
      }
      if (!currency) {
        delete payload.currency;
      }
      if (!targetLanguage) {
        delete payload.targetLanguage;
      }
      if (!proficiencyLevel) {
        delete payload.proficiencyLevel;
      }
      if (!categories || categories.length === 0) {
        delete payload.categories;
      }
      if (!teachers || teachers.length === 0) {
        delete payload.teachers;
      }
      if (!highlights || highlights.length === 0) {
        delete payload.highlights;
      }

      const handleDeleteAwsImages = () => {
        if (deletedImageKeys.length > 0) {
          deletedImageKeys.map((m) => deleteAwsImage(m));
        }
      };

      const clubDetails = await Club.findById(id);

      if (clubDetails) {
        const isPriceChanged = clubDetails.price !== price;
        if (isPriceChanged) {
          const updatedClub = await handleClubPricesInfoAndUpdate({
            club: clubDetails,
            price,
            currency,
            existingPayloadToUpdate: payload,
          });
          handleDeleteAwsImages();
          return res.send({
            data: updatedClub,
            message: "Updated the club successfully",
            success: true,
          });
        } else {
          const updatedVideo = await Club.findByIdAndUpdate(id, payload, {
            new: true,
          });
          handleDeleteAwsImages();
          return res.send({
            data: updatedVideo,
            message: "Updated the club successfully",
            success: true,
          });
        }
      }

      res.send({
        data: null,
        message: "Failed to update the club",
        success: false,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in club/update due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const deleteAwsImage = (key: string) => {
  deleteAwsFile(key);
};
