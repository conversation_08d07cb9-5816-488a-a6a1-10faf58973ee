import { Cart } from "@/api/mongo";
import { getAllClubDetails, getAllEventDetails } from "@/api/mongoHelpers";
import { ClubType } from "@/api/mongoTypes";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        needEventImage = true,
        needClubImage = true,
      } = req.query;
      const userId = req.headers.userid;
      let cartData = await Cart.find({
        userId,
        isCheckedOut: false,
        isBuying: false,
      })
        .skip(+skip)
        .limit(+limit)
        .populate("eventId")
        .populate("eventOn")
        .populate({
          path: "clubId",
          populate: {
            path: "teachers",
            select:
              "firstName lastName profileImageKey profileImageId profileImageUrl",
          },
        });

      const getAllCartsData = await Promise.all(
        cartData.map(async (m) => {
          if (m.eventId) {
            const eventsInfo = await getAllEventDetails({
              needEventImage: Boolean(needEventImage),
              data: m.eventId,
            });
            return {
              ...m.toObject(),
              eventId: eventsInfo,
            };
          }
          if (m.clubId) {
            const clubInfo = await getAllClubDetails({
              needClubImage: Boolean(needClubImage),
              data: m.clubId as ClubType,
              needCreatorImage: true,
            });
            return {
              ...m.toObject(),
              clubId: clubInfo,
            };
          }
          return m;
        })
      );

      if (cartData) {
        res.status(200).json({
          message: "Carts fetched successfully",
          data: getAllCartsData,
          success: true,
        });
      } else {
        res.status(404).json({
          message: "Something went wrong while fetching Carts",
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in cart/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
