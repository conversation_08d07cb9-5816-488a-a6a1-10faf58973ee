import { Cart } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "DELETE") {
      const { id } = req.query;
      const userId = req.headers.userid;
      const updatedCart = await Cart.findOneAndDelete({
        _id: id,
        userId,
      });
      if (updatedCart) {
        return res.status(200).json({
          data: updatedCart,
          message: "Deleted the class from cart successfully",
          success: true,
        });
      }
      res.status(400).json({
        data: null,
        message: "Failed to Delete",
        success: false,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in cart/delete/:id due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
