import { Cart } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const { classesId, plans, _id, clubId, memberships } = req.body;
      const userId = req.headers.userid;
      const payload = {
        classesId,
        plans,
        clubId,
        memberships,
        _id,
      };
      if (!plans || plans.length === 0) {
        delete payload.plans;
      }
      if (!clubId) {
        delete payload.clubId;
      }
      if (!memberships || memberships.length === 0) {
        delete payload.memberships;
      }
      if (!classesId) {
        delete payload.classesId;
      }
      const updatedCart = await Cart.findByIdAndUpdate(_id, payload, {
        new: true,
      });

      if (updatedCart) {
        return res.status(201).json({
          data: updatedCart,
          message: "Updated the cart successfully",
          success: true,
        });
      }
      res.status(400).json({
        data: null,
        message: "Failed to update",
        success: false,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in cart/update due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
