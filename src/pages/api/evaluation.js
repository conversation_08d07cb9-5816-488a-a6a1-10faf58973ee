import spanishBeginnerData from "../../data/evaluations/spanish-beginner.json";

const evaluationData = {
  english: {
    beginner: spanishBeginnerData,
    intermediate: spanishBeginnerData,
    advanced: spanishBeginnerData,
  },
  spanish: {
    beginner: spanishBeginnerData,
    intermediate: spanishBeginnerData,
    advanced: spanishBeginnerData,
  },
};

export default async function handler(req, res) {
  const { language, level } = req.query;

  if (!evaluationData[language] || !evaluationData[language][level]) {
    return res.status(400).json({ error: "Invalid language or level" });
  }

  const data = evaluationData[language][level];

  try {
    res.status(200).json({
      success: true,
      data,
      method: "GET",
    });
  } catch (err) {
    res.status(500).json({ err });
  }
}
