import { <PERSON>Job } from "@/api/mongo";
import { FormatResponse, getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const { id, status, errorMsg } = req.body;
      const payload = { status, errorMsg };
      const mediaJob = await MediaJob.findByIdAndUpdate(id, payload, {
        new: true,
      });
      if (mediaJob) {
        res.status(200).json(
          FormatResponse({
            data: mediaJob,
            message: "Media Job updated successfully",
            success: true,
          })
        );
      } else {
        res.status(400).json(
          FormatResponse({
            data: null,
            message: "Failed to update Media Job",
            success: false,
          })
        );
      }
    } else {
      res.setHeader("Allow", ["PUT"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in media-job/update due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
