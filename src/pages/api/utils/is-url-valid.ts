import { getMessageAndStatusCode } from "@/utils/responseUtil";
import axios from "axios";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "POST") {
      const { url } = req.body;
      const response = await axios.head(url, { timeout: 5000 });
      const isValid = response.status >= 200 && response.status < 400;
      res.status(200).json({
        data: isValid,
        success: true,
        message: "",
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in is-url-valid due to`, error);
    const { message, status } = getMessageAndStatusCode(error);

    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
