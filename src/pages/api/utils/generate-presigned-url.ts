import { getSignedUrlToUpload } from "@/api/aws";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { key, name } = req.body;
      const { data, isError } = await getSignedUrlToUpload(key, name);
      if (isError) {
        res.status(400).json({
          data: null,
          message: "Failed to generate signed url",
          success: false,
        });
      } else {
        res.status(201).json({
          data,
          message: "Generated url successfully",
          success: true,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in utils/generate-presigned-url due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
