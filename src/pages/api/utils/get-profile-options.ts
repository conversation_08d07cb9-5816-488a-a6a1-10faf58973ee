import { Goal, Interest, Language, Country, LanguageProficiency } from '@/api/mongo';
import { getMessageAndStatusCode } from '@/utils/responseUtil';
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const resources = {
    goals: Goal,
    interests: Interest,
    languages: Language,
    countries: Country,
    proficiencies: LanguageProficiency,
  };

  try {
    if (req.method === 'GET') {
      const data = {};
      for (let key of Object.keys(resources))
        data[key] = await resources[key].find({});

      res.status(200).json({
        data,
      });
    } else {
      res.setHeader('Allow', ['GET']);
      res.status(405).end('Method Not Allowed');
    }
  } catch (error) {
    console.error(
      `[Profile Edit Error]: Unable to fetch sufficient data`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);

    res.status(status).json({
      data: null,
      message,
      status: false,
    });
  }
}
