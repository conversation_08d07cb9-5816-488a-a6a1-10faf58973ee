"use server";
import { FieldError } from "@/api/errors";
import { addToWaitlist } from "@/api/mongoHelpers";
import { emailValidator } from "@/api/validators";
import { NextApiRequest, NextApiResponse } from "next";
import zod from "zod";
import {
  SESClient,
  SendEmailCommand,
  SendBounceCommandInput,
  CloneReceiptRuleSetCommand,
  SendEmailCommandInput,
} from "@aws-sdk/client-ses";

const sesclt = new SESClient({
  region: "us-east-1",
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.NEXT_PUBLIC_AWS_SECRET_ACCESS_KEY!,
  }
});

import { setCookie, getCookie, CookieValueTypes } from "cookies-next";
import { Language } from "@/api/mongo";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === "POST") {
    // Process a POST request
    const { email, fname, lname, language } = (await req.body) as {
      email: string;
      fname: string;
      lname: string;
      language: string;
    };
    const langName = await Language.findById(language);
    if (!langName) {
      throw new Error("Language not found");
    }
    addToWaitlist(email, fname, lname, language);
    const cookie = getCookie("patitofeo", { req, res }) as string;

    setCookie(
      "patitofeo",
      { ...JSON.parse(cookie || "{}"), wl: 1 },
      {
        req,
        res,
      }
    );
    var params: SendEmailCommandInput = {
      Destination: {
        CcAddresses: [],
        ToAddresses: ["<EMAIL>"],
      },
      Message: {
        /* required */
        Body: {
          Text: {
            Charset: "UTF-8",
            Data: `New waitlist signup: ${email} ${fname} ${lname} ${langName.name}`,
          },
        },
        Subject: {
          Charset: "UTF-8",
          Data: "New waitlist signup",
        },
      },
      Source: "<EMAIL>",
      ReplyToAddresses: ["<EMAIL>"],
    };

    const command = new SendEmailCommand(params);
    try {
      await sesclt.send(command);
      return res.status(200).json({ success: true, errors: null });
    } catch (error) {
      console.log(error);
      return res.status(400).json({ success: false, errors: error });
    }
  } else {
    // Handle any other HTTP method
  }
}
