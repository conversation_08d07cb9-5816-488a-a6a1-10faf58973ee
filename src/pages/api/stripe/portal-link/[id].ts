import { User } from "@/api/mongo";
import {
  createStripeCustomer,
  getStripeCustomerPortalLink,
} from "@/api/stripe";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

const STRIPE_KEY = process.env.STRIPE_KEY;
const stripe = require("stripe")(STRIPE_KEY);

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { id } = req.query;
      const userId = req.headers.userid;
      if (id && typeof id === "string") {
        let { url, noCustomer } = await getStripeCustomerPortalLink(id);

        if (noCustomer) {
          const user = await User.findById(userId);
          console.log("user", user);
          if (user?.email) {
            let newCustomerId = user?.stripeCustomerId ?? "";
            if (!newCustomerId) {
              newCustomerId = await createStripeCustomer({
                email: user.email,
                name: user.firstName,
                userId,
              });
            }
            console.log("newCustomerId", newCustomerId);
            const { url: newUrl } = await getStripeCustomerPortalLink(
              newCustomerId
            );
            url = newUrl;
          }
        }

        console.log("url", url);
        if (!url) {
          res.status(400).json({
            message: `Something went wrong while fetching portal link`,
            data: "",
            success: false,
          });
        } else {
          res.status(200).json({
            message: `Fetched portal link successfully`,
            data: url,
            success: true,
          });
        }
      } else {
        res.status(400).json({
          message: `id is reguired`,
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in stripe/portal-link/:id due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
