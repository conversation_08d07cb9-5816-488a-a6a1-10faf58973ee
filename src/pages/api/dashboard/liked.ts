import { Video, VideoCollection, VideoCollectionLikesMap } from "@/api/mongo";
import {
  getAllCollectionDetails,
  getAllVideoDetails,
} from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import mongoose from "mongoose";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const { skip = 0, limit = 10, search, contentType } = req.query;
      const userId = req.headers.userid;

      const pipeline: any[] = [
        {
          $project: {
            likedBy: 1,
            collectionId: 1,
            videoId: { $literal: null },
            createdAt: 1,
            updatedAt: 1,
            type: { $literal: "collection" },
          },
        },
        {
          $unionWith: {
            coll: "videolikes",
            pipeline: [
              {
                $project: {
                  likedBy: 1,
                  videoId: 1,
                  collectionId: { $literal: null },
                  createdAt: 1,
                  updatedAt: 1,
                  type: { $literal: "video" },
                },
              },
            ],
          },
        },
        { $match: { likedBy: new mongoose.Types.ObjectId(String(userId)) } },
      ];

      // filter contentType
      if (contentType === "video") {
        pipeline.push({ $match: { type: "video" } });
      } else if (contentType === "collection") {
        pipeline.push({ $match: { type: "collection" } });
      }

      // populate collectionId
      pipeline.push(
        {
          $lookup: {
            from: "videocollections",
            localField: "collectionId",
            foreignField: "_id",
            as: "collection",
          },
        },
        {
          $unwind: { path: "$collection", preserveNullAndEmptyArrays: true },
        },
        {
          $lookup: {
            from: "languageproficiencies",
            localField: "collection.proficiencyLevel",
            foreignField: "_id",
            as: "collection.proficiencyLevel",
          },
        },
        {
          $unwind: {
            path: "$collection.proficiencyLevel",
            preserveNullAndEmptyArrays: true,
          },
        }
      );

      // populate videoId
      pipeline.push(
        {
          $lookup: {
            from: "videos",
            localField: "videoId",
            foreignField: "_id",
            as: "video",
          },
        },
        {
          $unwind: { path: "$video", preserveNullAndEmptyArrays: true },
        },
        {
          $lookup: {
            from: "languageproficiencies",
            localField: "video.proficiencyLevel",
            foreignField: "_id",
            as: "video.proficiencyLevel",
          },
        },
        {
          $unwind: {
            path: "$video.proficiencyLevel",
            preserveNullAndEmptyArrays: true,
          },
        }
      );

      // populate creator inside video
      pipeline.push({
        $lookup: {
          from: "users",
          localField: "video.creator",
          foreignField: "_id",
          as: "video.creator",
        },
      });
      pipeline.push({
        $unwind: {
          path: "$video.creator",
          preserveNullAndEmptyArrays: true,
        },
      });

      // apply search
      if (search) {
        pipeline.push({
          $match: {
            $or: [
              { "collection.name": { $regex: search, $options: "i" } },
              { "video.title": { $regex: search, $options: "i" } },
            ],
          },
        });
      }

      // sort, paginate
      pipeline.push({ $sort: { createdAt: -1 } });
      pipeline.push({ $skip: +skip });
      pipeline.push({ $limit: +limit });

      const results = await VideoCollectionLikesMap.aggregate(pipeline);
      const data = await Promise.all(
        results.map(async (m) => {
          const info: any = m;

          if (m.collectionId) {
            const collectionInfo = await getAllCollectionDetails({
              data: m.collection,
              userId: String(userId),
              needCoverImages: true,
              needLikeStatus: true,
              needProgress: true,
            });
            info.collection = collectionInfo;
            return info;
          }
          if (m.videoId) {
            const videoInfo = await getAllVideoDetails({
              needProgress: true,
              needLikeStatus: true,
              needCreator: true,
              userId: String(userId),
              data: m.video,
              needThumbnail: true,
            });
            info.video = videoInfo;
            return info;
          }
          return info;
        })
      );

      res.status(200).json({
        data,
        message: "fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in dashboard/liked due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
