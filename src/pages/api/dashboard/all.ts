import { VideoCollection } from "@/api/mongo";
import {
  getAllCollectionDetails,
  getAllVideoDetails,
} from "@/api/mongoHelpers";
import { DASHBOARD_SORT_BY } from "@/constant/dashboard";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import mongoose from "mongoose";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        search,
        contentType,
        subtitles,
        contentLevel,
        sortBy,
        categories,
      } = req.query;
      const userId = req.headers.userid;

      let sortStage: any = { createdAt: -1 };
      if (sortBy === DASHBOARD_SORT_BY.LIKED) {
        sortStage = { likedCount: -1 };
      } else if (sortBy === DASHBOARD_SORT_BY.DATE) {
        sortStage = { createdAt: -1 };
      }

      const contentTypes = contentType
        ? String(contentType)
            .split(",")
            .map((t) => t.trim())
        : [];
      const levels = contentLevel
        ? String(contentLevel)
            .split(",")
            .map((id) => new mongoose.Types.ObjectId(id))
        : [];
      const categoriesNames = categories
        ? String(categories)
            .split(",")
            .map((id) => String(id))
        : [];
      const subtitleKeys = subtitles
        ? String(subtitles)
            .split(",")
            .map((s) => `${s}_subtitleKey`)
        : [];

      const searchTerm = search ? String(search).trim() : "";

      let results = await VideoCollection.aggregate([
        {
          $project: {
            _id: 1,
            name: 1,
            proficiencyLevel: 1,
            type: { $literal: "collection" },
            createdAt: 1,
            categories: 1,
            likedCount: 1,
            videoCount: 1,
            videoDuration: 1,
            imageId: 1,
            imageKey: 1,
            desc: 1,
          },
        },
        {
          $lookup: {
            from: "languageproficiencies",
            localField: "proficiencyLevel",
            foreignField: "_id",
            as: "proficiencyLevel",
          },
        },
        {
          $unwind: {
            path: "$proficiencyLevel",
            preserveNullAndEmptyArrays: true,
          },
        },

        {
          $unionWith: {
            coll: "videos",
            pipeline: [
              {
                $lookup: {
                  from: "users",
                  localField: "creator",
                  foreignField: "_id",
                  as: "creator",
                },
              },
              {
                $unwind: { path: "$creator", preserveNullAndEmptyArrays: true },
              },
              {
                $lookup: {
                  from: "languageproficiencies",
                  localField: "proficiencyLevel",
                  foreignField: "_id",
                  as: "proficiencyLevel",
                },
              },
              {
                $unwind: {
                  path: "$proficiencyLevel",
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $project: {
                  _id: 1,
                  title: 1,
                  description: 1,
                  proficiencyLevel: 1,
                  en_subtitleKey: 1,
                  es_subtitleKey: 1,
                  type: { $literal: "video" },
                  createdAt: 1,
                  likedCount: 1,
                  videoKey: 1,
                  thumbnailKey: 1,
                  status: 1,
                  categories: 1,
                  language: 1,
                  duration: 1,
                  creator: {
                    _id: 1,
                    name: 1,
                    email: 1,
                    avatar: 1,
                  },
                },
              },
            ],
          },
        },
        {
          $match: {
            ...(searchTerm
              ? {
                  $or: [
                    { name: { $regex: searchTerm, $options: "i" } },
                    { title: { $regex: searchTerm, $options: "i" } },
                  ],
                }
              : {}),
            ...(levels.length
              ? { "proficiencyLevel._id": { $in: levels } }
              : {}),
            ...(subtitleKeys.length
              ? {
                  $or: subtitleKeys.map((key) => ({
                    [key]: { $exists: true, $ne: null },
                  })),
                }
              : {}),
            ...(contentTypes.length ? { type: { $in: contentTypes } } : {}),
            ...(categoriesNames.length
              ? { categories: { $in: categoriesNames } }
              : {}),
          },
        },
        { $sort: sortStage },
        { $skip: +skip },
        { $limit: +limit },
      ]);

      const data = await Promise.all(
        results.map(async (m) => {
          if (m.type === "collection") {
            const collectionInfo = await getAllCollectionDetails({
              data: m,
              userId: String(userId),
              needCoverImages: true,
              needLikeStatus: true,
              needProgress: true,
            });
            return {
              ...m,
              ...collectionInfo,
            };
          } else {
            const videoInfo = await getAllVideoDetails({
              needProgress: true,
              needThumbnail: true,
              needLikeStatus: true,
              needCreator: true,
              userId: String(userId),
              data: m,
            });

            return {
              ...m,
              ...videoInfo,
            };
          }
        })
      );

      const filtered = data.filter((f) => f !== null);

      res.status(200).json({
        data: filtered,
        message: "fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in dashboard/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
