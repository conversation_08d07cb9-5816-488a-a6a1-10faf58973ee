import { Transaction } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

const STRIPE_KEY = process.env.STRIPE_KEY;
const stripe = require("stripe")(STRIPE_KEY);

async function getPaymentDetails(paymentId) {
  if (!paymentId) {
    return {};
  }
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentId);

    if (!paymentIntent.payment_method) {
      return null;
    }

    const paymentMethod = await stripe.paymentMethods.retrieve(
      paymentIntent.payment_method
    );

    const paymentDetails = {
      paymentType: paymentMethod.type,
      type: "",
      identifier: "",
    };

    switch (paymentMethod.type) {
      case "card":
        paymentDetails.type = paymentMethod.card.brand.toUpperCase();
        paymentDetails.identifier = paymentMethod.card.last4;
        break;

      case "sepa_debit":
        paymentDetails.type = "SEPA_DEBIT";
        paymentDetails.identifier = paymentMethod.sepa_debit.last4;
        break;

      case "paypal":
        paymentDetails.type = "PAYPAL";
        paymentDetails.identifier =
          paymentMethod.paypal.payer_email || "PayPal Account";
        break;

      case "afterpay_clearpay":
        paymentDetails.type = "AFTERPAY";
        paymentDetails.identifier =
          paymentMethod.afterpay_clearpay?.email || "Afterpay Account";
        break;

      case "alipay":
        paymentDetails.type = "ALIPAY";
        paymentDetails.identifier =
          paymentMethod.alipay?.fingerprint || "Alipay Account";
        break;

      case "apple_pay":
        paymentDetails.type = "APPLE PAY";
        paymentDetails.identifier = paymentMethod.card.last4;
        break;

      case "google_pay":
        paymentDetails.type = "GOOGLE PAY";
        paymentDetails.identifier = paymentMethod.card.last4;
        break;

      default:
        paymentDetails.type = paymentMethod.type.toUpperCase();
        paymentDetails.identifier = paymentMethod.id;
        break;
    }

    return paymentDetails;
  } catch (error) {
    console.error("Error retrieving payment details:", error);
    return {
      paymentType: "",
      type: "",
      identifier: "",
    };
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { id } = req.query;
      const transaction = await Transaction.findById(id)
        .populate("eventIds")
        .populate("customProductsDetails.customProductsInfo")
        .populate([
          {
            path: "clubsDetails",
            populate: {
              path: "clubInfo",
            },
          },
        ]);
      if (!transaction) {
        res.status(404).json({ message: "Transaction not found" });
        return;
      }
      const poaymentId = transaction.paymentIntentId;
      const paymentDetails = await getPaymentDetails(poaymentId);

      res.status(200).json({
        data: {
          ...transaction.toObject(),
          paymentDetails,
        },
        message: "Transaction fetched successfully",
        success: false,
      });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in payment/transaction/:id due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
