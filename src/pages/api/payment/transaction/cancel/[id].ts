import { Transaction } from "@/api/mongo";
import { stripe } from "@/api/stripe";
import { PAYMENT_MODE, PAYMENT_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { id } = req.query;
      const userId = req.headers.userid;

      if (!id) {
        return res.status(400).json({
          message: `transactionId is required`,
          success: false,
          data: null,
        });
      }

      const transactionDetails = await Transaction.findByIdAndUpdate(
        id,
        {
          status: PAYMENT_STATUS.CANCELLED,
        },
        { new: true }
      );
      if (transactionDetails) {
        res.status(200).json({
          message: `Cancelled payment`,
          data: transactionDetails,
          success: true,
        });
      } else {
        res.status(400).json({
          message: `Invalid transaction`,
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in payment/cancel-club-sub due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
