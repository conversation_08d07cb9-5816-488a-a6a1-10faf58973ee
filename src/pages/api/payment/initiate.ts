import { Cart, Transaction, User } from "@/api/mongo";
import { createStripeCustomer, makeStripePayment } from "@/api/stripe";
import { CURRENCY_ENUM, PAYMENT_MODE, PAYMENT_STATUS } from "@/constant/Enums";
import { AMOUNT_CONVERTOR } from "@/utils/classes";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const {
        emailId,
        price = 200,
        // plans = [],
        productData = [],
        clubsDetails = [],
        classesDetails = [],
        cartId = [],
        eventId = [],
        eventsPriceDetails = [],
        currency,
        customProductsDetails,
      } = req.body;
      const userId = req.headers.userid;
      const finalAmount = price * AMOUNT_CONVERTOR;

      let cartIds = [...cartId];

      if (cartId?.length === 0) {
        const formattedClassessId =
          classesDetails.length > 0 ? classesDetails[0]?.classInfo : null;

        const formattedPlans =
          classesDetails.length > 0 ? classesDetails[0]?.plans : [];

        const formattedMemberships =
          clubsDetails.length > 0 ? clubsDetails[0]?.memberships : [];

        const formattedClubId =
          clubsDetails.length > 0 ? clubsDetails[0]?.clubInfo : null;

        const payload = {
          classesId: formattedClassessId,
          plans: formattedPlans,
          userId,
          eventId,
          isBuying: true,
          clubId: formattedClubId,
          memberships: formattedMemberships,
        };

        if (!payload.classesId) {
          delete payload.classesId;
        }
        if (!payload.clubId) {
          delete payload.clubId;
        }
        if (payload.plans.length === 0 || !payload.plans) {
          delete payload.plans;
        }
        if (payload.memberships.length === 0 || !payload.memberships) {
          delete payload.memberships;
        }
        if (payload.eventId.length === 0 || !payload.eventId) {
          delete payload.eventId;
        }
        const createdCart = await Cart.create(payload);
        cartIds = createdCart?._id ? [createdCart?._id] : [];
      }
      const transactionPayload = {
        userId,
        status: PAYMENT_STATUS.INITIATED,
        email: emailId,
        stripeCheckoutId: "",
        price: finalAmount,
        discount: 0,
        finalAmount: finalAmount - 0,
        transactionDate: new Date(),
        cartId: cartIds,
        eventIds: eventId,
        mode: PAYMENT_MODE.PAYMENT,
        clubsDetails,
        classesDetails,
        eventsPriceDetails,
        customProductsDetails,
      };
      if (!transactionPayload.customProductsDetails) {
        delete transactionPayload.customProductsDetails;
      }
      const transaction = await Transaction.create(transactionPayload);

      const transactionId = transaction._id;

      const user = await User.findById(userId);
      const isMexican = currency === CURRENCY_ENUM.MXN;
      let customerId = (() => {
        if (isMexican) {
          return user.stripeCustomerIdMxn ?? "";
        } else {
          return user.stripeCustomerId ?? "";
        }
      })();
      if (!customerId) {
        customerId = await createStripeCustomer({
          email: emailId,
          name: user.firstName,
          userId,
          isMexican,
        });
      }
      const lineItems = productData.map((product) => ({
        price_data: {
          currency,
          product_data: {
            name: product.name,
            images: product.images,
          },
          unit_amount: product.price * AMOUNT_CONVERTOR,
        },
        quantity: 1,
      }));
      const payment = await makeStripePayment({
        price: finalAmount,
        metadata: {
          // cartId: JSON.stringify(cartId),
          // classesId: JSON.stringify(classesId),
          // eventId: JSON.stringify(eventId),
          userId,
          emailId,
        },
        lineItems,
        email: emailId,
        customerId,
        success_url: `${process.env.NEXT_PUBLIC_BASE_URL}payment/${transactionId}`,
        cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}payment/${transactionId}`,
      });

      const paymentId = payment?.id;
      if (paymentId) {
        const url = payment?.url;
        const updatedTransaction = await Transaction.findByIdAndUpdate(
          transactionId,
          {
            stripeCheckoutId: paymentId,
          },
          { new: true }
        );
        res.status(201).json({
          message: `Initiated payment successfully`,
          data: {
            paymentUrl: url,
            transactionDetails: updatedTransaction,
          },
          success: true,
        });
      } else {
        await Transaction.findByIdAndUpdate(
          transactionId,
          {
            status: PAYMENT_STATUS.FAILED,
          },
          { new: true }
        );
        res.status(400).json({
          message: `Failed to initiate payment.Please try again`,
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in payment/initiate due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
