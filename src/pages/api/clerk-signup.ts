import { NextApiRequest, NextApiResponse } from "next";
import { createUserFromClerkData } from "@/api/mongoHelpers";
import { withSessionAPI } from "@/lib/auth/session";
import { sendEmail } from "@/api/sendEmail";
import { getWelcomeEmailBody } from "@/constant/email/WelcomeEmail";
import SignupNoticeEmail from "@/constant/email/SignupNoticeEmail";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { createStripeCustomer } from "@/api/stripe";
import { createContact as createBrevoContact } from "@/utils/brevo";
import { handleTranslate } from "@/utils/common";

export default withSessionAPI(async function createUserRoute(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === "POST") {
    const signupAuthHeader = req.headers["x-pf-signup-auth"];
    const authKey = process.env.CLERK_SIGNUP_WEBHOOK_AUTH_KEY;

    if (signupAuthHeader === authKey) {
      const {
        id: clerkId,
        email_addresses,
        first_name: firstName,
        last_name: lastName,
      } = req.body.data;

      if (
        !email_addresses ||
        email_addresses.length === 0 ||
        !email_addresses[0].email_address
      ) {
        return res
          .status(400)
          .json({ message: "No email address provided or incorrect format" });
      }

      const email = email_addresses[0].email_address;
      const verifiedfirstName = firstName ?? email?.split("@")[0];

      const clerkData = {
        email,
        clerkId,
        firstName: verifiedfirstName,
        lastName,
      };

      try {
        const userProfile = await createUserFromClerkData(clerkData);
        if (userProfile) {
          const mailBody = getWelcomeEmailBody({
            name: verifiedfirstName,
            isEnglish: true,
          });
          sendEmail({
            body: mailBody,
            subject: handleTranslate("email.we.welcome", true),
            to: [email],
          });

          /**
           * Send notification email to for this new sign up to email group.
           * "stakeholder"? Not the best choice of identifier name.
           */
          const stakeholdersMailAlias =
            process.env.NOTICE_EMAIL_ADDRESS || `<EMAIL>`;
          sendEmail({
            body: SignupNoticeEmail(
              `${verifiedfirstName} ${lastName ?? ""}`,
              email,
              new Date().toLocaleString()
            ),
            subject: "New User Signed up",
            to: [stakeholdersMailAlias],
          });

          // Create user contact on Brevo
          createBrevoContact(email, verifiedfirstName, lastName, {
            signup_date: new Date().toISOString(),
          });

          createStripeCustomer({
            email,
            name: verifiedfirstName,
            isCreatingAccount: true,
            userId: userProfile._id,
          });
        }
        res.status(200).json({ success: true, userProfile });
      } catch (error) {
        console.error("Error creating user profile:", error);
        const { message, status } = getMessageAndStatusCode(error);
        res.status(status).json({
          data: null,
          message: message,
          success: false,
        });
      }
    } else {
      res.status(401).end("Unauthorized");
    }
  } else {
    res.setHeader("Allow", ["POST"]);
    res.status(405).end("Method Not Allowed");
  }
});
