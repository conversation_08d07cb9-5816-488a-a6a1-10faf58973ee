import { Schedule } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { skip = 0, limit = 10 } = req.query;
      const schedules = await Schedule.find()
        .skip(+skip)
        .limit(+limit)
        .sort({ startDate: -1 });

      res.status(200).json({
        message: "Scheduled classes fetched successfully",
        data: schedules,
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in schedule/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
