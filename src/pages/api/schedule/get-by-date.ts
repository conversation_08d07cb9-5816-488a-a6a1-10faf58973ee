import { Schedule } from "@/api/mongo";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  const { startDate, endDate } = req.body;
  const userId = req.headers.userid;

  if (!startDate || !endDate) {
    res.status(400).json({
      data: null,
      message: "startDate and endDate cannot be empty",
      success: false,
    });
    return;
  }

  try {
    if (req.method === "POST") {
      const singleEvent = await Schedule.findOne({
        startDate: { $lte: new Date(String(startDate)) },
        endDate: { $gte: new Date(String(endDate)) },
      });
      if (singleEvent) {
        res.status(200).json({
          data: singleEvent,
          message: "Scheduled class fetched successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: `Something went wrong while fetching schedule using get-by-date `,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in schedule/get-by-date due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message ?? "Something went wrong",
      success: false,
    });
  }
}
