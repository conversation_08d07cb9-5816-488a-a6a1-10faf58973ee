import { Schedule } from "@/api/mongo";
import { FormatResponse, getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "PUT") {
      const { id, students = [], teachers = [], updateStudents } = req.body;
      const payload = { students, teachers };
      if (payload.teachers.length === 0) {
        delete payload.teachers;
      }

      if (!updateStudents) {
        delete payload.students;
      }

      const updatedSchedule = await Schedule.findByIdAndUpdate(id, payload, {
        new: true,
      });
      if (updatedSchedule) {
        res.status(200).json(
          FormatResponse({
            data: updatedSchedule,
            message: "Schedule updated successfully",
            success: true,
          })
        );
      } else {
        res.status(400).json(
          FormatResponse({
            data: null,
            message: "Failed to update Schedule",
            success: false,
          })
        );
      }
    } else {
      res.setHeader("Allow", ["PUT"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in schedule/update due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
