import { Schedule } from "@/api/mongo";
import { FormatResponse, getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const checkSchedule = await Schedule.findOne({
        startDate: req.body.startDate,
        endDate: req.body.endDate,
      });

      if (checkSchedule) {
        return res.status(200).json(
          FormatResponse({
            data: checkSchedule,
            message: "Scheduled already exists for this class",
            success: true,
          })
        );
      }

      const schedule = await Schedule.create(req.body);
      if (schedule) {
        res.status(201).json(
          FormatResponse({
            data: schedule,
            message: "Scheduled the class successfully",
            success: true,
          })
        );
      } else {
        res.status(400).json(
          FormatResponse({
            data: null,
            message: "Failed to schedule the class",
            success: false,
          })
        );
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in schedule/create due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
