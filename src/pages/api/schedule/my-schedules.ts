import { awsDownload } from "@/api/aws";
import { Schedule } from "@/api/mongo";
import {
  getAllEventDetails,
  handleEventDateQuery,
  handleScheduleDateQuery,
} from "@/api/mongoHelpers";
import { CLASSES_SORT, CLASSESS_FETCH_DURATION } from "@/constant/Enums";
import { dateAsPerTimeZone, getDateAsPerUTC } from "@/utils/dateTime";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import mongoose, { get } from "mongoose";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      let {
        skip = 0,
        limit = 10,
        timeFilter = CLASSESS_FETCH_DURATION.ALL,
        isMyClassesOnly = false,
        filters,
        isAscending,
        date,
        isSuggestions = false,
        currentDate,
        timezone,
        from = null,
        to = null,
      } = req.query;
      const userId = req.headers.userid;

      const formattedCurrentDate = getDateAsPerUTC(
        dateAsPerTimeZone({
          dateToChange: new Date(String(currentDate)),
          timeZone: String(timezone),
        }),
        true
      );

      const selectedDateAsUTC = (() => {
        if (!date) {
          return null;
        }
        return getDateAsPerUTC(
          dateAsPerTimeZone({
            dateToChange: new Date(String(date)),
            timeZone: String(timezone),
          }),
          true
        );
      })();

      const filterArray = String(filters)
        .split(",")
        .filter((f) => f.length > 0);

      const isCommunityExperience = filterArray.includes(
        CLASSES_SORT.COMMUNITY
      );
      const isInPersonClass = filterArray.includes(
        CLASSES_SORT.IN_PERSON_CLASS
      );

      if (date && date !== "undefined") {
        timeFilter = CLASSESS_FETCH_DURATION.CURRENT;
      }

      const typedUserId = new mongoose.Types.ObjectId(String(userId));
      const typedSkip = parseInt(skip as string, 10);
      const typedLimit = parseInt(limit as string, 10);
      const typedIsAscending = isAscending === "true" ? 1 : -1;
      const typedIsMyClassesOnly = isMyClassesOnly === "true";
      const typedIsSuggestions = isSuggestions === "true";
      const typedFrom = from ? String(from) : null;
      const typedTo = to ? String(to) : null;

      const commonProps = {
        type: +timeFilter,
        date: selectedDateAsUTC?.toISOString() ?? "",
        currentDate: formattedCurrentDate,
        timezone: String(timezone),
        from: typedFrom,
        to: typedTo,
      };

      const eventDateQuery = handleEventDateQuery(commonProps);
      const scheduleDateQuery = handleScheduleDateQuery(commonProps);

      console.log({
        eventDateQuery: JSON.stringify(eventDateQuery),
        scheduleDateQuery: JSON.stringify(scheduleDateQuery),
        formattedCurrentDate,
        date,
        timeFilter,
        typedIsSuggestions,
        typedIsMyClassesOnly,
        typedIsAscending,
        typedUserId,
        typedFrom,
        typedTo,
      });

      const schedulePipeline = [
        {
          $match: {
            $and: [
              ...(typedIsMyClassesOnly
                ? [{ students: { $elemMatch: { userId: typedUserId } } }]
                : []),
              scheduleDateQuery,
              ...(typedIsSuggestions
                ? [
                    {
                      students: { $exists: true, $not: { $size: 0 } },
                    },
                  ]
                : []),
            ],
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "teachers.teacherId",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  profileImageKey: 1,
                  profileImageId: 1,
                  _id: 1,
                },
              },
            ],
            as: "teacherDetails",
          },
        },
        {
          $addFields: {
            student: {
              $cond: {
                if: { $eq: [typedIsSuggestions, true] },
                then: { $first: "$students" },
                else: {
                  $first: {
                    $filter: {
                      input: "$students",
                      as: "s",
                      cond: { $eq: ["$$s.userId", typedUserId] },
                    },
                  },
                },
              },
            },
            isBought: {
              $gt: [
                {
                  $size: {
                    $filter: {
                      input: "$students",
                      as: "s",
                      cond: { $eq: ["$$s.userId", typedUserId] },
                    },
                  },
                },
                0,
              ],
            },
          },
        },
        {
          $lookup: {
            from: "transactions",
            let: { transactionId: "$student.transaction" },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ["$_id", "$$transactionId"] },
                },
              },
              {
                $unwind: {
                  path: "$classesDetails",
                  preserveNullAndEmptyArrays: true,
                },
              },
              {
                $replaceRoot: {
                  newRoot: { $ifNull: ["$classesDetails", {}] },
                },
              },
            ],
            as: "classDetails",
          },
        },
        {
          $addFields: {
            type: "schedule",
            data: {
              $mergeObjects: ["$$ROOT", { classDetails: "$classDetails" }],
            },
            transactionId: "$student.transaction",
            userId: "$student.userId",
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              type: "$type",
              data: "$data",
              transactionId: "$transactionId",
              userId: "$userId",
              isBought: "$isBought",
            },
          },
        },
        ...(isInPersonClass ? [{ $match: { "data.type": "IN_PERSON" } }] : []),
        ...(typedIsSuggestions ? [{ $match: { isBought: false } }] : []),
      ];

      const eventPipeline = (() => {
        console.log({
          typedIsSuggestions,
          typedIsMyClassesOnly,
        });
        const myClassesOnlyPipeline = [
          {
            $match: {
              userId: typedUserId,
              status: "SUCCESS",
            },
          },
          { $unwind: "$eventsPriceDetails" },

          // fetch event info
          {
            $lookup: {
              from: "events",
              let: { eventId: "$eventsPriceDetails.eventInfo" },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ["$_id", "$$eventId"] },
                  },
                },
                {
                  $lookup: {
                    from: "languageproficiencies",
                    localField: "proficiencyLevel",
                    foreignField: "_id",
                    as: "proficiencyLevel",
                  },
                },
                {
                  $unwind: {
                    path: "$proficiencyLevel",
                    preserveNullAndEmptyArrays: true,
                  },
                },
              ],
              as: "eventInfo",
            },
          },
          {
            $unwind: {
              path: "$eventInfo",
              preserveNullAndEmptyArrays: true,
            },
          },

          // fetch eventOn info from the exact session purchased
          {
            $lookup: {
              from: "eventons",
              localField: "eventsPriceDetails.eventOn",
              foreignField: "_id",
              as: "eventOnInfo",
            },
          },
          { $unwind: "$eventOnInfo" },

          // ✅ apply date filter here
          {
            $match: {
              ...eventDateQuery, // e.g. { "eventOnInfo.startDateTime": { $gte: new Date() } }
            },
          },

          // normalize shape
          {
            $replaceRoot: {
              newRoot: {
                type: "event",
                data: {
                  $mergeObjects: [
                    "$eventInfo",
                    {
                      purchasedPrice: "$eventsPriceDetails.price",
                      purchasedCurrency: "$eventsPriceDetails.currency",
                      eventOn: "$eventOnInfo",
                      startDateTime: "$eventOnInfo.startDateTime",
                      endDateTime: "$eventOnInfo.endDateTime",
                      timezone: "$eventOnInfo.timezone",
                    },
                  ],
                },
                transactionId: "$_id",
                userId: "$userId",
                isBought: true,
              },
            },
          },
        ];

        const suggestionsPipeline = [
          // break each Event by its eventOn references
          { $unwind: "$eventOn" },

          // fetch eventOn details
          {
            $lookup: {
              from: "eventons",
              localField: "eventOn",
              foreignField: "_id",
              as: "eventOnInfo",
            },
          },
          { $unwind: "$eventOnInfo" },

          // ✅ apply date filter directly on eventOnInfo
          {
            $match: {
              ...eventDateQuery, // should reference eventOnInfo.startDateTime / endDateTime
            },
          },

          // check if user already bought this event
          {
            $lookup: {
              from: "transactions",
              localField: "_id",
              foreignField: "eventsPriceDetails.eventInfo",
              pipeline: [
                {
                  $match: {
                    userId: typedUserId,
                    status: "SUCCESS",
                  },
                },
              ],
              as: "transactionInfo",
            },
          },

          // compute flags
          {
            $addFields: {
              transactionId: { $first: "$transactionInfo._id" },
              userId: { $first: "$transactionInfo.userId" },
              isBought: { $gt: [{ $size: "$transactionInfo" }, 0] },
            },
          },

          {
            $replaceRoot: {
              newRoot: {
                type: "event",
                data: {
                  $mergeObjects: [
                    "$$ROOT",
                    {
                      eventOn: "$eventOnInfo",
                      startDateTime: "$eventOnInfo.startDateTime",
                      endDateTime: "$eventOnInfo.endDateTime",
                      timezone: "$eventOnInfo.timezone",
                    },
                  ],
                },
                transactionId: "$transactionId",
                userId: "$userId",
                isBought: "$isBought",
              },
            },
          },

          ...(typedIsSuggestions ? [{ $match: { isBought: false } }] : []),
        ];

        return [
          {
            $unionWith: {
              coll: typedIsMyClassesOnly ? "transactions" : "events",
              pipeline: typedIsMyClassesOnly
                ? myClassesOnlyPipeline
                : suggestionsPipeline,
            },
          },
        ];
      })();

      const filteringPipeline = [
        ...(filterArray.length > 0 &&
        !(isInPersonClass && isCommunityExperience)
          ? [
              {
                $match: {
                  type: {
                    $in: [
                      ...(isInPersonClass ? ["schedule"] : []),
                      ...(isCommunityExperience ? ["event"] : []),
                    ],
                  },
                },
              },
            ]
          : []),
        { $skip: typedSkip },
        { $limit: typedLimit },
        {
          $sort: {
            startDateTime: typedIsAscending,
            "data.createdAt": typedIsAscending,
          },
        },
      ];

      const pipeline: any[] = [
        // Schedule branch
        ...schedulePipeline,
        // Event branch
        ...eventPipeline,
        // Filter
        ...filteringPipeline,
      ];

      const schedules = await Schedule.aggregate(pipeline);

      const formattedSchedules = schedules
        .map((m) => {
          if (m.type === "schedule") {
            const scheduledPlan = m?.data?.student?.selectedPlan;
            const boughtClassess = m?.data?.classDetails;

            if (scheduledPlan) {
              const foundClass = boughtClassess.find((f) =>
                f.plans.some((p) => p.planId === scheduledPlan.planId)
              );

              return {
                ...m,
                data: {
                  ...m.data,
                  classInfo: foundClass?.classInfo,
                },
              };
            } else {
              console.log("formattedSchedules m", m);
              return null;
            }
          } else {
            return m;
          }
        })
        .filter((m) => m);

      const data = await Promise.all(
        formattedSchedules.map(async (m) => {
          if (m.type === "event") {
            const eventsInfo = await getAllEventDetails({
              needEventImage: true,
              data: m.data,
            });
            return {
              ...m,
              data: eventsInfo,
            };
          }
          return m;
        })
      );

      const teachersWithImages = await Promise.all(
        data.map(async (m) => {
          if (m.type === "schedule") {
            const teacherDetails = m.data.teacherDetails ?? [];
            const teachersWithImage = await Promise.all(
              teacherDetails.map(async (t) => {
                try {
                  t = t.toObject();
                } catch (e) {}
                if (t?.profileImageId && t?.profileImageKey) {
                  const profileImageUrl = await awsDownload(t?.profileImageKey);
                  return {
                    ...t,
                    profileImageUrl: profileImageUrl?.data ?? "",
                  };
                }
                return {
                  ...t,
                  profileImageUrl: "",
                };
              })
            );
            return {
              ...m,
              data: {
                ...m.data,
                teacherDetails: teachersWithImage,
              },
            };
          }
          return m;
        })
      );

      res.status(200).json({
        message: "Scheduled classes fetched successfully",
        data: teachersWithImages,
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(
      `Something went wrong in schedule/my-schedules due to`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
