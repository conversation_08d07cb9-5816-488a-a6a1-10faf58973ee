import {
  VideoProgress,
  VideoCollectionProgress,
  VideoCollection,
} from "@/api/mongo";
import {
  getAllCollectionDetails,
  getAllVideoDetails,
} from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import mongoose from "mongoose";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method !== "GET") {
      res.setHeader("Allow", ["GET"]);
      return res.status(405).end("Method Not Allowed");
    }

    const {
      skip = 0,
      limit = 10,
      search = "",
      status = "",
      type = "",
    } = req.query;

    const userId = req.headers.userid;
    if (!userId) throw new Error("UserId header missing");

    const typedUserId = new mongoose.Types.ObjectId(String(userId));

    const statusArray =
      typeof status === "string" && status.length > 0
        ? status.split(",").map((s) => +s)
        : [];

    const pipelineArray: any[] = (() => {
      const videoCollectionProgressCollectionName =
        VideoCollectionProgress.collection.name;
      const videoCollectionName = VideoCollection.collection.name;

      const pipeline: any[] = [];

      pipeline.push({ $match: { userId: typedUserId } });

      pipeline.push({
        $lookup: {
          from: "videos",
          let: { vid: "$videoId" },
          pipeline: [
            { $match: { $expr: { $eq: ["$_id", "$$vid"] } } },
            {
              $lookup: {
                from: "languageproficiencies",
                let: { prof: "$proficiencyLevel" },
                pipeline: [
                  { $match: { $expr: { $eq: ["$_id", "$$prof"] } } },
                  { $limit: 1 },
                ],
                as: "proficiencyLevel",
              },
            },
            {
              $addFields: {
                proficiencyLevel: { $arrayElemAt: ["$proficiencyLevel", 0] },
              },
            },
            { $limit: 1 },
          ],
          as: "videoId",
        },
      });

      // collapse array into single object (or null if no match)
      pipeline.push({
        $addFields: { videoId: { $arrayElemAt: ["$videoId", 0] } },
      });

      // mark type
      pipeline.push({
        $addFields: {
          type: "video",
          createdAt: { $ifNull: ["$createdAt", new Date(0)] },
        },
      });
      pipeline.push({
        $addFields: {
          type: "video",
          createdAt: { $ifNull: ["$createdAt", new Date(0)] },
        },
      });

      pipeline.push({
        $unionWith: {
          coll: videoCollectionProgressCollectionName,
          pipeline: [
            { $match: { userId: typedUserId } },
            {
              $lookup: {
                from: videoCollectionName,
                localField: "collectionId",
                foreignField: "_id",
                as: "collectionData",
              },
            },
            {
              $addFields: {
                type: "collection",
                createdAt: { $ifNull: ["$createdAt", new Date(0)] },
                originalCollectionId: "$collectionId",
                collectionId: {
                  $cond: {
                    if: { $gt: [{ $size: "$collectionData" }, 0] },
                    then: { $arrayElemAt: ["$collectionData", 0] },
                    else: null,
                  },
                },
              },
            },
            {
              $unset: "collectionData",
            },
            {
              $lookup: {
                from: "languageproficiencies",
                localField: "collectionId.proficiencyLevel",
                foreignField: "_id",
                as: "collectionId.proficiencyLevel",
              },
            },
            {
              $unwind: {
                path: "$collectionId.proficiencyLevel",
                preserveNullAndEmptyArrays: true,
              },
            },
          ],
        },
      });

      if (search) {
        pipeline.push({
          $match: {
            $or: [
              { "videoId.title": { $regex: search, $options: "i" } },
              { "videoId.description": { $regex: search, $options: "i" } },
              { "collectionId.name": { $regex: search, $options: "i" } },
              { "collectionId.desc": { $regex: search, $options: "i" } },
            ],
          },
        });
      }

      if (statusArray.length > 0) {
        pipeline.push({ $match: { state: { $in: statusArray } } });
      }

      if (type) {
        pipeline.push({ $match: { type } });
      }

      pipeline.push({ $sort: { createdAt: -1 } });
      pipeline.push({ $skip: Number(skip) });
      pipeline.push({ $limit: Number(limit) });

      return pipeline;
    })();

    let learning = await VideoProgress.aggregate(pipelineArray);

    learning = await Promise.all(
      learning.map(async (item) => {
        try {
          if (item.type === "collection" && item.collectionId) {
            const collectionInfo = await getAllCollectionDetails({
              data: item.collectionId,
              userId: String(userId),
              needCoverImages: true,
              needLikeStatus: true,
              needProgress: true,
            });
            return { ...item, collectionId: collectionInfo };
          } else if (item.type === "video" && item.videoId) {
            const videoInfo = await getAllVideoDetails({
              needProgress: true,
              needLikeStatus: true,
              needCreator: true,
              userId: String(userId),
              data: item.videoId,
              needThumbnail: true,
            });
            return { ...item, videoId: videoInfo };
          }
          console.log("Skipping item:", {
            type: item.type,
            hasCollectionId: !!item.collectionId,
            hasVideoId: !!item.videoId,
            originalCollectionId: item.originalCollectionId,
            itemKeys: Object.keys(item),
          });
          return null;
        } catch (error) {
          console.error("Error processing item:", item, error);
          return null;
        }
      })
    );

    const filtered = learning.filter((item) => !!item);

    res.status(200).json({
      data: filtered,
      message: "fetched successfully",
      success: true,
    });
  } catch (error) {
    console.error(`Something went wrong in /learning/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
