import { seedCategories } from "@/api/seed/seedCategories";
import { seedClasses } from "@/api/seed/seedClasses";
import { seedCountries } from "@/api/seed/seedCountries";
import { seedGoals } from "@/api/seed/seedGoals";
import { seedInterest } from "@/api/seed/seedInterest";
import { seedLanguages } from "@/api/seed/seedLanguages";
import { SEEDING_ENUMS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const { name } = req.query;
      if (name === SEEDING_ENUMS.ALL) {
        await seedLanguages();
        await seedInterest();
        await seedGoals();
        await seedCountries();
        await seedCategories();
        await seedClasses();
        return res.status(200).json({ success: true });
      }
      if (name === SEEDING_ENUMS.COUNTRIES) {
        await seedCountries();
        return res.status(200).json({ success: true });
      }
      if (name === SEEDING_ENUMS.CATEGORIES) {
        await seedCategories();
        return res.status(200).json({ success: true });
      }
      if (name === SEEDING_ENUMS.GOALS) {
        await seedGoals();
        return res.status(200).json({ success: true });
      }
      if (name === SEEDING_ENUMS.INTERESTS) {
        await seedInterest();
        return res.status(200).json({ success: true });
      }
      if (name === SEEDING_ENUMS.LANGUAGES) {
        await seedLanguages();
        return res.status(200).json({ success: true });
      }
      if (name === SEEDING_ENUMS.CLASSES) {
        await seedClasses();
        return res.status(200).json({ success: true });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in seed due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
