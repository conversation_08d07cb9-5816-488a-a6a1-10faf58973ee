import {
  Category,
  ClassDetails,
  Country,
  Goal,
  Interest,
  Language,
} from "@/api/mongo";
import { SEEDING_ENUMS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const { type } = req.query;
      const { data } = req.body;

      if (!Array.isArray(data)) {
        res.status(400).json({
          message: `data can be only of type array`,
        });
        return;
      }

      if (type === SEEDING_ENUMS.CATEGORIES) {
        await Category.insertMany(data);
        res.json({
          message: "Categories inserted successfully",
        });
        return;
      } else if (type === SEEDING_ENUMS.LANGUAGES) {
        await Language.insertMany(data);
        res.json({
          message: "Languages inserted successfully",
        });
        return;
      } else if (type === SEEDING_ENUMS.INTERESTS) {
        await Interest.insertMany(data);
        res.json({
          message: "Interests inserted successfully",
        });
        return;
      } else if (type === SEEDING_ENUMS.GOALS) {
        await Goal.insertMany(data);
        res.json({
          message: "Goals inserted successfully",
        });
        return;
      } else if (type === SEEDING_ENUMS.COUNTRIES) {
        await Country.insertMany(data);
        res.json({
          message: "Countries inserted successfully",
        });
        return;
      } else if (type === SEEDING_ENUMS.CLASSES) {
        await ClassDetails.insertMany(data);
        res.json({
          message: "ClassDetails inserted successfully",
        });
        return;
      } else {
        res.status(409).json({
          message: `Invalid type.Allowed types can be found in the constant folder in Enums.ts file`,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in seed due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
