import { Event, Transaction } from "@/api/mongo";
import { PAYMENT_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "DELETE") {
      const { id } = req.query;

      const eventId = id.toString();
      const count = await Transaction.countDocuments({
        status: "SUCCESS",
        $or: [
          { eventIds: { $in: [eventId] } },
          { "eventsPriceDetails.eventInfo": { $in: [eventId] } },
        ],
      });

      if (count > 0) {
        return res.status(409).json({
          data: null,
          message: `You cannot delete this event as it has been booked by ${count} users`,
          success: false,
        });
      }

      const createEvent = await Event.findByIdAndDelete(id);
      if (createEvent) {
        res.status(201).json({
          data: createEvent,
          message: "Event deleted successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Something went wrong while deleting event",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["DELETE"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in event/delete due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
