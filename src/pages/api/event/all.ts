import { Event } from "@/api/mongo";
import { getAllEventDetails } from "@/api/mongoHelpers";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        needEventImage = false,
        search,
        fetchAll,
        targetLanguage,
      } = req.query;

      const userId = req.headers.userid;
      const fetchAllEvents = Boolean(fetchAll);

      let pipeline: any[] = [
        {
          $match: {
            title: search
              ? { $regex: search, $options: "i" }
              : { $exists: true },
          },
        },
        { $sort: { createdAt: -1 } },
        { $skip: +skip },
        { $limit: +limit },
      ];

      if (!fetchAllEvents) {
        pipeline.push(
          {
            $lookup: {
              from: "eventons",
              localField: "eventOn",
              foreignField: "_id",
              as: "eventOn",
            },
          },
          {
            $match: {
              eventOn: {
                $elemMatch: {
                  endDateTime: { $gte: new Date() },
                },
              },
            },
          }
        );
      } else {
        pipeline.push(
          { $unwind: "$eventOn" },
          {
            $lookup: {
              from: "eventons",
              localField: "eventOn",
              foreignField: "_id",
              as: "eventOn",
            },
          },
          { $unwind: "$eventOn" }
        );
      }

      pipeline.push(
        {
          $lookup: {
            from: "transactions",
            let: { eventId: "$_id", eventOns: "$eventOn" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$status", "SUCCESS"] },
                      {
                        $in: [
                          "$$eventId",
                          {
                            $ifNull: [
                              {
                                $map: {
                                  input: "$eventsPriceDetails",
                                  as: "epd",
                                  in: "$$epd.eventInfo",
                                },
                              },
                              [],
                            ],
                          },
                        ],
                      },
                    ],
                  },
                },
              },
              {
                $project: {
                  pairs: {
                    $map: {
                      input: "$eventsPriceDetails",
                      as: "epd",
                      in: {
                        k: { $toString: "$$epd.eventOn" },
                        v: 1,
                      },
                    },
                  },
                },
              },
              { $unwind: "$pairs" },
              {
                $group: {
                  _id: "$pairs.k",
                  count: { $sum: "$pairs.v" },
                },
              },
            ],
            as: "purchases",
          },
        },
        {
          $addFields: {
            boughtCount: {
              $arrayToObject: {
                $map: {
                  input: {
                    $filter: {
                      input: "$purchases",
                      as: "p",
                      cond: { $ne: ["$$p._id", null] },
                    },
                  },
                  as: "p",
                  in: { k: { $toString: "$$p._id" }, v: "$$p.count" },
                },
              },
            },
          },
        }
      );

      pipeline.push(
        {
          $lookup: {
            from: "languageproficiencies",
            localField: "proficiencyLevel",
            foreignField: "_id",
            as: "proficiencyLevel",
          },
        },
        {
          $unwind: {
            path: "$proficiencyLevel",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "languages",
            localField: "targetLanguage",
            foreignField: "_id",
            as: "targetLanguage",
          },
        },
        {
          $unwind: {
            path: "$targetLanguage",
            preserveNullAndEmptyArrays: true,
          },
        }
      );

      if (targetLanguage) {
        pipeline.push({
          $match: {
            "targetLanguage.nameInEnglish": {
              $regex: new RegExp(`^${targetLanguage}$`, "i"),
            },
          },
        });
      }

      let events = await Event.aggregate(pipeline);

      if (needEventImage) {
        events = await Promise.all(
          events.map(async (m) => {
            const eventsInfo = await getAllEventDetails({
              needEventImage: Boolean(needEventImage),
              data: m,
            });
            return eventsInfo;
          })
        );
      }

      if (events) {
        res.status(200).json({
          message: "Events fetched successfully",
          data: events,
          success: true,
        });
      } else {
        res.status(404).json({
          message: "Something went wrong while fetching events",
          data: null,
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in events/all due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
