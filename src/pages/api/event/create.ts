import { Event, EventOn } from "@/api/mongo";
import { combineDateNTime } from "@/utils/dateTime";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const {
        title,
        description,
        mode,
        proficiencyLevel,
        categories,
        location,
        url,
        price,
        currency,
        maxNumberOfRegistrations,
        targetLanguage,
        eventOn,
        imagesKeysAndIds,
      } = req.body;

      const createEvent = await Event.create({
        title,
        description,
        mode,
        proficiencyLevel,
        categories,
        location,
        url,
        price,
        currency,
        maxNumberOfRegistrations,
        targetLanguage,
        imagesKeysAndIds
      });

      if (createEvent) {
        const formattedEventOn = eventOn.map((m) => ({
          ...m,
          startDateTime: combineDateNTime({
            date: m.startDate,
            time: m.startTime,
            timezone: m.timezone,
          }),
          endDateTime: combineDateNTime({
            date: m.endDate,
            time: m.endTime,
            timezone: m.timezone,
          }),
          eventId: createEvent._id,
        }));

        const eventsOnIds = await handleInsertEventOn({
          eventsOn: formattedEventOn,
        });

        createEvent.eventOn = eventsOnIds;
        await createEvent.save();

        res.status(201).json({
          data: createEvent,
          message: "Event created successfully",
          success: true,
        });
      } else {
        res.status(400).json({
          data: null,
          message: "Something went wrong while creating event",
          success: false,
        });
      }
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in event/create due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}

const handleInsertEventOn = async ({ eventsOn }) => {
  try {
    const newEventsOn = await EventOn.insertMany(eventsOn);
    return newEventsOn.map((m) => m._id);
  } catch (error) {
    console.error(`Something went wrong in handleInsertEventOn due to`, error);
    throw error;
  }
};
