import BusinessEnglishBenefits from "@/components/classes/BusinessEnglish/BusinessEnglishBenefits";
import BusinessEnglishCard from "@/components/classes/BusinessEnglish/BusinessEnglishCard";
import CTA from "@/components/classes/BusinessEnglish/CTA";
import ClassBanner from "@/components/classes/ClassBanner";
import SectionHeader from "@/components/classes/SectionHeader";
import SEO from "@/components/SEO";
import { englishBusinessCompanyBenfits } from "@/constant/classes/englishBusinessData";
import { Metadata } from "@/constant/seoData";
import useTranslate from "@/hooks/useTranslate";
import { Box, Button, Container, Typography } from "@mui/material";
import React from "react";
import sectionImage1 from "@/../public/images/classes/business-english/para-tu-preparacion-individual/one.webp";
import { useRouter } from "next/router";
import useRedirectToClass from "@/hooks/useRedirectToClass";

const IndividualBusinessClass = () => {
  useRedirectToClass({
    requiredLangauge: "es",
  });
  const { translate } = useTranslate();

  const handleSendMessage = () => {
    const message =
      "¡Hola, Patito Feo! Quisiera más información para las Business English Classes.”";
    const NEXT_PUBLIC_WHATSAPP_PHONE = process.env.NEXT_PUBLIC_WHATSAPP_PHONE;
    const url = `https://wa.me/${NEXT_PUBLIC_WHATSAPP_PHONE}?text=${message}`;
    window.open(url, "_blank");
  };

  return (
    <>
      <SEO
        title={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.title}
        description={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.description}
        url={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.url}
        keywords={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.keywords.join(",")}
      />
      <ClassBanner text={translate("class.bec")} />
      <Container maxWidth="lg" sx={{ textAlign: "center" }}>
        <SectionHeader
          data={{
            image: sectionImage1.src,
          }}
        >
          <Box
            sx={{
              p: 4,
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Typography fontWeight={700} textAlign="left" fontSize="2.25rem">
              {translate("cei.title")}
            </Typography>
            <Typography
              textAlign="left"
              fontWeight={700}
              fontSize="1.75rem"
              mb={2}
            >
              {translate("cei.subtitle")}
            </Typography>
            <Typography fontSize="1.2rem" textAlign="left">
              {translate("cei.description")}
            </Typography>
          </Box>
        </SectionHeader>

        <BusinessEnglishBenefits
          sx={{ mt: 20 }}
          title="cei.benefits.main-title"
          description="cei.benefits.main-subtitle"
          data={englishBusinessCompanyBenfits}
        />

        <CTA
          title={translate("cei.bottom-title")}
          description={translate("cei.bottom-description")}
          ctaButton={translate("cei.bottom-buttonText")}
        />
      </Container>
    </>
  );
};

export default IndividualBusinessClass;
