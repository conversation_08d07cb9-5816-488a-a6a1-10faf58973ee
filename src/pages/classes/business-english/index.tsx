import ClassBanner from "@/components/classes/ClassBanner";
import SectionHeader from "@/components/classes/SectionHeader";
import SEO from "@/components/SEO";
import { englishBusinessClassesCoreInfo } from "@/constant/classes/englishBusinessData";
import { Metadata } from "@/constant/seoData";
import useTranslate from "@/hooks/useTranslate";
import { Box, Container, Typography } from "@mui/material";
import React from "react";
import sectionImage1 from "@/../public/images/classes/business-english/main.webp";
import Link from "next/link";
import CustomButton from "@/components/CustomButton";
import { useRouter } from "next/router";
import useRedirectToClass from "@/hooks/useRedirectToClass";

const BusinessEnglish = () => {
  const { translate } = useTranslate();
  useRedirectToClass({
    requiredLangauge: "es",
  });

  return (
    <>
      <SEO
        title={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.title}
        description={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.description}
        url={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.url}
        keywords={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.keywords.join(",")}
      />
      <ClassBanner text={translate("class.bec")} />
      <Box
        sx={{
          background: "#14A79C",
          width: "100%",
          py: 6,
          pb: 10,
        }}
      >
        <Container maxWidth="lg" sx={{ textAlign: "center" }}>
          <SectionHeader index={1} data={{ image: sectionImage1.src }}>
            <Box sx={{ height: "100%", width: "100%", p: 6 }}>
              <Typography
                fontSize="2.75rem"
                textAlign="left"
                mb={4}
                fontWeight={700}
                color="#fff"
              >
                {translate("ci.title")}
              </Typography>

              {englishBusinessClassesCoreInfo.map((m) => (
                <TitleDescription
                  key={m.id}
                  title={translate(m.title)}
                  description={translate(m.description)}
                />
              ))}

              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 2,
                  background: "#d1d0d036",
                  borderRadius: 6,
                  p: 6,
                  mt: 6,
                }}
              >
                <Typography
                  textAlign="left"
                  color="#fff"
                  fontSize="1.1rem"
                  fontWeight="700"
                >
                  {translate("ci.option")}
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "row",
                    gap: 4,
                    mt: 2,
                  }}
                >
                  <Link href="/classes/business-english/para-tu-empresa">
                    <CustomButton
                      sx={{ width: "fit-content", px: 6 }}
                      text={translate("ci.button-1")}
                    />
                  </Link>
                  <Link href="/classes/business-english/para-tu-preparacion-individual">
                    <CustomButton
                      text={translate("ci.button-2")}
                      sx={{ width: "fit-content", px: 6 }}
                    />
                  </Link>
                </Box>
              </Box>
            </Box>
          </SectionHeader>
        </Container>
      </Box>
    </>
  );
};

export default BusinessEnglish;

type TitleDescriptionProps = React.FC<{
  title: string;
  description: string;
}>;
const TitleDescription: TitleDescriptionProps = ({ title, description }) => {
  return (
    <Box
      sx={{
        py: 2,
        display: "flex",
        flexDirection: "column",
        textAlign: "start",
      }}
    >
      <Typography color="#fff" fontSize="1.1rem" fontWeight="700">
        {title}
      </Typography>
      <Typography fontSize="1rem" color="#fff">
        {description}
      </Typography>
    </Box>
  );
};
