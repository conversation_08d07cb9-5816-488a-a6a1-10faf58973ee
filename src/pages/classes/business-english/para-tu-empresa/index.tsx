import ClassBanner from "@/components/classes/ClassBanner";
import useTranslate from "@/hooks/useTranslate";
import { Container } from "@mui/material";
import { useRouter } from "next/router";
import React from "react";
import { englishBusinessClassesBenefits } from "@/constant/classes/englishBusinessData";
import SectionHeader from "@/components/classes/SectionHeader";
import sectionImage1 from "@/../public/images/classes/business-english/para-tu-empresa/one.webp";

import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";
import BusinessEnglishBenefits from "@/components/classes/BusinessEnglish/BusinessEnglishBenefits";
import CTA from "@/components/classes/BusinessEnglish/CTA";
import useRedirectToClass from "@/hooks/useRedirectToClass";

const CompanyEnglishClasses = () => {
  const { translate } = useTranslate();

  useRedirectToClass({
    requiredLangauge: "es",
  });

  return (
    <>
      <SEO
        title={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.title}
        description={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.description}
        url={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.url}
        keywords={Metadata.ENGLISH_BUSINESS_CLASSES_PAGE.keywords.join(",")}
      />
      <ClassBanner text={translate("class.bec")} />
      <Container maxWidth="lg" sx={{ textAlign: "center" }}>
        <SectionHeader
          data={{
            title: "bec.main-title",
            subtitle: "bec.main-subtitle",
            body: [
              "bec.main-description-1",
              "bec.main-description-2",
              "bec.main-description-3",
            ],
            image: sectionImage1.src,
          }}
        />
        <BusinessEnglishBenefits
          title="bec.title"
          description="bec.desc"
          data={englishBusinessClassesBenefits}
        />

        {/* <Box my={16} display="flex" flexDirection="column" alignItems="center">
          <Typography fontWeight={700} fontSize="2rem">
            {translate("bec.list-header")}
          </Typography>
          <Typography fontSize={"1.2rem"}>
            {translate("bec.list-desc")}
          </Typography>
          <Typography
            sx={{
              background: "#18ff5757",
              width: "fit-content",
              textAlign: "center",
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              borderRadius: 5,
              px: 2,
              py: 1,
              color: "green",
              fontSize: "0.85rem",
              my: 1,
            }}
          >
            <TranslateIcon sx={{ fontSize: "0.85rem", mx: 1 }} />
            {translate("bec.list-lang")}
          </Typography>
        </Box> */}

        <CTA
          title={translate("bec.different-program")}
          description={translate("bec.different-program-info")}
          ctaButton={translate("bec.different-program-button")}
        />
      </Container>
    </>
  );
};

export default CompanyEnglishClasses;
