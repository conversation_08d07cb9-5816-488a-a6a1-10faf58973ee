import React, { useState } from "react";
import { useRouter } from "next/router";
import { Box, Container, Typography } from "@mui/material";
import Banner from "../../../components/classes/Banner";
import ImageSection from "../../../components/classes/ImageSection";
import MissionSatement from "../../../components/classes/MissionSatement";
import { ImageSectionData } from "@/constant/classes/ClasesOnlineData";
import { MissionStatementData } from "../../../constant/patitoBasics/patitoBasics";
import ClassesAboutFooter from "@/components/classes/ClassesAboutFooter";
import backgroundImage from "../../../../public/images/classes/classdetailbanner.svg";
import { ImInButton } from "../../../components/classes/ImInButton";
import PricingCard from "@/components/PricingCard";
import PricingTab from "@/components/PricingTab";
import SectionHeader from "@/components/classes/SectionHeader";
import ClassesTopNav from "@/components/classes/ClassesTopNav";
import axios from "axios";
import {
  CLASSES_TYPE,
  IN_PERSON_TYPE,
  ONLINE_CLASSES_TABS,
  ONLINE_CLASSES_TYPE,
} from "@/constant/Enums";
import { useUserContext } from "@/contexts/UserContext";
import { useSnackbar } from "@/hooks/useSnackbar";
import ViewMoreButton from "@/components/classes/ViewMoreButton";
import { LONG_SESSIONS, QUICK_SESSIONS, REGULAR_SESSIONS } from "data/pricing";
import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";
import ClassBanner from "@/components/classes/ClassBanner";
import useTranslate from "@/hooks/useTranslate";

const tabs = ONLINE_CLASSES_TABS;

export async function getServerSideProps() {
  const res = await axios.get(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/classes/get`,
    {
      params: {
        type: CLASSES_TYPE.ONLINE,
      },
    }
  );
  const data = res.data.data;
  const regularSessionsData =
    data.filter((m) => m.subType === ONLINE_CLASSES_TYPE.REGULAR_SESSIONS) ??
    [];
  const quickSessionsData =
    data.filter((m) => m.subType === ONLINE_CLASSES_TYPE.QUICK_SESSIONS) ?? [];
  const longSessionsData =
    data.filter((m) => m.subType === ONLINE_CLASSES_TYPE.LONG_SESSIONS) ?? [];

  return {
    props: {
      regularSessionsData,
      quickSessionsData,
      longSessionsData,
    },
  };
}

const OnlineClases = ({
  regularSessionsData,
  quickSessionsData,
  longSessionsData,
}) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(tabs[0].id);
  const [data, setData] = useState(quickSessionsData);
  const { dbUser } = useUserContext();
  const { showSnackbar } = useSnackbar();
  const [isViewMore, setIsViewMore] = useState(false);
  const { translate } = useTranslate();

  const redirectToSignup = () => {
    const localKey = `REDIRECT`;
    localStorage.setItem(
      localKey,
      JSON.stringify({
        type: CLASSES_TYPE.ONLINE,
        id: null,
      })
    );
    router.push("/sign-up");
    showSnackbar(translate("common.create-new-acc"), {
      type: "warning",
    });
  };

  return (
    <>
      <SEO
        title={Metadata.ONLINE_CLASSESS_PAGE.title}
        description={Metadata.ONLINE_CLASSESS_PAGE.description}
        ogTitle={Metadata.ONLINE_CLASSESS_PAGE.og.title}
        ogDescription={Metadata.ONLINE_CLASSESS_PAGE.og.description}
        twitterDescription={Metadata.ONLINE_CLASSESS_PAGE.twitter.description}
        twitterTitle={Metadata.ONLINE_CLASSESS_PAGE.twitter.title}
        url={Metadata.ONLINE_CLASSESS_PAGE.url}
        keywords={Metadata.ONLINE_CLASSESS_PAGE.keywords.join(",")}
      />
      <ClassBanner text={translate("class.online")} />
      <Container maxWidth="lg" sx={{ textAlign: "center" }}>
        <SectionHeader data={ImageSectionData[0]} />
        {isViewMore && (
          <>
            {ImageSectionData.map(
              (m, i) => i !== 0 && <SectionHeader key={i} data={m} index={i} />
            )}
            <MissionSatement data={MissionStatementData} />
          </>
        )}

        <ViewMoreButton
          isViewMore={isViewMore}
          toggle={() => {
            setIsViewMore((prev) => !prev);
          }}
        />

        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
          mt={6}
        >
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="center"
            sx={{
              width: {
                xs: "100%",
                sm: "70%",
                md: "50%",
              },
            }}
          >
            <PricingTab
              tabs={tabs}
              active={activeTab}
              onClick={(tabData) => {
                setActiveTab(tabData.id);

                if (tabData.id === 1) {
                  setData(quickSessionsData);
                } else if (tabData.id === 2) {
                  setData(regularSessionsData);
                } else {
                  setData(longSessionsData);
                }
              }}
            />
          </Box>
        </Box>

        <Typography
          textAlign="center"
          mt={8}
          mb={10}
          sx={{ fontWeight: "500" }}
        >
          {translate(tabs.find((f) => f.id === activeTab)?.description)}
        </Typography>

        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
          flexWrap="wrap"
          gap={10}
        >
          {data?.map((m, i) => (
            <PricingCard
              key={m?._id}
              data={m}
              isLoggedIn={!!dbUser}
              redirectToSignup={redirectToSignup}
            />
          ))}
        </Box>

        <ClassesAboutFooter />
      </Container>
    </>
  );
};
export default OnlineClases;
