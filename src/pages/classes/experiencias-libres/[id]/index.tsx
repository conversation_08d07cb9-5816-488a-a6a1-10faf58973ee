import {
  Contain<PERSON>,
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Chip,
  SxProps,
  Theme,
  Card,
} from "@mui/material";
import Image from "next/image";
import TagComponent from "../../../../components/dashboard/TagComponent";
import { useRouter } from "next/router";
import img from "@/../../public/images/interestImages/danceClass.webp";
import Link from "next/link";
import { CommunityExperiencesPlans } from "@/../../src/constant/classes/pricing.js";
import axiosInstance from "@/utils/interceptor";
import {
  convertToTime,
  dateAsPerTimeZone,
  formatDate,
  formatTime,
  getDateAsPerUTC,
} from "@/utils/dateTime";
import { EventOnType, EventSchemaType } from "@/api/mongoTypes";
import CustomButton, { getBgColor } from "@/components/CustomButton";
import NoEvent from "@/components/Create/event/NoEvent";
import useCart from "@/hooks/useCart";
import usePayment from "@/hooks/usePayment";
import { useSnackbar } from "@/hooks/useSnackbar";
import { CLASSES_TYPE, CURRENCY_ENUM, EVENT_MODE_TYPE } from "@/constant/Enums";
import { useUserContext } from "@/contexts/UserContext";
import { useEffect, useMemo } from "react";
import { el } from "date-fns/locale";
import {
  getEventOnDetails,
  getLocalBuyEventDetails,
  getPriceSymbol,
  getRemainingSeats,
} from "@/utils/classes";
import { getProficiencyEn } from "@/utils/common";
import { EventSchemaWithSingleEvent } from "@/types";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import ShoppingBagIcon from "@mui/icons-material/ShoppingBag";
import HeaderImages from "@/components/classes/HeaderImages";
import useTranslate from "@/hooks/useTranslate";
import useRedirectToClass from "@/hooks/useRedirectToClass";

const pageContainerStyles = { marginTop: "65px" };
const imageContainerStyles = {
  position: "relative",
  width: "100%",
  height: "440px",
  marginBottom: "40px",
};
const imageStyles = {
  borderRadius: "33px",
  // objectFit: "contain",
  background: "gray",
};
const titleContainerStyles = {
  display: "flex",
  flexDirection: { xs: "column", sm: "row" },
  width: "100%",
  alignItems: { xs: "start", sm: "center" },
  justifyContent: "space-between",
  marginBottom: "20px",
};
const buttonStyles = {
  textTransform: "none",
  width: { xs: "100%", sm: "auto" },
};
const eventTitleStyles = { fontSize: "22px", fontWeight: "500" };
const eventDetailsContainerStyles = {
  border: "1px solid #E6E6E6",
  borderRadius: "19px",
  padding: "20px",
};
const labelContainerStyles = {
  display: "flex",
  flexDirection: { xs: "column", sm: "row" },
  marginBottom: { xs: "15px", sm: "35px" },
};
const firstLabelContainerStyles = {
  width: { xs: "auto", sm: "40%" },
  marginBottom: { xs: "15px", sm: "0px" },
};
const dividerStyles = { border: "1px solid #EAEAEA", marginBottom: "35px" };
const labelStyles = {
  fontSize: "16px",
  fontWeight: "700",
};
const dateStyles = { fontSize: "13px", fontWeight: "500", color: "#6D6D6D" };
const modeStyles = { ...dateStyles, textTransform: "uppercase" };
const tagStyles = {
  marginRight: "16px",
  backgroundColor: "#D7F7F5",
  color: "#14A79C",
  fontSize: "13px",
  fontWeight: "500",
};
const eventdescriptionStyles = {
  fontSize: "14px",
  fontWeight: "500",
  color: "#6D6D6D",
};
const linkStyles = { textDecoration: "none", color: "#6D6D6D" };

export async function getServerSideProps(context) {
  const { id } = context.params;
  const { data } = await axiosInstance.get(`event/get/${id}`, {
    params: {
      needEventImage: true,
    },
  });
  return {
    props: data,
  };
}

type EventPageProps = React.FC<{
  data: EventSchemaWithSingleEvent;
  isError: boolean;
  message: string;
}>;
const EventPage: EventPageProps = ({ data, isError, message }) => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const { dbUser } = useUserContext();
  const isLoggedIn = !!dbUser;
  const { id } = router.query;
  const { translate } = useTranslate();

  useRedirectToClass({
    requiredLangauge: "en",
  });

  const event = data;

  const redirectToSignup = () => {
    const localKey = `REDIRECT`;
    localStorage.setItem(
      localKey,
      JSON.stringify({
        type: CLASSES_TYPE.IN_PERSON,
        id,
      })
    );
    router.push("/sign-up");
    showSnackbar(translate("common.create-new-acc"), {
      type: "warning",
    });
  };

  if (!event || isError) {
    return <NoEvent />;
  }

  const isOnline = event.mode === EVENT_MODE_TYPE.ONLINE;

  const proficiency = getProficiencyEn({
    data: data?.proficiencyLevel,
  });

  return (
    <Container maxWidth="md" sx={pageContainerStyles}>
      <Box sx={imageContainerStyles}>
        {/* images should be 990 x 4400 */}
        <HeaderImages
          images={data.images.map((m) => m.url)}
          currency={data.currency}
          price={data.price}
        />
        {proficiency && (
          <Chip
            label={proficiency}
            sx={{
              position: "absolute",
              top: 15,
              left: 15,
              fontSize: 12,
              textTransform: "uppercase",
              padding: 0.2,
              borderRadius: 1,
              height: "auto",
              background: "rgba(0, 0, 0, 0.52)",
              color: "#fff",
              "& .MuiChip-label": {
                fontSize: 12,
                fontWeight: "normal",
                color: "#fff",
                padding: "0.2rem",
              },
            }}
          />
        )}
        <Box
          sx={{
            background: "rgba(0, 0, 0, 0.52)",
            color: "#fff",
            p: 2,
            borderRadius: 2,
            position: "absolute",
            bottom: 10,
            left: "1.5rem",
          }}
        >
          <Typography sx={{ fontSize: 14 }}>
            {getPriceSymbol({
              currency: data.currency,
            })}
            &nbsp;
            {data.price}
            <span style={{ fontSize: 12, fontWeight: 400 }}>
              &nbsp;{translate("ed.onwards")}
            </span>
          </Typography>
        </Box>
      </Box>
      <Box sx={eventDetailsContainerStyles}>
        <Box sx={titleContainerStyles}>
          <Typography sx={eventTitleStyles}>{event.title}</Typography>
        </Box>
        <hr style={dividerStyles}></hr>
        <Box sx={labelContainerStyles}>
          <Box sx={firstLabelContainerStyles}>
            <Typography sx={labelStyles}>{translate("ed.mode")}</Typography>
            <Typography sx={modeStyles}>
              {translate(isOnline ? "ed.online" : "ed.offline")}
            </Typography>
          </Box>
          <Box>
            <Typography sx={labelStyles}>
              {translate(isOnline ? "ed.url" : "ed.location")}
            </Typography>
            <Typography sx={dateStyles}>
              {isOnline ? (
                <Link
                  href={event.url}
                  style={linkStyles}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {event.url}
                </Link>
              ) : (
                event.location
              )}
            </Typography>
          </Box>
        </Box>
        <Box sx={labelContainerStyles}>
          <Box>
            <Typography sx={labelStyles}>
              {translate("ed.categories")}
            </Typography>
            <Box mt={2}>
              {event.categories.map((tag) => (
                <TagComponent label={tag} tagStyles={tagStyles} key={tag} />
              ))}
            </Box>
          </Box>
        </Box>
        <Box sx={labelContainerStyles}>
          <Box>
            <Typography sx={labelStyles}>{translate("ed.about")}</Typography>
            <Typography sx={eventdescriptionStyles}>
              {event.description}
            </Typography>
          </Box>
        </Box>
        <Box sx={labelContainerStyles}>
          <Box sx={{ width: "100%" }}>
            <Typography sx={labelStyles}>
              {translate("ed.date-n-time")}
            </Typography>
            <Box sx={{ width: "100%" }}>
              <EventDateInfo
                isLoggedIn={isLoggedIn}
                eventOn={data?.eventOn}
                data={data}
                event={event}
                redirectToSignup={redirectToSignup}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};
export default EventPage;

const EventDateInfo = ({
  eventOn,
  data,
  isLoggedIn,
  redirectToSignup,
  event,
}) => {
  const isEventsOnArray = Array.isArray(eventOn);

  if (
    isEventsOnArray &&
    eventOn &&
    eventOn[0] &&
    "startDateTime" in eventOn[0]
  ) {
    const eventsOn = eventOn as EventOnType[];
    return (
      <Box display="flex" flexDirection="column">
        {eventsOn.map((m, i) => (
          <DateRow
            eventsOn={m}
            key={i}
            data={data}
            isLoggedIn={isLoggedIn}
            redirectToSignup={redirectToSignup}
            event={event}
          />
        ))}
      </Box>
    );
  }

  const eventsOn = eventOn as EventOnType;
  return (
    <DateRow
      eventsOn={eventsOn}
      data={data}
      event={event}
      isLoggedIn={isLoggedIn}
      redirectToSignup={redirectToSignup}
    />
  );
};

type DateRowProps = React.FC<{
  eventsOn: EventOnType;
  data: EventSchemaWithSingleEvent;
  isLoggedIn: boolean;
  redirectToSignup?: () => void;
  event: EventSchemaWithSingleEvent;
}>;
const DateRow: DateRowProps = ({
  eventsOn,
  data,
  isLoggedIn,
  redirectToSignup,
  event,
}) => {
  const { startDate, sartTime, endDate, endTime, isStartEndDateSame } =
    useMemo(() => {
      return getEventOnDetails(eventsOn);
    }, [eventsOn]);

  const { isMaking, makePayment } = usePayment();
  const { handleAddToCart, isAddingToCart } = useCart({
    onDeleteSuccess: () => {},
    onAddingSuccess: () => {},
  });
  const { showSnackbar } = useSnackbar();
  const { translate } = useTranslate();

  const handleBuy = async () => {
    try {
      makePayment({
        cartId: [],
        price: data.price,
        productData: [
          {
            name: data.title,
            description: data.description,
            images: ["https://www.patitofeo.com/patitoB.png"],
            price: data.price,
          },
        ],
        eventId: [data._id],
        clubsDetails: [],
        classesDetails: [],
        eventsPriceDetails: [
          {
            eventInfo: String(data._id),
            price: +data.price,
            currency: data?.currency ?? CURRENCY_ENUM.USD,
            eventOn: eventsOn._id,
          },
        ],
        currency: data?.currency ?? CURRENCY_ENUM.USD,
      });
    } catch (error) {
      console.error("Something went wrong in handleBuy due to ", error);
      showSnackbar(translate("eci.failed-buy"), {
        type: "error",
      });
    }
  };
  const remaingCount = getRemainingSeats(data, eventsOn);

  useEffect(() => {
    if (event && isLoggedIn) {
      const buyData = getLocalBuyEventDetails();

      if (
        buyData?._id === event._id &&
        eventsOn._id === buyData?.eventOn?._id
      ) {
        handleBuy();
      }
    }
  }, [event, isLoggedIn]);

  return (
    <Card
      sx={{
        display: "flex",
        flexDirection: {
          xs: "column",
          sm: "row",
        },
        gap: 2,
        width: "100%",
        mt: 2,
        justifyContent: "space-between",
        p: 2,
        boxShadow: "0px 2px 10px 0px #0000000d;",
      }}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        gap={2}
      >
        <Typography fontSize="0.75rem">
          {formatDate({ date: new Date(startDate) })}
          {isStartEndDateSame ? (
            <>
              &nbsp; {sartTime} - {endTime}
            </>
          ) : (
            <>
              &nbsp; {sartTime} - {formatDate({ date: new Date(endDate) })}{" "}
              {endTime}
            </>
          )}
        </Typography>
        {typeof remaingCount === "number" && (
          <Box
            sx={{
              backgroundColor: "#f3b35854",
              padding: 1,
              borderRadius: 10,
              px: 3,
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <Typography fontSize="0.75rem" mb={0}>
              {translate("ed.available-spots")}
              <b>&nbsp;{remaingCount}</b>
            </Typography>
          </Box>
        )}
      </Box>
      <Box
        display="flex"
        alignItems="center"
        flexDirection="row"
        gap={2}
        sx={{
          justifyContent: {
            xs: "center",
            sm: "end",
          },
        }}
      >
        <Button
          sx={{
            ...buttonStyle,
            background: getBgColor("primary"),
            "&:hover": {
              background: getBgColor("primary"),
            },
          }}
          disabled={isAddingToCart || isMaking}
          onClick={(e) => {
            e.stopPropagation();
            handleAddToCart({
              eventId: isLoggedIn ? data?._id : data,
              eventOn: isLoggedIn ? eventsOn._id : eventsOn,
            });
          }}
        >
          <ShoppingCartIcon sx={{ height: 20, width: 20 }} />
          {translate("common.add-to-cart")}
        </Button>
        <Button
          sx={{
            ...buttonStyle,
            background: getBgColor("secondary"),
            "&:hover": {
              background: getBgColor("secondary"),
            },
          }}
          disabled={isMaking || isAddingToCart}
          onClick={(e) => {
            e.stopPropagation();
            if (isLoggedIn) {
              handleBuy();
            } else {
              localStorage.setItem("BUY_EVENT", JSON.stringify(data));
              redirectToSignup();
            }
          }}
        >
          <ShoppingBagIcon sx={{ height: 20, width: 20 }} />
          {translate("common.buy-now")}
        </Button>
      </Box>
    </Card>
  );
};

const buttonStyle = {
  fontSize: "0.85rem",
  display: "flex",
  gap: 2,
};
