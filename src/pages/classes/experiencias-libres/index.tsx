import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { Container, Box } from "@mui/material";
import { ImageSectionData } from "@/constant/classes/ExperienciasLibresData";
import { MissionStatementData } from "../../../constant/patitoBasics/patitoBasics";
import ClassesAboutFooter from "@/components/classes/ClassesAboutFooter";
import MissionSatement from "../../../components/classes/MissionSatement";
import SectionHeader from "@/components/classes/SectionHeader";
import EventCard from "@/components/Create/event/EventCard";
import InfiniteScroll from "react-infinite-scroll-component";
import axiosInstance from "@/utils/interceptor";
import useDebounce from "@/hooks/useDebounce";
import { useSnackbar } from "@/hooks/useSnackbar";
import ExperiencesLoading from "@/components/classes/ExperiencesLoading";
import { useUserContext } from "@/contexts/UserContext";
import ViewMoreButton from "@/components/classes/ViewMoreButton";
import { CLASSES_TYPE } from "@/constant/Enums";
import SEO from "@/components/SEO";
import { Metadata } from "@/constant/seoData";
import CreateCommunity from "@/components/classes/CreateCommunity";
import ClassBanner from "@/components/classes/ClassBanner";
import { EventSchemaWithSingleEvent } from "@/types";
import { getTargetLanguageFromCookieOrUser } from "@/utils/classes";
import useTranslate from "@/hooks/useTranslate";
import useRedirectToClass from "@/hooks/useRedirectToClass";

const LIMIT = 12;

const ExperienciasLibres = () => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState<EventSchemaWithSingleEvent[]>([]);
  const [search, setSearch] = useState("");
  const { dbUser, isfetching, isRetrying } = useUserContext();
  const [isViewMore, setIsViewMore] = useState(false);
  const { translate } = useTranslate();
  const debouncedSearchTerm = useDebounce(search, 1000);

  useRedirectToClass({
    requiredLangauge: "en",
  });

  const redirectToSignup = (id: string) => {
    const localKey = `REDIRECT`;
    localStorage.setItem(
      localKey,
      JSON.stringify({
        type: CLASSES_TYPE.COMMUNITY,
        id,
      })
    );
    router.push("/sign-up");
    showSnackbar(translate("common.create-new-acc"), {
      type: "warning",
    });
  };

  const fetchData = async ({ skipCount = 0, search = "" }) => {
    const showLoading = +skipCount === 0;
    if (skipCount === 0) {
      setData([]);
    }
    try {
      if (showLoading) {
        setIsLoading(true);
      }
      const language = getTargetLanguageFromCookieOrUser({ dbUser });
      const params = {
        needEventImage: true,
        skip: skipCount,
        limit: LIMIT,
        search,
        targetLanguage: language,
      };
      if (!search) {
        delete params.search;
      }
      if (!language) {
        delete params.targetLanguage;
      }
      const { data } = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/event/all`,
        {
          params,
        }
      );
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        showSnackbar("Failed to fetch Events", {
          type: "error",
        });
      }
      if (showLoading) {
        setIsLoading(false);
      }
    } catch (error) {
      console.error(`Something went wrong in fetchData due to ${error}`);
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    if (!isfetching && !isRetrying) {
      fetchData({ skipCount: 0, search: debouncedSearchTerm });
    }
  }, [debouncedSearchTerm, isfetching, isRetrying]);

  const handleClick = () => {
    router.push("/sign-up");
  };

  return (
    <>
      <SEO
        title={Metadata.COMMUNITY_CLASSES_PAGE.title}
        description={Metadata.COMMUNITY_CLASSES_PAGE.description}
        url={Metadata.COMMUNITY_CLASSES_PAGE.url}
        keywords={Metadata.COMMUNITY_CLASSES_PAGE.keywords.join(",")}
      />
      <ClassBanner text={translate("class.experience")} />
      <Container maxWidth="lg" sx={{ textAlign: "center" }}>
        <SectionHeader data={ImageSectionData[0]} />
        {isViewMore && (
          <>
            {ImageSectionData.map(
              (m, i) => i !== 0 && <SectionHeader key={i} data={m} index={i} />
            )}

            <CreateCommunity />
            <MissionSatement data={MissionStatementData} />
          </>
        )}

        <ViewMoreButton
          isViewMore={isViewMore}
          toggle={() => {
            setIsViewMore((prev) => !prev);
          }}
        />

        <Container
          sx={{
            display: "flex",
            flexDirection: "row",
            flexWrap: "wrap",
            alignItems: "center",
          }}
        >
          {isLoading ? (
            <ExperiencesLoading />
          ) : (
            <InfiniteScroll
              style={{
                width: "100%",
              }}
              dataLength={data.length}
              next={() => fetchData({ skipCount: skip, search: search })}
              hasMore={hasMore}
              loader={<p style={{ textAlign: "center" }}>Loading</p>}
              endMessage={
                <p style={{ textAlign: "center" }}>
                  {search.length > 0 ? (
                    <p>
                      No event with title <b>{search}</b>
                    </p>
                  ) : (
                    <b>Yay! You have seen it all</b>
                  )}
                </p>
              }
            >
              <Box
                display="flex"
                flexDirection="row"
                alignItems="center"
                justifyContent="center"
                flexWrap="wrap"
                gap={10}
                mt={10}
              >
                {data.map((m, i) => (
                  <EventCard
                    isAdmin={false}
                    key={m._id}
                    isLoggedIn={!!dbUser}
                    redirectToSignup={() => redirectToSignup(m._id)}
                    data={m}
                    onClick={() => {
                      router.push(`/classes/experiencias-libres/${m._id}`);
                    }}
                  />
                ))}
              </Box>
            </InfiniteScroll>
          )}
        </Container>

        <ClassesAboutFooter />
      </Container>
    </>
  );
};

export default ExperienciasLibres;
