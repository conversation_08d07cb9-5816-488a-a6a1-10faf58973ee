import {
  CartType,
  EventSchemaType,
  MembershipType,
  TransactionType,
} from "@/api/mongoTypes";
import CustomButton from "@/components/CustomButton";
import NoOrder from "@/components/payment/NoOrder";
import { CURRENCY_ENUM, PAYMENT_MODE, PAYMENT_STATUS } from "@/constant/Enums";
import { useUserContext } from "@/contexts/UserContext";
import usePayment from "@/hooks/usePayment";
import { useSnackbar } from "@/hooks/useSnackbar";
import { EventSchemaWithSingleEvent } from "@/types";
import {
  checkIfEveryCurrencySame,
  getCustomProductInfo,
  getFinalAmount,
  getItemsDetailsFromTransaction,
  getPriceSymbol,
  getProductData,
} from "@/utils/classes";
import { getLocalUser, getSingleEventOn } from "@/utils/common";
import { formatDate } from "@/utils/dateTime";
import { getTypeName } from "@/utils/format";
import axiosInstance from "@/utils/interceptor";
import {
  Box,
  Card,
  Container,
  Skeleton,
  SxProps,
  Theme,
  Typography,
} from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useMemo, useState } from "react";

export async function getServerSideProps(context) {
  const { id } = context.params;
  const { data } = await axiosInstance.get(`payment/transaction/${id}`);
  return {
    props: data,
  };
}

const CardStyle = {
  width: {
    xs: "100%",
    md: 450,
    sm: 350,
  },
  borderRadius: 2,
  p: 4,
  flexDirection: "column",
  display: "flex",
  border: "1px solid rgba(224, 224, 224, 1)",
  boxShadow: "none",
  alignItems: "center",
};

type CommonTransactionType = TransactionType & {
  paymentDetails: {
    type: string;
    paymentType: string;
    identifier: string;
  };
};

type PaymentPageProps = React.FC<{
  data: CommonTransactionType;
}>;
const Success: PaymentPageProps = ({ data }) => {
  const { dbUser } = useUserContext();
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const { isMaking, makePayment, isSubscribing, subscribeClub } = usePayment();
  const [email, setEmail] = useState("Your email");
  const [transactionDetails, setTransactionDetails] =
    useState<CommonTransactionType>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isSuccess = transactionDetails?.status === PAYMENT_STATUS.SUCCESS;
  const isCancelled = transactionDetails?.status === PAYMENT_STATUS.CANCELLED;
  const isSubscription = transactionDetails?.mode === PAYMENT_MODE.SUBSCRIPTION;

  const cartItems: CartType[] =
    transactionDetails?.cartId?.map((item) => item as CartType) ?? [];

  useEffect(() => {
    const userDetails = getLocalUser();
    const checkedEmail = userDetails?.email ?? dbUser?.email;
    if (checkedEmail) {
      setEmail(checkedEmail);
    }
  }, [dbUser]);

  console.log("data", data);

  const paymentMode = useMemo(() => {
    if (transactionDetails?.paymentDetails?.type) {
      return `${transactionDetails?.paymentDetails?.type}-${transactionDetails?.paymentDetails?.identifier}`;
    }
    if (transactionDetails?.mode === PAYMENT_MODE.BANK_TRANSFER) {
      return "Bank Transfer";
    }
    if (transactionDetails?.mode === PAYMENT_MODE.CASH) {
      return "Cash";
    }
    return "-";
  }, [transactionDetails]);

  const title = useMemo(() => {
    if (isSubscription) {
      if (isSuccess) {
        return "Subscription successful";
      }
      if (isCancelled) {
        return "Subscription Cancelled";
      }
      return "Subscription Failed";
    } else {
      if (isSuccess) {
        return "Payment successful";
      }
      if (isCancelled) {
        return "Payment cancelled";
      }
      return "Payment Failed";
    }
  }, [isSubscription, isSuccess, isCancelled]);

  const subtitle = useMemo(() => {
    if (isSubscription) {
      return isSuccess
        ? "Your subscription is active!"
        : isCancelled
        ? "Your subscription has been cancelled."
        : "Unable to activate your subscription. Please try again.";
    }
    return isSuccess
      ? "Payment successful! Thank you for your purchase!"
      : isCancelled
      ? "Your payment has been cancelled."
      : "Payment failed. Please check your details and try again.";
  }, [isSubscription, isSuccess, isCancelled]);

  const makePaymentCancelled = async (id) => {
    try {
      const { data } = await axiosInstance.get(
        `payment/transaction/cancel/${id}`
      );
    } catch (error) {
      console.error(
        "Something went wrong in makePaymentCancelled due to ",
        error
      );
    }
  };

  useEffect(() => {
    if (data._id) {
      const localTransactionId = localStorage.getItem("lastTransactionId");
      if (localTransactionId === data?._id) {
        const isInitiated = data.status === PAYMENT_STATUS.INITIATED;
        if (isInitiated) {
          const payload = {
            ...data,
            status: PAYMENT_STATUS.CANCELLED,
          } as CommonTransactionType;
          setTransactionDetails(payload);
          makePaymentCancelled(data._id);
        } else {
          setTransactionDetails(data);
        }
        localStorage.removeItem("lastTransactionId");
      } else {
        setTransactionDetails(data);
      }
      setIsLoading(false);
    }
  }, [data?._id]);

  const { currencies, isEveryCurrencySame } = useMemo(() => {
    if (transactionDetails) {
      const customProductInfo = getCustomProductInfo(
        transactionDetails?.customProductsDetails?.customProductsInfo
      );

      if (customProductInfo) {
        return {
          currencies: [customProductInfo.currency],
          isEveryCurrencySame: true,
        };
      }

      const classesList = transactionDetails.classesDetails.map(
        (m) => CURRENCY_ENUM.USD
      );
      const eventList =
        transactionDetails.eventIds.map((m) => {
          const eventDetails = m as EventSchemaType;
          return eventDetails?.currency ?? CURRENCY_ENUM.USD;
        }) ?? [];

      const clubList = transactionDetails.clubsDetails.map((m) => m.currency);
      const allCurrencies = [...eventList, ...clubList, ...classesList];
      const isEveryCurrencySame = checkIfEveryCurrencySame({
        currencies: allCurrencies,
      });
      return {
        currencies: allCurrencies,
        isEveryCurrencySame,
      };
    }
    return {
      currencies: [],
      isEveryCurrencySame: true,
    };
  }, [transactionDetails]);

  return (
    <Container
      maxWidth="lg"
      sx={{
        padding: { xs: "1rem", sm: "2rem" },
        backgroundColor: "background.paper",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {isLoading ? (
        <LoadingCard />
      ) : transactionDetails ? (
        <Card sx={CardStyle}>
          <Icon isSuccess={isSuccess} />
          <Typography textAlign="center" fontWeight="800" fontSize={22} mb={2}>
            {title}
          </Typography>
          <Typography
            textAlign="center"
            fontSize={12}
            color="rgba(100, 116, 139, 1)"
          >
            {subtitle}
          </Typography>
          <Typography
            textAlign="center"
            fontSize={12}
            color="rgba(100, 116, 139, 1)"
            mb={6}
          >
            {isSuccess ? (
              <>We&apos;ve emailed your order receipt to {email}.</>
            ) : isCancelled ? (
              ""
            ) : (
              "Please try again, or reach out to support if the issue continues."
            )}
          </Typography>

          <NameValue left="Transaction ID" bb right={transactionDetails?._id} />
          <NameValue
            left="Transaction Date"
            bb
            right={formatDate({
              date: new Date(transactionDetails?.createdAt),
            })}
          />
          <NameValue left="Payment Method" bb right={paymentMode} />
          <OrderDetails
            data={transactionDetails}
            isSubscription={isSubscription}
          />
          <NameValue
            bb
            left="Total Amount"
            right={
              <>
                <b>
                  {getPriceSymbol({
                    currency: currencies[0],
                  })}
                  {getFinalAmount(transactionDetails?.finalAmount)}
                </b>
              </>
            }
          />

          {isSuccess ? (
            <>
              <Link href="/classes/" style={{ width: "100%" }}>
                <CustomButton
                  text="Continue Shopping"
                  colortype="secondary"
                  sx={{
                    width: "100%",
                    mt: 3,
                  }}
                />
              </Link>
              <Link href="/profile/dashboard" style={{ width: "100%" }}>
                <CustomButton
                  text="Go to My Dashboard"
                  colortype="primary"
                  sx={{
                    width: "100%",
                    mt: 3,
                  }}
                />
              </Link>
            </>
          ) : (
            <CustomButton
              disabled={isMaking || isSubscribing}
              colortype="secondary"
              onClick={() => {
                if (transactionDetails.mode === PAYMENT_MODE.SUBSCRIPTION) {
                  const clubId =
                    transactionDetails.clubsDetails[0].clubInfo._id;
                  const memberships =
                    transactionDetails.clubsDetails[0].memberships;
                  subscribeClub({
                    clubId,
                    memberships,
                  });
                  return;
                }

                const customProductInfo = getCustomProductInfo(
                  transactionDetails?.customProductsDetails?.customProductsInfo
                );
                if (customProductInfo) {
                  makePayment({
                    cartId: [],
                    eventId: [],
                    classesDetails: [],
                    clubsDetails: [],
                    price: customProductInfo.price,
                    productData: [
                      {
                        name: customProductInfo.title,
                        description: customProductInfo.description,
                        images: ["https://www.patitofeo.com/patitoB.png"],
                        price: customProductInfo.price,
                      },
                    ],
                    eventsPriceDetails: [],
                    currency: customProductInfo.currency,
                    customProductsDetails: {
                      price: customProductInfo.price,
                      currency: customProductInfo.currency,
                      customProductsInfo: customProductInfo._id,
                    },
                  });
                  return;
                }

                const cartIds = cartItems
                  .map((item) => item._id)
                  .filter((f) => !!f);
                const eventIds =
                  transactionDetails?.eventIds?.map((item) => item?._id) ?? [];
                const productData =
                  getItemsDetailsFromTransaction(transactionDetails);
                const finalAmount = getFinalAmount(
                  transactionDetails?.finalAmount
                );

                if (isEveryCurrencySame) {
                  makePayment({
                    cartId: cartIds,
                    price: finalAmount,
                    eventId: eventIds,
                    productData: productData.map((m) => ({
                      name: m.title,
                      price: m.amount,
                    })),
                    classesDetails: transactionDetails.classesDetails.map(
                      (m) => ({
                        classInfo: m.classInfo,
                        plans: m.plans,
                      })
                    ),
                    clubsDetails: transactionDetails.clubsDetails.map((m) => ({
                      clubInfo: m.clubInfo._id,
                      memberships: m.memberships,
                    })),
                    eventsPriceDetails:
                      transactionDetails.eventIds.map((m) => {
                        const eventDetails = m as EventSchemaWithSingleEvent;
                        const singleEventOn = getSingleEventOn(
                          eventDetails.eventOn
                        );
                        return {
                          eventInfo: eventDetails._id,
                          price: +eventDetails?.price,
                          currency: eventDetails?.currency ?? CURRENCY_ENUM.USD,
                          eventOn: singleEventOn._id,
                        };
                      }) ?? [],
                    currency: currencies[0],
                  });
                } else {
                  showSnackbar("Please try again", {
                    type: "error",
                  });
                  router.push("/cart");
                }
              }}
              text="Try Again"
              sx={{
                width: "100%",
                mt: 3,
              }}
            />
          )}
        </Card>
      ) : (
        <NoOrder />
      )}
    </Container>
  );
};

const LoadingCard = () => {
  return (
    <Card sx={CardStyle}>
      <Skeleton sx={{ width: 150, height: 150 }} />
      <Skeleton sx={{ width: "40%", height: 40 }} />
      <Skeleton sx={{ width: "50%", height: 35 }} />
      <Skeleton sx={{ width: "100%", height: 55 }} />
      <Skeleton sx={{ width: "100%", height: 55 }} />
      <Skeleton sx={{ width: "100%", height: 55 }} />
      <Skeleton sx={{ width: "100%", height: 55 }} />
      <Skeleton sx={{ width: "100%", height: 55 }} />
      <Skeleton sx={{ width: "100%", height: 55 }} />
    </Card>
  );
};

type ItemRowProps = React.FC<{
  planName: string;
  classType: string;
  amount: string;
  currency: CURRENCY_ENUM;
}>;
const ItemRow: ItemRowProps = ({ planName, classType, amount, currency }) => {
  return (
    <Box
      width="100%"
      display="flex"
      flexDirection="row"
      justifyContent="space-between"
      alignItems="center"
      mt={2}
    >
      <Box>
        <Typography
          color="rgba(60, 60, 60, 1)"
          fontWeight={700}
          fontSize="0.7rem"
        >
          {classType}
        </Typography>
        <Typography fontSize="0.65rem" color="rgba(163, 163, 163, 1)">
          {planName}
        </Typography>
      </Box>
      <Typography
        fontWeight={700}
        fontSize="0.7rem"
        color="rgba(109, 109, 109, 1)"
      >
        {getPriceSymbol({ currency })}
        {amount}
      </Typography>
    </Box>
  );
};

type OrderDetailsProps = React.FC<{
  data: CommonTransactionType;
  isSubscription: boolean;
}>;
const OrderDetails: OrderDetailsProps = ({ data, isSubscription }) => {
  const items = getItemsDetailsFromTransaction(data);
  return (
    <Box width="100%" p={2}>
      <Typography fontSize="0.85rem" fontWeight={700}>
        {isSubscription ? "Subscription details" : "Order Details"}
      </Typography>
      {items.map((m, i) => (
        <ItemRow
          key={i}
          currency={m.currency}
          amount={String(m.amount)}
          classType={(() => {
            if (m.type === "Custom Product") {
              return "Custom Product";
            }
            if (m.type === "Club") {
              return "Club";
            }
            if (m.type === "Community") {
              return "Community";
            }
            return getTypeName(m.type);
          })()}
          planName={m.title}
        />
      ))}
    </Box>
  );
};

type NameValueProps = React.FC<{
  left: string;
  right: any;
  sx?: SxProps<Theme>;
  bb?: boolean;
}>;
const NameValue: NameValueProps = ({ left, right, sx = {}, bb = false }) => {
  return (
    <Box
      width="100%"
      display="flex"
      flexDirection="row"
      justifyContent="space-between"
      alignItems="center"
      p={2}
      sx={{
        ...sx,
        borderBottom: !bb ? "none" : "1px solid rgba(241, 241, 241, 1)",
      }}
    >
      <Typography fontSize="0.85rem" fontWeight={700}>
        {left}
      </Typography>
      <Typography fontSize="0.75rem" color="rgba(109, 109, 109, 1)">
        {right}
      </Typography>
    </Box>
  );
};

const Icon = ({ isSuccess = false }) => {
  const SIZE = 120;
  if (isSuccess) {
    return (
      <svg
        width={SIZE}
        height={SIZE}
        viewBox="0 0 144 144"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="144" height="144" rx="72" fill="#F0FDF7" />
        <circle cx="72" cy="72" r="62.5" fill="#DBF6E9" />
        <circle cx="72" cy="71" r="53" fill="#C2EFDA" />
        <rect x="29" y="26" width="88" height="88" rx="44" fill="white" />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M29 70C29 58.3305 33.6357 47.1389 41.8873 38.8873C50.1389 30.6357 61.3305 26 73 26C84.6695 26 95.8611 30.6357 104.113 38.8873C112.364 47.1389 117 58.3305 117 70C117 81.6695 112.364 92.8611 104.113 101.113C95.8611 109.364 84.6695 114 73 114C61.3305 114 50.1389 109.364 41.8873 101.113C33.6357 92.8611 29 81.6695 29 70ZM70.4891 88.832L95.8213 57.1637L91.2453 53.5029L69.6443 80.4955L54.344 67.7472L50.5893 72.2528L70.4891 88.832Z"
          fill="#14AE5C"
        />
      </svg>
    );
  } else {
    return (
      <svg
        width={SIZE}
        height={SIZE}
        viewBox="0 0 144 144"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect width="144" height="144" rx="72" fill="#FFEFEF" />
        <circle cx="72" cy="72" r="62.5" fill="#FFDADA" />
        <circle cx="72" cy="71" r="53" fill="#FFC3C2" />
        <rect x="29" y="26" width="88" height="88" rx="44" fill="white" />
        <path
          d="M55.4 91.0613L73 73.4613L90.6 91.0613L94.0613 87.6L76.4613 70L94.0613 52.4L90.6 48.9387L73 66.5387L55.4 48.9387L51.9387 52.4L69.5387 70L51.9387 87.6L55.4 91.0613ZM73.0147 114C66.9329 114 61.2129 112.846 55.8547 110.539C50.4997 108.228 45.8406 105.092 41.8773 101.132C37.9141 97.1724 34.777 92.5182 32.4662 87.1698C30.1554 81.8213 29 76.103 29 70.0147C29 63.9264 30.1554 58.2064 32.4662 52.8547C34.7738 47.4997 37.9043 42.8406 41.8578 38.8773C45.8113 34.9141 50.4671 31.777 55.8253 29.4662C61.1836 27.1554 66.9036 26 72.9853 26C79.0671 26 84.7871 27.1554 90.1453 29.4662C95.5003 31.7738 100.159 34.9059 104.123 38.8627C108.086 42.8194 111.223 47.4753 113.534 52.8302C115.845 58.1852 117 63.9035 117 69.9853C117 76.0671 115.846 81.7871 113.539 87.1453C111.231 92.5035 108.096 97.1627 104.132 101.123C100.169 105.083 95.515 108.22 90.1698 110.534C84.8246 112.848 79.1062 114.003 73.0147 114Z"
          fill="#EC221F"
        />
      </svg>
    );
  }
};

export default Success;
