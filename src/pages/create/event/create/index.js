import React, { useEffect, useState } from "react";
import EventBody from "@/components/Create/event/EventBody";
import { useSnackbar } from "@/hooks/useSnackbar";
import { useRouter } from "next/router";
import axios from "axios";
import EventBodyLoading from "@/components/Create/event/Loading";

const CreateEvent = () => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(false);
  const [proficiencies, setProficiencies] = useState([]);
  const [languages, setLanguages] = useState([]);

  useEffect(() => {
    const fetchProficiencies = async () => {
      try {
        setIsLoading(true);
        const { data } = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
        );
        if (data.isError) {
          router.back();
          showSnackbar("Something went wrong.Please try again", {
            type: "error",
          });
        } else {
          const languages = data.languages;
          const proficiencies = data.languageProficiencies;
          setProficiencies(proficiencies);
          setLanguages(languages);
        }
        setIsLoading(false);
      } catch (error) {
        console.error(
          "Something went wrong while fetching proficiencies ",
          error
        );
        router.back();
        setIsLoading(false);
        showSnackbar("Something went wrong.Please try again", {
          type: "error",
        });
      }
    };
    fetchProficiencies();
  }, []);

  if (isLoading) {
    return <EventBodyLoading />;
  }

  return (
    <>
      <EventBody proficiencies={proficiencies} languages={languages} />
    </>
  );
};

export default CreateEvent;
