import React, { useEffect, useState } from "react";
import axios from "axios";
import EventBody from "@/components/Create/event/EventBody";
import { useParams } from "next/navigation";
import { useRouter } from "next/router";
import { useSnackbar } from "@/hooks/useSnackbar";
import EventBodyLoading from "@/components/Create/event/Loading";
import { LanguageType } from "@/api/mongoTypes";

const EventEdit = () => {
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState(null);
  const router = useRouter();
  const id = router?.query?.id;
  const [proficiencies, setProficiencies] = useState([]);
  const [languages, setLanguages] = useState<LanguageType[]>([]);

  const fetchEventDetails = async (eventId: string) => {
    try {
      setIsLoading(true);
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/event/get/${eventId}`,
        {
          params: {
            needEventImage: true,
          },
        }
      );

      const { data: languagesData } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
      );
      if (languagesData.isError) {
        router.back();
        showSnackbar("Failed to fetch Event", {
          type: "error",
        });
      } else {
        const languages = languagesData.languages;
        const proficiencies = languagesData.languageProficiencies;
        setProficiencies(proficiencies);
        setLanguages(languages);
      }

      if (data.isError) {
        router.back();
        showSnackbar("Failed to fetch Event", {
          type: "error",
        });
      } else {
        setData(data.data);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchEventDetails(String(id));
    }
  }, [id]);

  if (isLoading) {
    return <EventBodyLoading />;
  }

  return (
    <>
      <EventBody
        languages={languages}
        proficiencies={proficiencies}
        id={String(id)}
        data={data}
      />
    </>
  );
};

export default EventEdit;
