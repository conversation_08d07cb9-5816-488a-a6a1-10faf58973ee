import CreateScheduleBody from "@/components/Create/schedule/CreateScheduleBody";
import { ROLE_TYPES } from "@/constant/Enums";
import axiosInstance from "@/utils/interceptor";
import React from "react";

export async function getServerSideProps() {
  const { data } = await axiosInstance.post(
    `user/get-users`,
    {
      role: ROLE_TYPES.TEAHCER,
    },
    {
      params: {
        skip: 0,
        limit: 30,
      },
    }
  );
  const teachersList = data.data;
  return { props: { teachersList } };
}

const CreateSchedule = ({ teachersList }) => {
  return <CreateScheduleBody scheduleData={null} teachersList={teachersList} />;
};

export default CreateSchedule;
