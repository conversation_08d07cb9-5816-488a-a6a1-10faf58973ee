import CreateEditCollection from "@/components/Create/collection/CreateEditCollection";
import EventBodyLoading from "@/components/Create/event/Loading";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

const EditCollection = () => {
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState(null);
  const router = useRouter();
  const id = router?.query?.id;
  const [proficiencies, setProficiencies] = useState([]);

  const fetchCollectionDetails = async () => {
    const handleError = () => {
      setIsLoading(false);
      showSnackbar("Failed to fetch collection", {
        type: "error",
      });
    };

    try {
      setData(null);
      setIsLoading(true);
      const { data: respData } = await axiosInstance.get(
        `collection/get/${id}`,
        {
          params: {
            needCoverImages: true,
            needVideos: true,
          },
        }
      );
      if (respData.data) {
        setData(respData.data);
        const { data: languagesData } = await axiosInstance.get(
          `utils/get-proficiencies`
        );
        setProficiencies(languagesData.data);
      } else {
        handleError();
      }
      setIsLoading(false);
    } catch (error) {
      console.error(
        "Something went wrong in in fetchCollectionDetails due to ",
        error
      );
      handleError();
    }
  };

  useEffect(() => {
    if (id) {
      fetchCollectionDetails();
    }
  }, [id]);

  if (isLoading) {
    return <EventBodyLoading />;
  }

  if (!data) {
    return <p>Something went wrong.Please try again.</p>;
  }

  return <CreateEditCollection data={data} proficiencies={proficiencies} />;
};

export default EditCollection;
