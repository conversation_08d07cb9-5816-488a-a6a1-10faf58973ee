import CreateEditCollection from "@/components/Create/collection/CreateEditCollection";
import EventBodyLoading from "@/components/Create/event/Loading";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

const CreateCollection = () => {
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(true);
  const [proficiencies, setProficiencies] = useState([]);

  const fetchCollectionDetails = async () => {
    const handleError = () => {
      setIsLoading(false);
      showSnackbar("Failed to fetch collection", {
        type: "error",
      });
    };

    try {
      setIsLoading(true);
      const { data: respData } = await axiosInstance.get(
        `utils/get-proficiencies`
      );
      if (respData.data) {
        setProficiencies(respData.data);
      } else {
        handleError();
      }
      setIsLoading(false);
    } catch (error) {
      console.error(
        "Something went wrong in in fetchCollectionDetails due to ",
        error
      );
      handleError();
    }
  };

  useEffect(() => {
    fetchCollectionDetails();
  }, []);

  if (isLoading) {
    return <EventBodyLoading />;
  }

  return <CreateEditCollection data={null} proficiencies={proficiencies} />;
};

export default CreateCollection;
