import CreateListHeader from "@/components/Create/CreateListHeader";
import SearchInput from "@/components/SearchInput";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { Box, Container } from "@mui/material";
import React, { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import CollectionCard from "@/components/dashboard/CollectionCard";

const LIMIT = 6;

const CollectionManager = () => {
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState([]);
  const [search, setSearch] = useState("");

  const fetchData = async ({ skipCount = 0, search = "" }) => {
    const showLoading = +skipCount === 0;
    try {
      if (showLoading) {
        setIsLoading(true);
        setData([]);
      }
      const params = {
        needCoverImages: true,
        skip: skipCount,
        limit: LIMIT,
        search,
      };
      const { data } = await axiosInstance.get(`collection/all`, {
        params,
      });
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        showSnackbar("Failed to fetch collections", {
          type: "error",
        });
      }
      if (showLoading) {
        setIsLoading(false);
      }
    } catch (error) {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchData({ skipCount: 0, search: "" });
  }, []);

  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
      }}
    >
      <CreateListHeader
        createButtonText="Create Collection"
        createRoute="/create/collection/create"
        title="Collection"
      />

      {isLoading ? (
        <p>fetching collections</p>
      ) : (
        <>
          <Box
            width="100%"
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
          >
            <SearchInput
              placeholder="Search by name"
              searchValue={search}
              setSearchValue={setSearch}
              disabled={false}
              handleClear={() => {
                setSearch("");
                fetchData({ skipCount: 0, search: "" });
              }}
              handleSearch={() => {
                fetchData({ skipCount: 0, search });
              }}
            />
            {/* <ClassesFilter
              setTargetLanguage={setTargetLanguage}
              targetLanguage={targetLanguage}
            /> */}
          </Box>
          <InfiniteScroll
            dataLength={data.length}
            next={() => fetchData({ skipCount: skip })}
            hasMore={hasMore}
            loader={<p style={{ textAlign: "center" }}>Loading</p>}
            endMessage={
              <p style={{ textAlign: "center" }}>
                <b>
                  {search.length > 0
                    ? `No collections found with title ${search}`
                    : "Yay! You have seen it all"}
                </b>
              </p>
            }
          >
            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              justifyContent="center"
              flexWrap="wrap"
              gap={10}
              mt={10}
            >
              {data.map((m, i) => (
                <CollectionCard
                  isAdmin
                  key={m._id}
                  data={m}
                  onDelete={(id) => {
                    setData((prev) => prev.filter((f) => f._id !== id));
                  }}
                />
              ))}
            </Box>
          </InfiniteScroll>
        </>
      )}
    </Container>
  );
};

export default CollectionManager;
