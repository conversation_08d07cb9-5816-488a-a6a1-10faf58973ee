import CreateEditCollection from "@/components/Create/collection/CreateEditCollection";
import EventBodyLoading from "@/components/Create/event/Loading";
import VideoCreateEdit from "@/components/Create/video/VideoCreateEdit";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

const EditVideo = () => {
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState(null);
  const router = useRouter();
  const id = router?.query?.id;

  const fetchVideosDetails = async () => {
    const handleError = () => {
      setIsLoading(false);
      showSnackbar("Failed to fetch video details", {
        type: "error",
      });
    };

    try {
      setData(null);
      setIsLoading(true);
      const { data: respData } = await axiosInstance.get(`video/get/${id}`, {
        params: {
          needCategoryIds: true,
          needCollectionIds: true,
          needCreator: true,
          needThumbnail: true,
          needEsSubtitleUrl: true,
          needEnSubtitleUrl: true,
          needVideoUrl: true,
        },
      });
      if (respData.data) {
        setData(respData.data);
      } else {
        handleError();
      }
      setIsLoading(false);
    } catch (error) {
      console.error(
        "Something went wrong in in fetchVideosDetails due to ",
        error
      );
      handleError();
    }
  };

  useEffect(() => {
    if (id) {
      fetchVideosDetails();
    }
  }, [id]);

  if (isLoading) {
    return <EventBodyLoading />;
  }

  if (!data) {
    return <p>Something went wrong.Please try again.</p>;
  }

  return <VideoCreateEdit data={data} />;
};

export default EditVideo;
