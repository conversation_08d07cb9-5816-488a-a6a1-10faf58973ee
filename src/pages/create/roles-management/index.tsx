import CreateHeader from "@/components/Create/CreateHeader";
import { ROLE_TYPES } from "@/constant/Enums";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import {
  Box,
  Button,
  Chip,
  CircularProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import Head from "next/head";
import React, { useEffect, useState } from "react";
import { getUserFullName, getUserRoles } from "@/utils/format";
import InfiniteScroll from "react-infinite-scroll-component";
import useDebounce from "@/hooks/useDebounce";
import { UserType } from "@/api/mongoTypes";
import UpdateRoleModal from "@/components/Create/role/UpdateRoleModal";
import DeleteUserModal from "@/components/Create/role/DeleteUserModal";
import { useUserContext } from "@/contexts/UserContext";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "80%",
  alignItems: "center",
  width: "100%",
  margin: "0 auto",
  padding: { xs: "20px", sm: "0px" },
};

const LIMIT = 10;

const HeadersList = [
  {
    id: 1,
    name: "name",
  },
  {
    id: 2,
    name: "email",
  },
  {
    id: 3,
    name: "role",
  },
  {
    id: 4,
    name: "action",
  },
  {
    id: 5,
    name: "action",
  },
];

const RoleManagement = () => {
  const { showSnackbar } = useSnackbar();
  const [search, setSearch] = useState("");
  const [role, setRole] = useState<string>("");
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [users, setusers] = useState([]);
  const debouncedSearchTerm = useDebounce(search, 1000);
  const [selectedUser, setSelectedUser] = useState<UserType>(null);
  const [deleteUser, setDeleteUser] = useState<UserType>(null);
  const { dbUser } = useUserContext();

  const fetchData = async ({ search, skip, role }) => {
    try {
      if (skip === 0) {
        setIsLoading(true);
      }
      const { data: respData } = await axiosInstance.post(
        `user/get-users`,
        {
          role,
        },
        {
          params: {
            skip,
            limit: LIMIT,
            search,
          },
        }
      );
      if (respData.success) {
        if (skip === 0) {
          setusers(respData.data);
        } else {
          setusers((prev) => [...prev, ...respData.data]);
        }
        setSkip(+skip + LIMIT);
        setHasMore(+respData.data.length >= LIMIT);
      } else {
        showSnackbar("Failed to fetch users", { type: "error" });
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      showSnackbar("Failed to fetch users", { type: "error" });
      console.error("Something went wrong in fetchData due to", error);
    }
  };

  useEffect(() => {
    fetchData({
      search: debouncedSearchTerm,
      skip: 0,
      role,
    });
  }, [debouncedSearchTerm, role]);

  return (
    <>
      <Head>
        <title>Internal Roles Management</title>
      </Head>
      <Box sx={containerStyles}>
        <CreateHeader
          text="Role Management"
          maxWidth="100%"
          bottomBorderNeeded
        />
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          mt={4}
          gap={2}
          width="100%"
          justifyContent="start"
          sx={{
            position: "sticky",
            top: 80,
            background: "#fff",
          }}
        >
          <TextField
            label="Search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Filter by Role</InputLabel>
            <Select
              value={role}
              label="Filter by Role"
              onChange={(e) => {
                const newRole = e.target.value;
                setRole(newRole);
              }}
            >
              <MenuItem value="">All Roles</MenuItem>
              {Object.values(ROLE_TYPES).map((role) => (
                <MenuItem key={role} value={role}>
                  {role}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <InfiniteScroll
          dataLength={users.length}
          next={() => fetchData({ search: debouncedSearchTerm, skip, role })}
          hasMore={hasMore}
          loader={
            <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          }
          endMessage={
            <Box sx={{ textAlign: "center", p: 2, color: "text.secondary" }}>
              <Typography variant="body2">
                {users.length > 0
                  ? "You've seen all users"
                  : "No users found matching your criteria"}
              </Typography>
            </Box>
          }
          scrollableTarget="scrollableDiv"
        >
          <Box display="flex" flexDirection="row" alignItems="center" mt={5}>
            {HeadersList.map((m) => (
              <RowCell key={m.id}>{m.name}</RowCell>
            ))}
          </Box>
          <Box>
            {isLoading ? (
              <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : (
              users.map((row) => {
                const userRoles = getUserRoles(row as any);
                return (
                  <Box
                    display="flex"
                    flexDirection="row"
                    alignItems="center"
                    width="100%"
                    key={row._id}
                    mb={1}
                    p={1}
                    sx={{
                      borderBottom: "1px solid silver",
                    }}
                  >
                    <RowCell>{getUserFullName(row)}</RowCell>
                    <RowCell>{row.email}</RowCell>
                    <RowCell>
                      {userRoles.map((m) => (
                        <Chip key={m} label={m} size="small" sx={{ mr: 1 }} />
                      ))}
                    </RowCell>
                    <RowCell>
                      <Button
                        onClick={() => {
                          setSelectedUser(row);
                        }}
                      >
                        Update
                      </Button>
                    </RowCell>
                    <RowCell>
                      <Button
                        sx={{
                          background: "red",
                          "&:hover": {
                            background: "red",
                          },
                        }}
                        onClick={() => {
                          if (dbUser._id === row._id) {
                            showSnackbar("You cannot delete yourself", {
                              type: "warning",
                            });
                            return;
                          }
                          setDeleteUser(row);
                        }}
                      >
                        Delete
                      </Button>
                    </RowCell>
                  </Box>
                );
              })
            )}
          </Box>
        </InfiniteScroll>
      </Box>

      {selectedUser && (
        <UpdateRoleModal
          selectedUser={selectedUser}
          open={!!selectedUser}
          setOpen={setSelectedUser}
          onUpdate={(user) => {
            setusers((prev) =>
              prev.map((m) => {
                if (m._id === user._id) {
                  return {
                    ...m,
                    ...user,
                  };
                } else {
                  return m;
                }
              })
            );
            setSelectedUser(null);
          }}
        />
      )}

      {deleteUser && (
        <DeleteUserModal
          open={!!deleteUser}
          close={() => {
            setDeleteUser(null);
          }}
          onDeleteSuccess={() => {
            setusers((prev) => prev.filter((m) => m._id !== deleteUser._id));
          }}
          deleteUserDetails={deleteUser}
        />
      )}
    </>
  );
};

export default RoleManagement;

const RowCell = ({ children }) => {
  return (
    <Box
      width="20%"
      sx={{
        textAlign: "center",
      }}
    >
      {children}
    </Box>
  );
};
