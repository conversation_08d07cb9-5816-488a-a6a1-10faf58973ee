import ClubBody from "@/components/Create/club/ClubBody";
import { ROLE_TYPES } from "@/constant/Enums";
import axios from "axios";
import React from "react";

export async function getServerSideProps() {
  const { data: languagesData } = await axios.get(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
  );
  const languages = languagesData.languages;
  const proficiencies = languagesData.languageProficiencies;

  const { data: respData } = await axios.post(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/user/get-users`,
    {
      role: ROLE_TYPES.TEAHCER,
    },
    {
      params: {
        skip: 0,
        limit: 30,
      },
    }
  );
  const teachers = respData.data;

  return { props: { proficiencies, teachers, languages } };
}

const CreateClub = ({ proficiencies, teachers, languages }) => {
  return (
    <ClubBody
      data={null}
      languages={languages}
      teachers={teachers}
      proficiencies={proficiencies}
    />
  );
};

export default CreateClub;
