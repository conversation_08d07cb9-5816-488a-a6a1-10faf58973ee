import { ClubType } from "@/api/mongoTypes";
import OnlineClubCard from "@/components/classes/OnlineClubCard";
import ClassesFilter from "@/components/Create/club/ClubFilter";
import CreateListHeader from "@/components/Create/CreateListHeader";
import SearchInput from "@/components/SearchInput";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { Box, Button, Container, Typography } from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

const LIMIT = 6;

const Clubs = () => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState<ClubType[]>([]);
  const [targetLanguage, setTargetLanguage] = useState("all");
  const [search, setSearch] = useState("");

  const fetchData = async ({ skipCount = 0, search = "", targetLanguage }) => {
    const showLoading = +skipCount === 0;
    try {
      if (showLoading) {
        setIsLoading(true);
        setData([]);
      }
      const params = {
        needClubImage: true,
        skip: skipCount,
        limit: LIMIT,
        search,
        targetLanguage,
      };
      if (params.targetLanguage === "all") {
        delete params.targetLanguage;
      }
      const { data } = await axiosInstance.get(`club/all`, {
        params,
      });
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        showSnackbar("Failed to fetch Events", {
          type: "error",
        });
      }
      if (showLoading) {
        setIsLoading(false);
      }
    } catch (error) {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchData({ skipCount: 0, search: "", targetLanguage });
  }, [targetLanguage]);

  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
      }}
    >
      <CreateListHeader
        createButtonText="Create Club"
        createRoute="/create/club/create"
        title="Clubs"
      />

      {isLoading ? (
        <p>fetching clubs</p>
      ) : (
        <>
          <Box
            width="100%"
            display="flex"
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
          >
            <SearchInput
              placeholder="Search by name"
              searchValue={search}
              setSearchValue={setSearch}
              disabled={false}
              handleClear={() => {
                setSearch("");
                fetchData({ skipCount: 0, search: "", targetLanguage });
              }}
              handleSearch={() => {
                fetchData({ skipCount: 0, search, targetLanguage });
              }}
            />
            <ClassesFilter
              setTargetLanguage={setTargetLanguage}
              targetLanguage={targetLanguage}
            />
          </Box>
          <InfiniteScroll
            dataLength={data.length}
            next={() => fetchData({ skipCount: skip, search, targetLanguage })}
            hasMore={hasMore}
            loader={<p style={{ textAlign: "center" }}>Loading</p>}
            endMessage={
              <p style={{ textAlign: "center" }}>
                <b>Yay! You have seen it all</b>
              </p>
            }
          >
            <Box
              display="flex"
              flexDirection="row"
              alignItems="center"
              justifyContent="center"
              flexWrap="wrap"
              gap={10}
              mt={10}
            >
              {data.map((m, i) => (
                <OnlineClubCard
                  key={m._id}
                  data={m}
                  onDelete={() => {
                    setData((prev) => prev.filter((f) => f._id !== m._id));
                  }}
                  isAdmin
                  onClick={() => {
                    router.push(`/create/club/edit/${m._id}`);
                  }}
                />
              ))}
            </Box>
          </InfiniteScroll>
        </>
      )}
    </Container>
  );
};

export default Clubs;
