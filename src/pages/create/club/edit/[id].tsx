import { LanguageType } from "@/api/mongoTypes";
import ClubBody from "@/components/Create/club/ClubBody";
import EventBodyLoading from "@/components/Create/event/Loading";
import { ROLE_TYPES } from "@/constant/Enums";
import { useSnackbar } from "@/hooks/useSnackbar";
import axios from "axios";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

const EditClub = () => {
  const { showSnackbar } = useSnackbar();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState(null);
  const router = useRouter();
  const id = router?.query?.id;
  const [proficiencies, setProficiencies] = useState([]);
  const [languages, setLanguages] = useState<LanguageType[]>([]);
  const [teachers, setTeachers] = useState([]);

  const fetchEventDetails = async (eventId: string) => {
    try {
      setIsLoading(true);
      const { data } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/club/get/${eventId}`,
        {
          params: {
            needClubImage: true,
            needCreators: false,
          },
        }
      );
      const { data: languagesData } = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
      );
      setLanguages(languagesData.languages);
      setProficiencies(languagesData.languageProficiencies);

      if (data.isError) {
        router.back();
        showSnackbar("Failed to fetch Event", {
          type: "error",
        });
      } else {
        setData(data.data);
      }

      const { data: respData } = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}api/user/get-users`,
        {
          role: ROLE_TYPES.TEAHCER,
        },
        {
          params: {
            skip: 0,
            limit: 30,
          },
        }
      );
      const teachers = respData.data;
      setTeachers(teachers);

      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchEventDetails(String(id));
    }
  }, [id]);

  if (isLoading) {
    return <EventBodyLoading />;
  }
  return (
    <ClubBody
      data={data}
      languages={languages}
      teachers={teachers}
      proficiencies={proficiencies}
    />
  );
};

export default EditClub;
