import CustomProductBody from "@/components/Create/CustomProduct/CustomProductBody";
import axios from "axios";
import React from "react";

export async function getServerSideProps() {
  const { data: languagesData } = await axios.get(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
  );
  const languages = languagesData.languages;
  const proficiencies = languagesData.languageProficiencies;
  return { props: { proficiencies, languages } };
}

const CreateCustomProduct = ({ languages }) => {
  return <CustomProductBody languages={languages} data={null} />;
};

export default CreateCustomProduct;
