import { CustomProductType } from "@/api/mongoTypes";
import ExperiencesLoading from "@/components/classes/ExperiencesLoading";
import CreateListHeader from "@/components/Create/CreateListHeader";
import CPCard from "@/components/Create/CustomProduct/CPCard";
import SearchInput from "@/components/SearchInput";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { Box, Button, Container, Input } from "@mui/material";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

const LIMIT = 6;
const BASE_URL = "/create/custom-product";

const list = [
  {
    id: 1,
    name: "All",
  },
  {
    id: 2,
    name: "Archieved",
  },
];

const CustomProductList = () => {
  const { showSnackbar } = useSnackbar();
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState<CustomProductType[]>([]);
  const [isArchieved, setIsArchieved] = useState(false);
  const [search, setSearch] = useState("");

  const fetchData = async ({
    skipCount = 0,
    isArchieved = false,
    search = "",
  }) => {
    const showLoading = +skipCount === 0;
    try {
      if (showLoading) {
        setSkip(0);
        setData([]);
        setIsLoading(true);
      }
      const { data } = await axiosInstance.get(`custom-product/all`, {
        params: {
          skip: skipCount,
          limit: LIMIT,
          isArchieved,
          search,
        },
      });
      if (data.success && data?.data) {
        const length = data?.data?.length;
        setData((prev) => [...prev, ...data.data]);
        setHasMore(length >= LIMIT);
        setSkip(+skipCount + LIMIT);
      } else {
        showSnackbar("Failed to fetch custom products", {
          type: "error",
        });
      }
      if (showLoading) {
        setIsLoading(false);
      }
    } catch (error) {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchData({ skipCount: 0, isArchieved, search });
  }, [isArchieved]);

  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
      }}
    >
      <CreateListHeader
        createButtonText="Create Custom Product"
        createRoute={`${BASE_URL}/create`}
        title="Custom Products"
      />
      <Box
        display="flex"
        flexDirection="row"
        width="100%"
        alignItems="center"
        mt={6}
        justifyContent="space-between"
      >
        <SearchInput
          placeholder="Search by name"
          searchValue={search}
          setSearchValue={setSearch}
          disabled={false}
          handleClear={() => {
            setSearch("");
            fetchData({ skipCount: 0, isArchieved, search: "" });
          }}
          handleSearch={() => {
            fetchData({ skipCount: 0, isArchieved, search });
          }}
        />
        <TabContainer
          setIsArchieved={setIsArchieved}
          isArchieved={isArchieved}
        />
      </Box>
      {isLoading ? (
        <ExperiencesLoading />
      ) : (
        <InfiniteScroll
          dataLength={data.length}
          next={() => fetchData({ skipCount: skip, isArchieved })}
          hasMore={hasMore}
          loader={<p style={{ textAlign: "center" }}>Loading</p>}
          endMessage={
            <p style={{ textAlign: "center" }}>
              <b>Yay! You have seen it all</b>
            </p>
          }
        >
          <Box
            aria-disabled={isLoading}
            display="flex"
            flexDirection="row"
            alignItems="stretch"
            justifyContent="center"
            flexWrap="wrap"
            gap={10}
            mt={10}
          >
            {data.map((m, i) => (
              <CPCard
                setIsArchieved={setIsArchieved}
                key={m._id}
                data={m}
                setData={setData}
              />
            ))}
          </Box>
        </InfiniteScroll>
      )}
    </Container>
  );
};

export default CustomProductList;

const TabContainer = ({ setIsArchieved, isArchieved }) => {
  return (
    <Box
      display="flex"
      flexDirection="row"
      gap={2}
      sx={{
        background: "#8080801c",
        p: 1,
        borderRadius: 3,
      }}
    >
      <Tab
        isActive={!isArchieved}
        name="Active"
        onClick={() => {
          setIsArchieved(false);
        }}
      />
      <Tab
        isActive={isArchieved}
        name="Archieved"
        onClick={() => {
          setIsArchieved(true);
        }}
      />
    </Box>
  );
};

const Tab = ({ isActive, name, ...props }) => {
  return (
    <Box
      {...props}
      sx={{
        background: isActive ? "#F3B358" : "",
        fontSize: "0.9rem",
        fontWeight: isActive ? 500 : 400,
        borderRadius: 2,
        px: 4,
        py: 1,
        cursor: "pointer",
      }}
    >
      {name}
    </Box>
  );
};
