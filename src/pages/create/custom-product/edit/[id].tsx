import CPLoading from "@/components/Create/CustomProduct/CPLoading";
import CPNoData from "@/components/Create/CustomProduct/CPNoData";
import CustomProductBody from "@/components/Create/CustomProduct/CustomProductBody";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import axios from "axios";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";

export async function getServerSideProps() {
  const { data: languagesData } = await axios.get(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/getLanguages`
  );
  const languages = languagesData.languages;
  const proficiencies = languagesData.languageProficiencies;
  return { props: { proficiencies, languages } };
}

const EditCustomProduct = ({ languages }) => {
  const router = useRouter();
  const { id } = router.query;
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState(null);
  const { showSnackbar } = useSnackbar();

  const handleError = () => {
    setIsLoading(false);
    showSnackbar("Failed to fetch custom product", {
      type: "warning",
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const { data } = await axiosInstance.get(`custom-product/get/${id}`, {
          params: { isAdmin: true },
        });
        if (data.success) {
          setData(data.data);
          setIsLoading(false);
        } else {
          handleError();
        }
      } catch (error) {
        console.log("Something went wrong in fetchData", error);
        handleError();
      }
    };

    if (id) {
      fetchData();
    }
  }, [id]);

  if (isLoading) {
    return <CPLoading />;
  }

  if (!data) {
    return <CPNoData />;
  }

  return <CustomProductBody languages={languages} data={data} />;
};

export default EditCustomProduct;
