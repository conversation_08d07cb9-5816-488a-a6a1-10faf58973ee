import BackButton from "@/components/Create/student/BackButton";
import AllStudentDetails from "@/components/Create/student/StudentDetails";
import { Box, Container, Typography } from "@mui/material";
import axios from "axios";
import Link from "next/link";
import React, { useState } from "react";

export async function getServerSideProps(context) {
  const { id } = context.params;
  const { data } = await axios.post(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/user/get-user`,
    {
      clerkId: id,
      populateAll: true,
    }
  );
  const studentDetails = data.data;
  return { props: { studentDetails } };
}

const StudentDetail = ({ studentDetails }) => {
  const [studentInfo, setStudentInfo] = useState(studentDetails);

  return (
    <Container
      sx={{
        display: "flex",
        flexDirection: {
          xs: "column",
          md: "row",
        },
        padding: { xs: "1rem", sm: "2rem", md: "4rem" },
        gap: 3,
      }}
    >
      <Box display="flex" flexDirection="row" gap={2}>
        <BackButton sx={{ position: "relative" }} />
        <Typography
          fontWeight={700}
          fontSize="1.75rem"
          mb={5}
          sx={{
            display: {
              xs: "flex",
              md: "none",
            },
          }}
        >
          Student Details
        </Typography>
      </Box>
      <Box display="flex" flexDirection="column" width="100%">
        <Typography
          fontWeight={700}
          fontSize="1.75rem"
          mb={5}
          sx={{
            display: {
              xs: "none",
              md: "flex",
            },
          }}
        >
          Student Details
        </Typography>
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="end"
          mb={5}
        >
          <Box
            sx={{
              border: "1px solid #B3B3B3",
              borderRadius: "5px",
              fontSize: 15,
              padding: "5px 10px",
              color: "#000",
              fontWeight: 600,
              cursor: "pointer",
              width: {
                xs: "100%",
                md: "auto",
              },
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Link
              style={{
                color: "#000",
              }}
              href={`/create/student-dashboard/student/edit/${studentInfo.clerkId}`}
            >
              Edit Profile
            </Link>
          </Box>
        </Box>
        <AllStudentDetails studentInfo={studentInfo} />
      </Box>
    </Container>
  );
};

export default StudentDetail;
