import ManageProfile from "@/pages/manage-profile";
import axios from "axios";
import React, { useState } from "react";

export async function getServerSideProps(context) {
  const { id } = context.params;
  const { data } = await axios.post(
    `${process.env.NEXT_PUBLIC_BASE_URL}api/user/get-user`,
    {
      clerkId: id,
    }
  );
  const studentDetails = data.data;
  return { props: { studentDetails } };
}

const StudentEdit = ({ studentDetails }) => {
  const [studentInfo, setStudentInfo] = useState(studentDetails);

  return (
    <ManageProfile
      isStudentDashboard
      studentInfo={studentInfo}
      setStudentInfo={setStudentInfo}
    />
  );
};

export default StudentEdit;
