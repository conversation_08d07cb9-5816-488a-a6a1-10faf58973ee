import AboutUsLayout from "@/components/Aboutus";
import { WhyVideoLoading } from "@/components/Aboutus/WhyVideo";
import StackedCard from "@/components/StackedCard";
import { foundersMessage, whyPatitoData } from "@/constant/about/WhyPatito";
import useTranslate from "@/hooks/useTranslate";
import { Box, Typography } from "@mui/material";
import dynamic from "next/dynamic";
import Image from "next/image";
import React from "react";

const WhyVideo = dynamic(() => import("@/components/Aboutus/WhyVideo"), {
  loading: () => <WhyVideoLoading />,
  ssr: false,
});

const WhyPatitoFeo = () => {
  const { translate } = useTranslate();
  return (
    <AboutUsLayout>
      <Box
        width="100%"
        display="flex"
        flexDirection="row"
        // alignItems="center"
        justifyContent="center"
        p={5}
      >
        <WhyVideo />
      </Box>

      <Typography fontWeight="800" fontSize={24} my={10}>
        {translate(whyPatitoData.title)}
      </Typography>

      <Box>
        {whyPatitoData.description.map((m, i) => (
          <Typography
            fontWeight="500"
            key={i}
            mb={10}
            sx={{
              textAlign: {
                xs: "center",
                md: "left",
              },
              fontSize: {
                xs: 15,
                md: 17,
              },
            }}
          >
            {translate(m)}
          </Typography>
        ))}
      </Box>

      <StackedCard color="#F9B238">
        <Box
          p={3}
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: {
              xs: "column",
              md: "row",
            },
            justifyContent: {
              md: "space-between",
            },
          }}
        >
          <Box
            sx={{
              width: {
                xs: "100%",
                md: "73%",
              },
              height: "100%",
              position: "relative",
              borderRadius: 3,
            }}
          >
            <Box
              display="flex"
              flexDirection="row"
              sx={{
                mb: {
                  xs: 10,
                },
              }}
            >
              <Quote />
              <Quote />
            </Box>
            <Typography
              textAlign="left"
              my={5}
              sx={{
                fontWeight: {
                  xs: "700",
                  sm: "500",
                },
              }}
            >
              {translate(foundersMessage.quote)}
            </Typography>
            <Box
              sx={{
                display: {
                  xs: "none",
                  md: "flex",
                },
                flexDirection: "column",
              }}
            >
              <Typography textAlign="left" fontWeight="800" mt={4}>
                {foundersMessage.name}
              </Typography>
              <Typography textAlign="left" color="#64748B" fontSize={14}>
                {translate(foundersMessage.designation)}
              </Typography>
            </Box>
          </Box>
          <Box
            sx={{
              width: {
                xs: "100%",
                md: "25%",
              },
              display: {
                xs: "none",
                md: "flex",
              },
              position: "relative",
              borderRadius: 3,
              overflow: "hidden",
            }}
          >
            <Image
              src="/images/about/whyPatito/ty.webp"
              objectFit="cover"
              objectPosition="center"
              fill
              alt="Tys pic"
            />
          </Box>
          <Box
            flexDirection="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{
              display: {
                xs: "flex",
                md: "none",
              },
            }}
          >
            <Box>
              <Typography textAlign="left" fontWeight="800" mt={4}>
                {foundersMessage.name}
              </Typography>
              <Typography textAlign="left" color="#64748B" fontSize={14}>
                {translate(foundersMessage.designation)}
              </Typography>
            </Box>
            <Image
              src="/images/about/whyPatito/ty.webp"
              alt="Tys pic"
              height={90}
              width={90}
              objectFit="cover"
              objectPosition="center"
              style={{
                borderRadius: 8,
              }}
            />
          </Box>
        </Box>
      </StackedCard>
    </AboutUsLayout>
  );
};

export default WhyPatitoFeo;

const Quote = () => {
  return (
    <Box
      sx={{
        height: {
          xs: 20,
          md: 43,
        },
        width: {
          xs: 20,
          md: 26,
        },
      }}
    >
      <svg viewBox="0 0 26 43" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M10.1121 18.096C10.5812 13.0448 12.7823 10.1809 21.3775 9.97063C23.079 9.929 24.5 8.58447 24.5 6.8824V3C24.5 1.34315 23.1534 -0.00958209 21.498 0.0596391C6.25032 0.697223 0.5 6.56879 0.5 23.5V40C0.5 41.6569 1.84315 43 3.5 43H22.9294C24.6136 43 25.9678 41.614 25.9286 39.9303L25.566 24.3372C25.529 22.7454 24.2549 21.4598 22.6635 21.4085L12.9033 21.0937C11.2849 21.0414 9.96242 19.7082 10.1121 18.096Z"
          fill="#14A79C"
        />
      </svg>
    </Box>
  );
};
