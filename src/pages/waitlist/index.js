import React, { useState } from "react";
import { Container, <PERSON>, Typography, But<PERSON> } from "@mui/material";
import TextBoxWithButton from "@/components/TextBoxWithButton";
import Image from "next/image";
import { userSignInValidator } from "../../api/validators";
import { addUserSubmission, sendWaitlistEmail } from "@/utils/sendEmail";
import Link from "next/link";
import patitoLogo from "@/../public/images/icons/patito-feo.svg";
import Head from "next/head";
import { useSnackbar } from "@/hooks/useSnackbar";

const ADDRESS = "/api/launch-waitlist/";

export default function LaunchWaitlist() {
  const [submitted, setSubmitted] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false); //TODO: HANDLE ERROR
  const [email, setEmail] = useState(""); // Track email state for resending email
  const { showSnackbar } = useSnackbar();

  const emailValidation = async (email) => {
    try {
      const isValid = await userSignInValidator.parseAsync({ email });
      if (isValid) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  };

  const handleSendEmail = async (email) => {
    try {
      const { data, success } = sendWaitlistEmail(email);
      if (success) {
        showSnackbar("Sent Waitlist Email successfully", {
          type: "success",
        });
      } else {
        showSnackbar("Failed to send waitlist email", {
          type: "success",
        });
      }
    } catch (error) {
      // console.error("Something went wrong in handleSendEmail due to ", error);
      showSnackbar("Failed to send waitlist email", {
        type: "success",
      });
    }
  };

  const handleSubmit = async (email) => {
    setSubmitted(true);
    setEmail(email); // Save email for resending later

    const validEmail = await emailValidation(email);
    if (validEmail) {
      const res = addUserSubmission(email, ADDRESS);
      if (res) {
        setIsSuccess(true);
        await handleSendEmail(email);
      } else {
        // Handle error state for bad response
        setSubmitted(false);
        setIsError(true);
      }
    } else {
      // Handle invalid email case
      setSubmitted(false);
      setIsError(true);
    }
  };

  return (
    <>
      <Head>
        <title>Waitlist</title>
      </Head>
      <Container
        spacing={5}
        sx={{
          mt: { xs: 10, sm: 20 },
          padding: { xs: "0px 20px", md: "0px opx" },
        }}
      >
        <div className="flex flex-col justify-center items-center">
          <Image
            src={patitoLogo}
            alt="logo"
            width={150}
            height={150}
            className="w-30 h-30 md:w-50 md:h-50 mb-12"
          />
        </div>

        {submitted && isSuccess ? (
          <div>
            <div className="flex flex-col justify-center items-center text-center">
              <h1 variant="h2" className="font-medium">
                Congratulations!<br></br> You are all set!
              </h1>
              <p variant="p" className="max-w-80 text-center ">
                You&apos;re one step closer! 🎉<br></br>We will send you an
                email with all the details as soon as a spot opens up. Stay
                tuned!
              </p>
            </div>
            <div className="flex flex-col sm:flex-row mt-5 justify-center items-center">
              <Link className="" href="/games/adivina-game">
                <Button
                  className="mx-2 mb-5"
                  type="submit"
                  variant="contained"
                  sx={{
                    color: "white",
                    borderRadius: "4px 4px 4px 4px",
                    px: 4,
                  }}
                >
                  Play a Game
                </Button>
              </Link>

              <Button
                className="mx-2 mb-5"
                type="submit"
                variant="contained"
                onClick={() => sendWaitlistEmail(email)} // Use saved email to resend
                sx={{
                  color: "white",
                  backgroundColor: "#02B199",
                  borderRadius: "4px 4px 4px 4px",
                  px: 4,
                }}
              >
                Resend Email
              </Button>
            </div>
          </div>
        ) : (
          <div>
            <Box
              display="flex"
              flexDirection="column"
              justifyContent="center"
              alignItems="center"
            >
              <Typography
                variant="h4"
                className="pb-2"
                sx={{ fontWeight: "500" }}
              >
                Join The <span style={{ color: "#F3B358" }}>Waitlist</span>
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  maxWidth: "400px",
                  width: { xs: "90%", sm: "80%", md: "60%", lg: "40%" },
                  textAlign: "center",
                  mb: 8,
                }}
              >
                Be the first to know when enrollment opens. Send us your email
                to secure your spot and start your language learning with Patito
                Feo!
              </Typography>
            </Box>
            <TextBoxWithButton
              handleSubmit={handleSubmit}
              buttonText="Join Waitlist"
              placeholderText="Your Email Address"
            />
            <Link
              className="flex justify-center mt-10 underline-offset-2 visited:text-black"
              href="/"
              passHref
            >
              Go Back Home
            </Link>
          </div>
        )}
      </Container>
    </>
  );
}
