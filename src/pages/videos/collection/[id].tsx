import {
  Container,
  Box,
  Typography,
  CircularProgress,
  Grid,
} from "@mui/material";
import Image from "next/image";
import TagComponet from "@/../../src/components/dashboard/TagComponent";
import img from "@/../../public/images/dashboard/mylearning.webp";
import avatar from "@/../../public/images/dashboard/mylearning.webp";
import { useRouter } from "next/router";
import axiosInstance from "@/utils/interceptor";
import { VideosCollectionType } from "@/api/mongoTypes";
import VideoCollectionBreadCrumb from "@/components/dashboard/VideoCollectionBreadCrumb";
import Head from "next/head";
import ContentCard from "@/components/dashboard/ContentCard";
import { useMemo } from "react";
import NoVideoCollection from "@/components/dashboard/NoVideoCollection";

const collectionMenuPathStyles = {
  fontWeight: "400",
  fontSize: "16px",
  color: "#6D6D6D",
};
const collectionMenuContainerStyles = {
  marginTop: "40px",
  marginBottom: "50px",
};
const collectionMenuBodyStyles = {
  display: "flex",
  flexDirection: "row",
  marginBottom: "27px",
  mt: 4,
};
const collectionDescriptionContainerStyles = {
  marginLeft: "25px",
};
const collectionTitleStyles = {
  fontWeight: "700",
  fontSize: "32px",
  color: "#000000",
  marginBottom: "10px",
};
const collectionBodyStyles = {
  fontWeight: "500",
  fontSize: "20px",
  color: "#6D6D6D",
  marginBottom: "20px",
};
const tagStyles = {
  backgroundColor: "#D7F7F5",
  color: "#14A79C",
  fontWeight: "500",
  fontSize: "13px",
  marginRight: "5px",
};
const tagContainerStyles = {
  marginBottom: "20px",
};
const collectionStatisticStyles = {
  fontWeight: "500",
  fontSize: "14px",
  color: "#3C3C3C",
  marginRight: "23px",
};
const collectionStatisticsContainerStyles = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
};
const bulletPointStyles = {
  color: "#F9B238",
  marginRight: "5px",
};
const videoContentTitleStyles = {
  fontWeight: "500",
  fontSize: "20px",
  color: "#000000",
  margin: "23px 0px",
};
const collectionVideoListContainerStyles = { borderTop: "2px solid #D9D9D9" };
const gridStyles = { width: { xs: "100%", sm: "1000px" } };

const progressCircleStyles = { marginRight: "8px" };

const CircularProgressWithLabel = ({ value, customStyles }) => {
  return (
    <Box
      sx={{
        position: "relative",
        display: "inline-flex",
        ...customStyles,
      }}
    >
      {/* Circular Progress */}
      <CircularProgress
        variant="determinate"
        value={value}
        size={40} // Size of the progress bar
        thickness={3} // Thickness of the circle
        sx={{
          color: value === 100 ? "primary.main" : "#F9B238", // Change color if completed
        }}
      />
      {/* Display Percentage Value */}
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
        }}
      >
        <Typography variant="caption" component="div" color="text.secondary">
          {`${Math.round(value)}%`}
        </Typography>
      </Box>
    </Box>
  );
};

type CollectionPageProps = React.FC<{
  collection: VideosCollectionType;
  error: string;
}>;

const CollectionPage: CollectionPageProps = ({ collection, error }) => {
  const duration = useMemo(() => {
    if (collection) {
      const inMinutes = +collection.videoDuration / 60;
      return inMinutes.toFixed(2);
    }
    return null;
  }, [collection]);

  const progress = useMemo(() => {
    if (collection) {
      const percentage = collection?.videoCollectionProgress?.progress;
      if (percentage) {
        return +percentage;
      }
      return null;
    }
    return null;
  }, [collection]);

  console.log("collection", collection);

  if (error) {
    return <NoVideoCollection isVideo={false} />;
  }
  if (!collection) {
    return <NoVideoCollection isVideo={false} />;
  }

  console.log("collection", collection);

  return (
    <>
      <Head>
        <title>{collection.name} | Collection</title>
      </Head>
      <Container>
        <VideoCollectionBreadCrumb title={String(collection.name)} />
        <Box sx={collectionMenuBodyStyles}>
          <Image
            src={collection.coverImageUrl}
            width="167"
            height="146"
            alt={String(collection.name)}
            style={{ borderRadius: "12px" }}
          />
          <Box sx={collectionDescriptionContainerStyles}>
            <Typography sx={collectionTitleStyles}>
              {collection.name}
            </Typography>
            <Typography sx={collectionBodyStyles}>{collection.desc}</Typography>
            <Box sx={tagContainerStyles}>
              {collection.categories.map((tag) => (
                <TagComponet
                  key={String(tag)}
                  label={tag}
                  tagStyles={tagStyles}
                />
              ))}
            </Box>
            <Box sx={collectionStatisticsContainerStyles}>
              <Typography sx={collectionStatisticStyles}>
                <span style={bulletPointStyles}>&#8226;</span>
                {+collection.videoCount} Videos
              </Typography>
              {duration && (
                <Typography sx={collectionStatisticStyles}>
                  <span style={bulletPointStyles}>&#8226;</span>
                  {duration} Mins
                </Typography>
              )}
              {progress && (
                <>
                  <span style={bulletPointStyles}>&#8226;</span>
                  <CircularProgressWithLabel
                    value={+progress}
                    customStyles={progressCircleStyles}
                  />
                  <Typography sx={collectionStatisticStyles}>
                    Completed
                  </Typography>
                </>
              )}
            </Box>
          </Box>
        </Box>
        <Box sx={collectionVideoListContainerStyles}>
          <Typography sx={videoContentTitleStyles}>Contents</Typography>
          {/* <DashboardGrid data={collection.videos}></DashboardGrid> */}
          <Box sx={gridStyles}>
            <Grid container spacing={11}>
              {!collection.videos ? (
                <Grid item xs={12} sm={6} md={4}>
                  <Typography>
                    Could not find any videos or collections
                  </Typography>
                </Grid>
              ) : (
                collection.videos.map((item, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <ContentCard
                      isAdmin={false}
                      data={item}
                      isLearning
                      collectionId={collection._id}
                    />
                  </Grid>
                ))
              )}
            </Grid>
          </Box>
        </Box>
      </Container>
    </>
  );
};

export async function getServerSideProps(context) {
  try {
    const { id } = context.params;
    const userData = context.req.cookies["user_data"];
    const userDetails = JSON.parse(userData);
    const userId = userDetails?._id;

    const { data } = await axiosInstance.get(`collection/get/${id}`, {
      params: {
        needCoverImages: true,
        needLikeStatus: true,
        needProgress: true,
        needVideos: true,
      },
      headers: {
        userid: userId,
      },
    });

    return {
      props: {
        collection: data?.data ?? null,
        error: "",
      },
    };
  } catch (error) {
    return {
      props: {
        collection: null,
        error: error.message ?? "Something went wrong",
      },
    };
  }
}
export default CollectionPage;
