import React, { useEffect, useMemo, useState } from "react";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import LikeComponent from "@/../../src/components/dashboard/LikeComponent";
import Image from "next/image";
import TagComponent from "@/../../src/components/dashboard/TagComponent";
import Head from "next/head";
import { VideoPlayer } from "@/components/VideoPlayer";
import axiosInstance from "@/utils/interceptor";
import { UserType, VideoType } from "@/api/mongoTypes";
import { getUserFullName } from "@/utils/format";
import VideoCollectionBreadCrumb from "@/components/dashboard/VideoCollectionBreadCrumb";
import { useSnackbar } from "@/hooks/useSnackbar";
import { useRouter } from "next/router";
import { getVideoState } from "@/utils/dashboard";
import { PROGRESS_STATUS } from "@/constant/Enums";
import NoVideoCollection from "@/components/dashboard/NoVideoCollection";

const titleContainerStyles = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  padding: { xs: "0px 10px", sm: "0px" },
};

const titleStyles = { fontWeight: "700", fontSize: "32px", color: "#3C3C3C" };

const avatarTagContainerStyles = {
  display: "flex",
  flexDirection: {
    xs: "column",
    sm: "row",
  },
  justifyContent: "space-between",
  alignItems: { xs: "start", sm: "center" },
  padding: { xs: "0px 10px", sm: "0px" },
};
const avatarContainerStyles = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "white",
  borderRadius: "30px",
  margin: "10px 0px",
  padding: "10px 10px",
  boxShadow: "0px 6px 48px 0px #0000000D",
  overflow: "hidden",
};

const avatarTitleStyles = {
  fontSize: "16px",
  fontWeight: "500",
  color: "#717171",
  padding: "0px 15px",
};

const tagStyles = {
  color: "white",
  backgroundColor: "#72CAC4",
  fontSize: "13px",
  fontWeight: "500",
  marginLeft: { xs: "0px", sm: "15px" },
  marginBottom: { xs: "10px", sm: "0px" },
};

const tagContainerStyles = {
  display: "flex",
  width: { xs: "100%", sm: "auto" },
  flexDirection: "row",
  justifyContent: "space-evenly",
};

type VideoDetailPageProps = React.FC<{
  video: VideoType;
}>;

const VideoDetailPage: VideoDetailPageProps = ({ video }) => {
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [isMarkedEnded, setIsMarkEnded] = useState(false);
  const [isMarkedStarted, setIsMarkStarted] = useState(false);
  const router = useRouter();
  const collectionId = router?.query?.collectionId;
  const fromCollection = !!collectionId;
  const { showSnackbar } = useSnackbar();
  const [isEnded, setIsEnded] = useState(false);
  const [isVideoProgressUpdating, setIsVideoProgressUpdating] = useState(false);

  useEffect(() => {
    if (video && video?.progress && "state" in video?.progress) {
      setIsEnded(video?.progress?.state === PROGRESS_STATUS.COMPLETE);
    }
  }, [video]);

  const creatorDetails = useMemo(() => {
    const creator = video?.creator;
    if (
      creator &&
      typeof creator !== "string" &&
      creator?._id &&
      "profileImageUrl" in creator
    ) {
      return creator as UserType;
    }
    return null;
  }, [video]);

  if (!video) {
    return <NoVideoCollection isVideo />;
  }

  const fullName = getUserFullName(creatorDetails);

  const handleVideoProgress = async (duration: number) => {
    if (isVideoProgressUpdating) {
      return;
    }

    const handleError = () => {
      setIsVideoProgressUpdating(false);
      showSnackbar("Failed to update video progress", {
        type: "error",
      });
    };

    try {
      setIsVideoProgressUpdating(true);
      const { data: respData } = await axiosInstance.post(`video/progress`, {
        videoId: video._id,
        progress: duration,
        state: getVideoState(duration),
        collectionId: fromCollection ? String(collectionId) : "",
      });
      if (respData?.success) {
        console.log("Success");
      } else {
        handleError();
      }
      setIsVideoProgressUpdating(false);
    } catch (error) {
      handleError();
      console.log(
        "Somethging went wrong in handleVideoProgress due to ",
        error
      );
    }
  };

  return (
    <div>
      <Head>
        <title>{video.title}</title>
      </Head>
      <div style={{ maxWidth: "1000px", margin: "auto" }}>
        <VideoCollectionBreadCrumb title={video.title} />
        <Box sx={titleContainerStyles}>
          <Typography sx={titleStyles}>{video.title}</Typography>
          <LikeComponent
            isLiked={video.isLiked}
            id={video._id}
            isCollection={false}
          />
        </Box>
        <Box sx={avatarTagContainerStyles}>
          <Box sx={avatarContainerStyles}>
            <Image
              src={creatorDetails?.profileImageUrl}
              width={40}
              height={40}
              style={{
                borderRadius: 20,
              }}
              alt={`avatar of the creator ${fullName}`}
            />
            <Typography sx={avatarTitleStyles}>{fullName}</Typography>
          </Box>
          <Box sx={tagContainerStyles}>
            {video.categories.map((tag) => (
              <TagComponent key={tag} label={tag} tagStyles={tagStyles} />
            ))}
          </Box>
        </Box>
        <VideoPlayer
          src={video.videoUrl}
          poster={video.thumbnailUrl}
          onPlayerChange={(videoPlayer) => {}}
          onUnmount={(percent) => {
            if (!isEnded) {
              handleVideoProgress(percent);
            }
          }}
          onEnd={(percent) => {
            if (!isEnded) {
              setIsEnded(true);
              handleVideoProgress(percent);
            }
          }}
          onPause={(percent) => {
            if (!isEnded) {
              handleVideoProgress(percent);
            }
          }}
          onResume={(percent) => {
            if (!isEnded) {
              handleVideoProgress(percent);
            }
          }}
          onPlay={(percent) => {
            console.log("percent on onPlay", percent);
            if (!isEnded) {
              handleVideoProgress(percent);
            }
          }}
        />
      </div>
    </div>
  );
};

export default VideoDetailPage;

export async function getServerSideProps(context) {
  const { id } = context.params;
  const userData = context.req.cookies["user_data"];
  const userDetails = JSON.parse(userData);
  const userId = userDetails?._id;

  const { data } = await axiosInstance.get(`video/get/${id}`, {
    params: {
      needCreator: true,
      needLikeStatus: true,
      needProgress: true,
      needThumbnail: true,
      needVideoUrl: true,
      needEsSubtitleUrl: true,
      needEnSubtitleUrl: true,
    },
    headers: {
      userid: userId,
    },
  });

  return {
    props: {
      video: data.data,
    },
  };
}
