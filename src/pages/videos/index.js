import axiosInstance from "@/utils/interceptor";
import DashboardSection from "@/components/dashboard/DashboardSection";
import VideoLayout from "@/components/dashboard/VideoLayout";

const DashboardPage = ({
  learning = [],
  categories = [],
  featured = [],
  collections = [],
  liked = [],
}) => {
  return (
    <VideoLayout>
      <DashboardSection
        learning={learning}
        featured={featured}
        collections={collections}
        categories={categories}
        liked={liked}
      />
    </VideoLayout>
  );
};

export default DashboardPage;

export async function getServerSideProps(context) {
  try {
    const userData = context.req.cookies["user_data"];
    const userDetails = JSON.parse(userData);
    const userId = userDetails?._id;

    const commonHeaders = {
      headers: {
        userid: userId,
      },
    };

    const [learningRes, categoriesRes, featuredRes, collectionsRes, likedRes] =
      await Promise.all([
        axiosInstance.get("learning/all", {
          params: {
            skip: 0,
            limit: 5,
          },
          ...commonHeaders,
        }),
        axiosInstance.get("utils/get-categories", commonHeaders),
        axiosInstance.get("video/all", {
          params: {
            isFeatured: true,
            skip: 0,
            limit: 5,
            needCreator: true,
            needProgress: true,
            needThumbnail: true,
            needLikeStatus: true,
            needProficiencyLevel: true,
          },
          ...commonHeaders,
        }),
        axiosInstance.get("collection/all", {
          params: {
            isFeatured: true,
            needCoverImages: true,
            skip: 0,
            limit: 5,
            needLikeStatus: true,
          },
          ...commonHeaders,
        }),
        axiosInstance.get("dashboard/liked", {
          params: {
            skip: 0,
            limit: 5,
          },
          ...commonHeaders,
        }),
      ]);

    console.log({
      learningRes,
    });

    return {
      props: {
        learning: learningRes?.data?.data || [],
        categories: categoriesRes?.data?.data || [],
        featured: featuredRes?.data?.data || [],
        collections: collectionsRes?.data?.data || [],
        liked: likedRes?.data?.data || [],
      },
    };
  } catch (error) {
    console.error("Something went wrong in getServerSideProps", error);
    return {
      props: {
        learning: [],
        categories: [],
        featured: [],
        collections: [],
        liked: [],
      },
    };
  }
}
