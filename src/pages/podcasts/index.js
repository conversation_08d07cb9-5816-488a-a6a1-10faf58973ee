import React, { useEffect, useState } from "react";
import Container from "@mui/material/Container";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import SearchBar from "@/components/events/eventSearchBar";
import EventCard from "@/components/events/eventCard";
import fs from "fs";
import path from "path";
import { ConstructionModal } from "@/components/Modals/ConstructionModal";
import Head from "next/head";
const PodcastPageLayout = ({ children }) => (
  <Container
    maxWidth="lg"
    sx={{
      padding: { xs: "1rem", sm: "2rem" },
      backgroundColor: "background.paper",
    }}
  >
    <Typography variant="h4" sx={{ marginBottom: "1rem" }}>
      Podcasts
    </Typography>
    {children}
  </Container>
);

export default function PodcastsPage({ podcastData }) {
  const [filteredPodcasts, setFilteredPodcasts] = useState([]);
  const [filteredEpisodes, setFilteredEpisodes] = useState([]);
  const [isConstructionModalOpen, setIsConstructionModalOpen] = useState(true);

  const handleSearch = (event, value) => {
    const filtered = filteredEpisodes.filter(
      (podcast) =>
        podcast.title.includes(value) || podcast.description.includes(value)
    );
    setFilteredPodcasts(filtered);
  };

  useEffect(() => {
    const extractPodcastData = (podcasts) => {
      const podcastList = [];
      const episodeList = [];

      podcasts.forEach((podcast) => {
        // Extract and store podcast ID and name
        podcastList.push({
          podcast_id: podcast.podcast_id,
          podcast_name: podcast.podcast_name,
        });

        // Extract and store episodes
        if (podcast.episodes && podcast.episodes.length > 0) {
          episodeList.push(...podcast.episodes);
        }
      });

      return { podcastList, episodeList };
    };
    const { podcastList, episodeList } = extractPodcastData(podcastData);
    setFilteredPodcasts(podcastList);
    setFilteredEpisodes(episodeList);
  }, [podcastData]);

  return (
    <>
      <Head>
        <title>Podcasts</title>
      </Head>{" "}
      <PodcastPageLayout>
        <SearchBar
          events={filteredEpisodes}
          handleSearch={handleSearch}
          helperText="Search podcasts"
        />

        <Grid container spacing={4}>
          {filteredEpisodes.map((episode) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={episode.id}>
              <EventCard event={episode} />
            </Grid>
          ))}
        </Grid>
        <ConstructionModal
          isOpen={isConstructionModalOpen}
          handleClose={() => setIsConstructionModalOpen(false)}
        />
      </PodcastPageLayout>
    </>
  );
}

export async function getStaticProps() {
  const filePath = path.resolve("src", "data", "podcasts", "podcastData.json");
  const fileContent = fs.readFileSync(filePath, "utf8");
  let podcastData = JSON.parse(fileContent);

  podcastData = podcastData.sort((a, b) => {
    const dateA = new Date(a.airDate);
    const dateB = new Date(b.airDate);
    return dateA - dateB;
  });

  return {
    props: {
      podcastData,
    },
  };
}
