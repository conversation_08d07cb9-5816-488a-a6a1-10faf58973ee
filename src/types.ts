import { SxProps, Theme } from "@mui/material";
import { Types } from "mongoose";
import {
  BaseInterestType,
  CartType,
  ClassesPricingType,
  ClubType,
  EventOnType,
  EventSchemaType,
  EventsPriceDetailsType,
  InterestType,
  LanguageProficiencyType,
  LanguageType,
  MembershipType,
  PlanType,
  ScheduledStudentType,
  ScheduleType,
  SingleMembershipType,
  SingleScheduledStudentType,
  TransactionType,
  UserType,
  VideoCollectionProgressSchemaType,
  VideoProgressSchemaType,
  VideosCollectionType,
  VideoType,
} from "./api/mongoTypes";
import { CURRENCY_ENUM, LEVEL_TYPES, PLAN_FOR } from "./constant/Enums";

export type SingleLanguageType = {
  language: LanguageType;
  proficiency: LanguageProficiencyType;
};

export type langugaeStateTypes = SingleLanguageType[] | [];

export type InterestTypeWithpreference = {
  preference: string;
} & (BaseInterestType | InterestType);

export type interestStateTypes = InterestTypeWithpreference[] | [];

export type Maybe<T> = T | null | undefined;
export type MaybeEmptytArray<T> = T[] | [];

export type MaybeObjectId<T> = T | Types.ObjectId;

export type ValueOf<T> = T[keyof T];

export type TeamDataType = {
  name: string;
  designation: string;
  description: {
    en: string;
    es: string;
  };
  img1: string;
  img2: string;
};

export type localPlanType = {
  planId: string;
  startDate: Date;
  endDate: Date;
  planFor: PLAN_FOR;
  emailId: string;
  isDateEnabled: boolean;
};

export type LangugaesListType = LanguageType[] | [];
export type LanguageProficiencyListType = LanguageProficiencyType[] | [];

export type langDataType = null | {
  languages: LangugaesListType;
  languageProficiencies: LanguageProficiencyListType;
  isError: boolean;
};

export type ScheduleTeacherType = {
  location: string;
  level: LEVEL_TYPES;
  teacherId: string;
};

export type StudentScheduleInfo = ScheduleType & {
  classPricing: ClassesPricingType;
  student: SingleScheduledStudentType;
  teacherDetails: UserType[];
};

export type MyScheduleResponseType = "schedule" | "event";

export type MySingleScheduleResponse = {
  type: MyScheduleResponseType;
  data: EventSchemaType | StudentScheduleInfo;
  transactionId: String;
  userId: String;
  isBought: Boolean;
};

export type MySchedulesResponse = MaybeEmptytArray<MySingleScheduleResponse>;

export type StudyingType = {
  language: String;
  proficiency: String;
};

export type purchaseDetailsType = TransactionType & {
  classesDetails: {
    classInfo: ClassesPricingType;
    plans: PlanType;
  };
  clubsDetails: {
    clubInfo: ClubType;
    memberships: SingleMembershipType;
    price: Number;
    currency: CURRENCY_ENUM;
  };
  eventIds: EventSchemaType;
  eventsPriceDetails: EventsPriceDetailsType;
};

export type MaybeCartSchedule =
  | CartType
  | MySingleScheduleResponse
  | purchaseDetailsType;

export type EventSchemaWithSingleEvent = EventSchemaType & {
  eventOn: EventOnType | EventOnType[];
  boughtCount: {
    [key: string]: number;
  };
};

export type LikedListType = {
  _id: string;
  collectionId: string | null;
  createdAt: Date;
  updatedAt: Date;
  videoId: string | null;
  type: "collection" | "video";
  collection?: VideosCollectionType;
  video?: VideoType;
};

export type MaybeFileOrUrl = Maybe<File | string>;

export type ProgressType =
  | ({ type: "video" } & VideoProgressSchemaType)
  | ({ type: "collection" } & VideoCollectionProgressSchemaType);

export type MUIStyle = SxProps<Theme>;
