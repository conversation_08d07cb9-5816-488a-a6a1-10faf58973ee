import React, { useState, useCallback, RefObject } from "react";
import { isInside } from "../utils/coordinateUtils";

/**
 * A hook to enable draggable functionality on an element.
 *
 * @param {Object} params - The parameters object.
 * @param {RefObject} params.parentRef - A ref to the parent element in which dragging is constrained.
 *
 * @returns {Object} - An object containing handlers and state for managing draggable functionality.
 */

const useDraggable = ({ parentRef }) => {
  const [coordinate, setCoordinate] = useState({
    block: { x: 0, y: 0 },
    blockInitial: { x: 0, y: 0 },
    initial: { x: 0, y: 0 },
    movingBlockIndex: null,
  });

  const getCoordinates = (event) => {
    return "touches" in event
      ? { x: event.touches[0].clientX, y: event.touches[0].clientY }
      : { x: event.clientX, y: event.clientY };
  };

  const handleStart = useCallback((event, block, index) => {
    const startingCoordinates = getCoordinates(event);
    setCoordinate((prev) => ({
      ...prev,
      block,
      blockInitial: block,
      initial: startingCoordinates,
      movingBlockIndex: index,
    }));
    event.stopPropagation();
    event.preventDefault();
  }, []);

  const handleMove = useCallback(
    (event) => {
      if (coordinate.movingBlockIndex === null) {
        return;
      }
      const coordinates = getCoordinates(event);
      if (
        parentRef.current &&
        !isInside(parentRef.current, {
          left: coordinates.x,
          top: coordinates.y,
        })
      ) {
        handleEnd(event);
        return;
      }
      setCoordinate((prev) => {
        const diff = {
          x: coordinates.x - prev.initial.x,
          y: coordinates.y - prev.initial.y,
        };
        return {
          ...prev,
          block: {
            x: prev.blockInitial.x + diff.x,
            y: prev.blockInitial.y + diff.y,
          },
        };
      });
      event.preventDefault();
    },
    [coordinate, parentRef]
  );

  const handleEnd = useCallback((event) => {
    setCoordinate((prev) => ({
      ...prev,
      movingBlockIndex: null,
    }));
    event.preventDefault();
  }, []);

  return {
    handleMouseDown: (event, block, index) => handleStart(event, block, index),
    handleMouseMove: handleMove,
    handleMouseUp: handleEnd,
    handleTouchStart: (event, block, index) => handleStart(event, block, index),
    handleTouchMove: handleMove,
    handleTouchEnd: handleEnd,
    block: coordinate.block,
    movingBlockIndex: coordinate.movingBlockIndex,
  };
};

export default useDraggable;
