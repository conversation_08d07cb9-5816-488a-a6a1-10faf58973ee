import { useRouter } from "next/router";
import { useEffect } from "react";
import useTranslate from "./useTranslate";

const useRedirectToClass = ({ requiredLangauge = "" }) => {
  const router = useRouter();
  const { preferredLanguage } = useTranslate();

  useEffect(() => {
    if (preferredLanguage && requiredLangauge.length > 0) {
      if (requiredLangauge !== preferredLanguage) {
        router.push("/classes");
      }
    }
  }, [preferredLanguage, requiredLangauge]);

  return null;
};

export default useRedirectToClass;
