import { useUserContext } from "@/contexts/UserContext";
import { handleVisitor } from "@/utils/common";
import axiosInstance from "@/utils/interceptor";
import React, { useEffect, useState } from "react";

const useVisitor = () => {
  const { dbUser } = useUserContext();
  const [location, setLocation] = useState({
    latitude: null,
    longitude: null,
  });
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const userId = dbUser?._id;

  const fetchCoordinates = () => {
    try {
      const defaultOptions = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000,
      };

      const handleSuccess = (position) => {
        const { latitude, longitude } = position.coords;
        setLocation({
          latitude,
          longitude,
        });
        setError(null);
        setLoading(false);
      };

      const handleError = (err) => {
        let errorMessage = "An unknown error occurred";

        switch (err.code) {
          case err.PERMISSION_DENIED:
            errorMessage = "Location access denied by user";
            break;
          case err.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable";
            break;
          case err.TIMEOUT:
            errorMessage = "Location request timed out";
            break;
        }

        setError(errorMessage);
        setLoading(false);
      };

      // Get current position
      navigator.geolocation.getCurrentPosition(
        handleSuccess,
        handleError,
        defaultOptions
      );
    } catch (error) {
      console.error("Something went wrong in fetchCoordinates", error);
    }
  };

  useEffect(() => {
    if (!navigator.geolocation) {
      setError("Geolocation is not supported by this browser");
      setLoading(false);
      return;
    }
    fetchCoordinates();
  }, []);

  const handleSaveVisitor = async () => {
    try {
      const { data: respData } = await axiosInstance.put(
        `user/update-visitor`,
        location
      );
      if (respData?.success) {
        handleVisitor(false);
      }
    } catch (error) {
      console.error(
        "Something went wrong in hanbdleSaveVisitor due to ",
        error
      );
    }
  };

  useEffect(() => {
    if (location?.latitude && location?.longitude && userId) {
      const isItStore = localStorage.getItem("visitor") === "STORE";
      if (isItStore) {
        handleSaveVisitor();
      }
    }
  }, [location?.latitude, location?.longitude, userId]);

  return null;
};

export default useVisitor;
