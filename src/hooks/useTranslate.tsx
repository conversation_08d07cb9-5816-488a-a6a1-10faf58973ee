import { languageContext } from "@/contexts/LanguageContext";
import locales from "@/locale";
import { useContext } from "react";

const useTranslate = () => {
  const { language, setLanguage } = useContext(languageContext);

  function __lang(key: string, language: string): string {
    return locales[language]?.[key] || locales[`en`]?.[key] || ``;
  }
  const translate = (key: string) => __lang(key, language);

  return {
    translate,
    preferredLanguage: language,
    setPreferredLanguage: setLanguage,
  };
};

export default useTranslate;
