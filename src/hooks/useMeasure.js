import { useRef, useState, useEffect } from "react";
import ResizeObserver from "resize-observer-polyfill";

//https://codesandbox.io/s/login-form-example-using-react-spring-sn5xf?file=/src/useMeasure.js:0-440
export default function useMeasure() {
  const ref = useRef();

  const [bounds, set] = useState({ left: 0, top: 0, width: 0, height: 0 });
  const [ro] = useState(
    () => new ResizeObserver(([entry]) => set(entry.contentRect))
  );
  useEffect(() => ro.observe(ref.current, ro.disconnect), [ro]);
  return [{ ref }, bounds];
}
