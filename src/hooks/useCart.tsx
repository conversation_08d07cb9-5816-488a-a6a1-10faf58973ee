import { useState } from "react";
import { useSnackbar } from "./useSnackbar";
import axiosInstance from "@/utils/interceptor";
import { localPlanType } from "@/types";
import {
  CartType,
  ClassesPricingType,
  ClubType,
  EventOnType,
  EventSchemaType,
  MembershipType,
} from "@/api/mongoTypes";
import { useCartCountContext } from "@/contexts/CartCountContext";
import { useUserContext } from "@/contexts/UserContext";
import { getLocalCarts } from "@/utils/classes";
import useTranslate from "./useTranslate";

type handleAddToCartProps = {
  classesId?: ClassesPricingType;
  eventId?: string | EventSchemaType;
  clubId?: string | ClubType;
  plans?: localPlanType[];
  onSuccess?: () => void;
  memberships?: MembershipType;
  eventOn?: string | EventOnType;
};

type handleUpdateCartProps = {
  id: string;
  classesId?: ClassesPricingType;
  eventId?: string;
  clubId?: string;
  memberships?: MembershipType;
  plans?: localPlanType[];
  onUpdateSuccess: (data: CartType) => void;
};

const useCart = ({ onDeleteSuccess, onAddingSuccess }) => {
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isDeletingCart, setIsDeletingCart] = useState(false);
  const [isUpdatingCart, setIsUpdatingCart] = useState(false);
  const [isAddingManyToCart, setIsAddingManyToCart] = useState(false);
  const { showSnackbar } = useSnackbar();
  const { addOneCart, removeOneCart } = useCartCountContext();
  const { dbUser } = useUserContext();
  const isLoggedIn = !!dbUser;
  const { translate } = useTranslate();

  const handleAddToCart = async ({
    eventId = null,
    clubId = null,
    classesId = null,
    plans = [],
    memberships = [],
    onSuccess = () => {},
    eventOn = null,
  }: handleAddToCartProps) => {
    console.log({
      eventOn,
      eventId,
    });
    const handleError = () => {
      setIsAddingToCart(false);
      showSnackbar(translate("cart.failed-add"), {
        type: "error",
      });
    };

    const handleSuccess = () => {
      addOneCart();
      onAddingSuccess();
      showSnackbar(translate("cart.add-success"), {
        type: "success",
      });
      setIsAddingToCart(false);
      onSuccess();
    };

    try {
      setIsAddingToCart(true);
      const payload = {
        eventId,
        classesId,
        plans,
        _id: Date.now(),
        clubId,
        memberships,
        eventOn,
      };
      if (!isLoggedIn) {
        const cartItems = getLocalCarts();
        const newCarts = [...cartItems, payload];
        localStorage.setItem("CART", JSON.stringify(newCarts));
        handleSuccess();
      } else {
        delete payload._id;
        const { data: createdData } = await axiosInstance.post("cart/create", {
          eventId,
          classesId,
          plans,
          clubId,
          memberships,
          eventOn,
        });
        if (createdData.success) {
          handleSuccess();
        } else {
          handleError();
        }
      }
      setIsAddingToCart(false);
    } catch (error) {
      handleError();
    }
  };

  const handleDelete = async ({ id }) => {
    const handleError = () => {
      setIsDeletingCart(false);
      showSnackbar(translate("cart.failed-remove"), {
        type: "error",
      });
    };
    const handleSuccess = () => {
      removeOneCart();
      showSnackbar(translate("cart.success-remove"), {
        type: "success",
      });
      onDeleteSuccess();
      setIsDeletingCart(false);
    };

    try {
      setIsDeletingCart(true);
      if (isLoggedIn) {
        const { data: resData } = await axiosInstance.delete(
          `cart/delete/${id}`
        );
        if (resData.success && resData.data) {
          handleSuccess();
        } else {
          handleError();
        }
      } else {
        const cartItems = getLocalCarts();
        const newCarts = cartItems.filter((f) => f?._id !== id);
        localStorage.setItem("CART", JSON.stringify(newCarts));
        handleSuccess();
      }
      setIsDeletingCart(false);
    } catch (error) {
      handleError();
    }
  };

  const handleUpdateCart = async ({
    id,
    plans,
    classesId,
    onUpdateSuccess,
    clubId,
    memberships,
  }: handleUpdateCartProps) => {
    const showErrorMessage = () => {
      setIsUpdatingCart(false);
      showSnackbar(translate("cart.failed-update"), {
        type: "error",
      });
    };
    setIsUpdatingCart(true);
    const { data } = await axiosInstance.put("cart/update", {
      classesId,
      plans,
      _id: id,
      clubId,
      memberships,
    });
    if (data.success && data.data) {
      showSnackbar(translate("cart.update-success"), {
        type: "success",
      });
      onUpdateSuccess(data.data);
    } else {
      showErrorMessage();
    }
    setIsUpdatingCart(false);
    try {
    } catch (error) {
      showErrorMessage();
    }
  };

  const handleAddManyToCart = async ({
    payload,
  }: {
    payload: handleAddToCartProps[];
  }) => {
    const showErrorMessage = () => {
      setIsAddingManyToCart(false);
      showSnackbar(translate("cart.failed-update"), {
        type: "error",
      });
    };
    try {
      setIsAddingManyToCart(true);
      const { data: createdData } = await axiosInstance.post(
        "cart/create-many",
        payload
      );
      if (createdData.success) {
        onAddingSuccess();
        showSnackbar(translate("cart.add-success"), {
          type: "success",
        });
      } else {
        showErrorMessage();
      }
      setIsAddingManyToCart(false);
    } catch (error) {
      setIsAddingManyToCart(false);
      showErrorMessage();
    }
  };

  return {
    isAddingToCart,
    isDeletingCart,
    handleAddToCart,
    handleDelete,
    isUpdatingCart,
    handleUpdateCart,
    handleAddManyToCart,
    isAddingManyToCart,
  };
};

export default useCart;
