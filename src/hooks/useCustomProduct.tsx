import axiosInstance from "@/utils/interceptor";
import { useRouter } from "next/router";
import { useState } from "react";
import { useSnackbar } from "./useSnackbar";
import { PAYMENT_STATUS } from "@/constant/Enums";

const BASE_URL = "/create/custom-product";

const useCustomProduct = ({ data }) => {
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const [isEditing, setIsEditing] = useState(false);
  const [isViewing, setIsViewing] = useState(false);
  const [isArchieving, setIsArchieving] = useState(false);
  const [isMarkingAsPaid, setIsMarkingAsPaid] = useState(false);

  const handleMarkAsPaid = async ({ handleSuccess = () => {} }) => {
    if (data?.transactionId?.status === PAYMENT_STATUS.SUCCESS) {
      showSnackbar("Product is already paid", {
        type: "warning",
      });
      return;
    }

    const handleError = () => {
      showSnackbar("something went wrong while marking the product as paid", {
        type: "error",
      });
      setIsMarkingAsPaid(false);
    };

    try {
      setIsMarkingAsPaid(true);
      const { data: respData } = await axiosInstance.post(
        `custom-product/transfer`,
        {
          productId: data._id,
          code: data.code,
          isPayByCash: true,
        }
      );
      if (respData.success) {
        handleSuccess && handleSuccess();
        showSnackbar("Marked product as paid successfully", {
          type: "success",
        });
      } else {
        handleError();
      }
    } catch (error) {
      handleError();
      console.error("Something went wrong in handleMarkAsPaid", error);
    }
  };

  const handleEdit = () => {
    try {
      setIsEditing(true);
      router.push(`${BASE_URL}/edit/${data._id}`);
      setIsEditing(false);
    } catch (error) {
      setIsEditing(false);
      console.error("Something went wrong in handleEdit", error);
    }
  };

  const handleView = () => {
    try {
      setIsViewing(true);
      router.push(`${BASE_URL}/view/${data._id}`);
      setIsViewing(false);
    } catch (error) {
      setIsViewing(false);
      console.error("Something went wrong in handleView", error);
    }
  };

  const handleArchieve = async ({ onSuccess = () => {} }) => {
    const handleError = () => {
      showSnackbar("something went wrong while archiving the custom product", {
        type: "error",
      });
      setIsArchieving(false);
    };

    try {
      setIsArchieving(true);
      const { data: respData } = await axiosInstance.post(
        `custom-product/archieve`,
        {
          id: data._id,
        }
      );
      if (respData.success) {
        onSuccess && onSuccess();
        showSnackbar("Archieved the custom product successfully", {
          type: "success",
        });
      } else {
        handleError();
      }
      setIsArchieving(false);
    } catch (error) {
      handleError();
      console.error("Something went wrong in handleArchieve", error);
    }
  };

  const handleShare = async () => {
    try {
      const sharedUrl = `${process.env.NEXT_PUBLIC_BASE_URL}classes/custom-products/detail/${data._id}`;
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(sharedUrl);
        console.log("Link copied to clipboard!");
        showSnackbar("Link copied to clipboard!", {
          type: "success",
        });
      }
    } catch (error) {
      console.error("Something went wrong in handleShare", error);
    }
  };

  return {
    isEditing,
    isViewing,
    isArchieving,
    isMarkingAsPaid,
    handleEdit,
    handleView,
    handleShare,
    handleArchieve,
    handleMarkAsPaid,
  };
};

export default useCustomProduct;
