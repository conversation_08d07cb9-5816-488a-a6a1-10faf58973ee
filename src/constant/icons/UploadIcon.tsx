import React from "react";

const UploadIcon = ({ height = 14, width = 15, ...props }) => {
  return (
    <svg
      {...props}
      width={width}
      height={height}
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 10.577V1.927L4.67 4.257L3.962 3.539L7.5 0L11.039 3.539L10.331 4.258L8 1.927V10.577H7ZM2.116 14C1.65533 14 1.271 13.846 0.963 13.538C0.655 13.23 0.500667 12.8453 0.5 12.384V9.961H1.5V12.384C1.5 12.538 1.564 12.6793 1.692 12.808C1.82 12.9367 1.961 13.0007 2.115 13H12.885C13.0383 13 13.1793 12.936 13.308 12.808C13.4367 12.68 13.5007 12.5387 13.5 12.384V9.961H14.5V12.384C14.5 12.8447 14.346 13.229 14.038 13.537C13.73 13.845 13.3453 13.9993 12.884 14H2.116Z"
        fill="black"
      />
    </svg>
  );
};

export default UploadIcon;
