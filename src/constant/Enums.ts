enum INTEREST_TYPES {
  HATE = "HATE",
  NEUTRAL = "NEUTRAL",
  LOVE = "LOVE",
}

enum SEEDING_ENUMS {
  COUNTRIES = "countries",
  GOALS = "goals",
  INTERESTS = "interests",
  LANGUAGES = "languages",
  PROFICIENCIES = "proficiency",
  CATEGORIES = "category",
  CLASSES = "classes",
  ALL = "all",
}

enum VIDEO_STATUS {
  NONE = "none",
  READY = "ready",
}

enum ENVIRONMENT {
  SANDBOX = "sandbox",
  PUBLISHED = "published",
}

enum PROGRESS_STATUS {
  COMPLETE = 2,
  NOT_STARTED = 0,
  IN_PROGRESS = 1,
}

enum ASSETS_TYPES {
  VIDEO = "VIDEO",
  THUMBNAIL = "THUMBNAIL",
  CC = "CC",
}

enum ASSETS_STATUS {
  STARTED = "STARTED",
  FAILED = "FAILED",
  READY = "READY",
}

enum LEVEL_TYPES {
  BEGINNER = "BEGINNER",
  ADVANCED = "ADVANCED",
  INTERMEDIATE = "INTERMEDIATE",
}

const LEVELS = [
  {
    id: 1,
    name: "Beginner",
    value: LEVEL_TYPES.BEGINNER,
    color: "#B1F3B1",
  },
  {
    id: 2,
    name: "Intermediate",
    value: LEVEL_TYPES.INTERMEDIATE,
    color: "#FED07C",
  },
  {
    id: 3,
    name: "Advanced",
    value: LEVEL_TYPES.ADVANCED,
    color: "#FF8C61",
  },
];

enum EVENT_MODE_TYPE {
  ONLINE = "ONLINE",
  OFFLINE = "OFFLINE",
}

enum CLASSES_TYPE {
  IN_PERSON = "IN_PERSON",
  COMMUNITY = "COMMUNITY",
  ONLINE = "ONLINE",
}

enum IN_PERSON_TYPE {
  GROUP = "GROUP",
  PRIVATE = "PRIVATE",
}

enum ONLINE_CLASSES_TYPE {
  QUICK_SESSIONS = "QUICK_SESSIONS",
  REGULAR_SESSIONS = "REGULAR_SESSIONS",
  LONG_SESSIONS = "LONG_SESSIONS",
}

enum DURATION_TYPE {
  WEEK = "WEEK",
  HOUR = "HOUR",
  MINUTES = "MINUTES",
  THIRTY_MINUTES = "THIRTY_MINUTES",
  NINETY_MINUTES = "NINETY_MINUTES",
}

enum PLAN_FOR {
  MYSELF = "MYSELF",
  SOMEONE = "SOMEONE",
}

enum PAYMENT_STATUS {
  SUCCESS = "SUCCESS",
  FAILED = "FAILED",
  INITIATED = "INITIATED",
  EXPIRED = "EXPIRED",
  CANCELLED = "CANCELLED",
}

enum CLASSES_SORT {
  ONLINE_CLUB = "ONLINE_CLUB",
  COMMUNITY = "COMMUNITY",
  IN_PERSON_CLASS = "IN_PERSON_CLASS",
  ONLINE_CLASS = "ONLINE_CLASS",
}

enum ROLE_TYPES {
  ADMIN = "ADMIN",
  TEAHCER = "TEACHER",
  STUDENT = "STUDENT",
  CREATOR = "CREATOR",
}

enum CLASSESS_FETCH_DURATION {
  PAST = 1,
  CURRENT = 2,
  UPCOMING = 3,
  ALL = 4,
}

enum PAYMENT_MODE {
  PAYMENT = "payment",
  SUBSCRIPTION = "subscription",
  BANK_TRANSFER = "BANK_TRANSFER",
  CASH = "CASH",
}

enum SORT {
  ASCENDING = "asc",
  DESCENDING = "desc",
}

enum WeekDay {
  MONDAY = 1,
  TUESDAY = 2,
  WEDNESDAY = 3,
  THURSDAY = 4,
  FRIDAY = 5,
  SATURDAY = 6,
  SUNDAY = 0,
}

enum CURRENCY_ENUM {
  USD = "USD",
  MXN = "MXN",
}

enum TRANSACTION_TYPE_ENUM {
  ONLINE = "ONLINE",
  OFFLINE = "OFFLINE",
}

const IN_PERSON_CLASSES_TABS = [
  {
    id: 1,
    name: "ipc.group",
    description: "ipc.private-info",
    value: IN_PERSON_TYPE.GROUP,
  },
  {
    id: 2,
    name: "ipc.private",
    description: "ipc.group-info",
    value: IN_PERSON_TYPE.PRIVATE,
  },
];

const ONLINE_CLASSES_TABS = [
  {
    id: 1,
    name: "oc.30mins",
    description: "oc.30mins-desc",
    value: ONLINE_CLASSES_TYPE.QUICK_SESSIONS,
  },
  {
    id: 2,
    name: "oc.1hour",
    description: "oc.1hour-desc",
    value: ONLINE_CLASSES_TYPE.REGULAR_SESSIONS,
  },
  {
    id: 3,
    name: "oc.90mins",
    value: ONLINE_CLASSES_TYPE.LONG_SESSIONS,
    description: "oc.90mins-desc",
  },
];

const LEARNING_LANGUAGE = {
  SPANISH: "spanish",
  ENGLISH: "english",
};

export {
  INTEREST_TYPES,
  SEEDING_ENUMS,
  VIDEO_STATUS,
  ENVIRONMENT,
  PROGRESS_STATUS,
  ASSETS_TYPES,
  ASSETS_STATUS,
  LEVEL_TYPES,
  EVENT_MODE_TYPE,
  CLASSES_TYPE,
  IN_PERSON_TYPE,
  ONLINE_CLASSES_TYPE,
  DURATION_TYPE,
  PLAN_FOR,
  PAYMENT_STATUS,
  CLASSES_SORT,
  ROLE_TYPES,
  CLASSESS_FETCH_DURATION,
  LEVELS,
  PAYMENT_MODE,
  SORT,
  WeekDay,
  CURRENCY_ENUM,
  IN_PERSON_CLASSES_TABS,
  ONLINE_CLASSES_TABS,
  TRANSACTION_TYPE_ENUM,
  LEARNING_LANGUAGE,
};
