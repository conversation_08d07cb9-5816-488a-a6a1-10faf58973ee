import { handleTranslate } from "@/utils/common";
import { COLOR, getEmailSkeleton } from "./utils";
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

type getCourseGiftBodyProps = {
  userName: string;
  senderName: string;
  description: string;
  isEnglish: boolean;
};
const getCourseGiftBody = ({
  userName,
  senderName,
  description,
  isEnglish,
}: getCourseGiftBodyProps) => {
  const body = `
    <table 
        align="center" 
        width="100%" 
        border="0" 
        cellPadding="0" 
        cellSpacing="0" 
        role="presentation" 
        style="padding:1rem"
      >
        <tbody>
            <tr>
                <td>
                    <h1 style="color:rgb(0,0,0);font-size:24px;font-weight:400;text-align:center;padding:0px;margin-top:30px;margin-bottom:30px;margin-left:0px;margin-right:0px">
                        <strong>${handleTranslate(
                          "email.ce.dear",
                          isEnglish
                        )} <!-- -->${
    userName ?? `${handleTranslate("email.ce.user", isEnglish)}`
  }<!-- --> ,</strong>
                    </h1>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding:1rem">
                        <tbody>
                            <tr>
                                <td>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0">
${handleTranslate(
  "email.ge.surprise-1",
  isEnglish
)} ${senderName} ${handleTranslate("email.ge.surprise-2", isEnglish)}
                                    </p>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0">

                                        ${description}. ${handleTranslate(
    "email.delight",
    isEnglish
  )} 

                                    </p>
                                    <hr 
                                        style="margin-top:16px;margin-bottom:16px;border-top-width:2px;border-color:rgb(209,213,219);width:100%;border:none;border-top:1px solid #eaeaea" 
                                    />
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0"
                                    >
${handleTranslate(
  "email.next-step.1",
  isEnglish
)}<!-- --> <a href="${BASE_URL}/sign-up" style="color:${COLOR};text-decoration:none" target="_blank"> ${handleTranslate(
    "email.next-step.2",
    isEnglish
  )} </a> ${handleTranslate("email.next-step.3", isEnglish)}
                                    </p>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0"
                                    >
                                        ${handleTranslate(
                                          "email.excited",
                                          isEnglish
                                        )}
                                    
                                    </p>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0"
                                    >
                                        ${handleTranslate(
                                          "email.warm-regards",
                                          isEnglish
                                        )}
                                    </p>
                                    <p 
                                        style="color:rgb(0,0,0);font-size:14px;font-weight:400;line-height:24px;margin:16px 0"
                                    >
${handleTranslate("email.team-pf", isEnglish)}
                                    </p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
  `;

  return getEmailSkeleton({ body, isEnglish });
};

export { getCourseGiftBody };
