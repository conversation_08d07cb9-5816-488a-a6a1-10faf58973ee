import { handleTranslate } from "@/utils/common";
import { getEmailSkeleton } from "./utils";

export const getWelcomeWaitlistEmailBody = ({
  welcomeMessage = "Thank you for joining the waitlist for <PERSON><PERSON>! We're excited to have you on board as we get ready to go live soon. You'll be among the first to experience our service, and we can't wait to share it with you! Stay tuned for more updates!",
  assistanceMessage = "Need assistance or have questions? Contact us and our support team will provide you with the help you need.",
  tallyMessage = "In the mean time, please fill our intake inform:",
}) => {
  const body = `
  <h1 
    style="color:rgb(0,0,0);font-size:24px;font-weight:400;text-align:center;padding:0px;margin-top:30px;margin-bottom:30px;margin-left:0px;margin-right:0px"
  >
    <strong>
      You're On the List 🎉
    </strong>
  </h1>
  <table 
    align="center" 
    width="100%" 
    border="0" 
    cellPadding="0" 
    cellSpacing="0" 
    role="presentation" 
    style="padding:1rem"
  >
    <tbody>
      <tr>
        <td>
          <p 
            style="color:rgb(0,0,0);font-size:18px;font-weight:400;line-height:24px;margin:16px 0; padding: 30px;"
          >
            ${welcomeMessage}
          </p>
          <p 
            style="color:rgb(0,0,0);font-size:18px;font-weight:400;line-height:24px;margin:16px 0; padding: 0px 30px;"
          >
            ${tallyMessage}
          </p>
          <div 
            style="margin:16px 0; padding: 0px 30px;"
          >
            <a 
              href="https://tally.so/r/mBBRNA"
            >
              <button 
                style="background-color: #22B8B5; color: white; border: none; padding: 10px 20px; font-size: 16px; border-radius: 5px; cursor: pointer;"
              >
                User Intake Form
              </button>
            </a>
          </div>
          <p 
            style="color:rgb(0,0,0);font-size:14px;line-height:17px;margin:16px 0; padding: 20px; text-align: center;"
          >
            ${assistanceMessage}
          </p>
        </td>
      </tr>
    </tbody>
  </table>
  `;

  return getEmailSkeleton({
    body,
    name: "",
    isEnglish: true,
    footerMessage: handleTranslate("email.footer.waitlist", true),
  });
};
