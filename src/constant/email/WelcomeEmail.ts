import { handleTranslate } from "@/utils/common";
import { getEmailSkeleton } from "./utils";

export const getWelcomeEmailBody = ({ name, isEnglish = true }) => {
  const body = `
    <table 
      align="center" 
      width="100%" 
      border="0" 
      cellPadding="0" 
      cellSpacing="0" 
      role="presentation" 
      style="padding:1rem"
    >
      <tbody>
        <tr>
          <td>
            <p 
              style="color:rgb(0,0,0);font-size:18px;font-weight:400;line-height:24px;margin:16px 0"
            >
              ${handleTranslate("email.we.welcome-info", isEnglish)}
            </p>
            <p 
              style="color:rgb(0,0,0);font-size:14px;line-height:17px;margin:16px 0">
              ${handleTranslate("email.we.need-assistance", isEnglish)}
            </p>
          </td>
        </tr>
      </tbody>
    </table>
  `;

  return getEmailSkeleton({ body, name, isEnglish });
};
