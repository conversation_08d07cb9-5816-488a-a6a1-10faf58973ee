import { handleTranslate } from "@/utils/common";
import { getEmailSkeleton } from "./utils";

export const getWelcomeSubscriptionEmailBody = ({ isEnglish = true }) => {
  const body = `
    <h1 
      style="color:rgb(0,0,0);font-size:24px;font-weight:400;text-align:center;padding:0px;margin-top:30px;margin-bottom:30px;margin-left:0px;margin-right:0px"
      >
        <strong>
          ${handleTranslate("email.subscribed", isEnglish)}
        </strong>
    </h1>
    <table 
      align="center" 
      width="100%" 
      border="0" 
      cellPadding="0" 
      cellSpacing="0" 
      role="presentation" 
      style="padding:1rem"
    >
      <tbody>
        <tr>
          <td>
            <p 
              style="color:rgb(0,0,0);font-size:18px;font-weight:400;line-height:24px;margin:16px 0; padding: 30px;"
            >${handleTranslate("email.nl.1", isEnglish)}</p>
            <p 
              style="color:rgb(0,0,0);font-size:18px;font-weight:400;line-height:24px;margin:16px 0; padding: 0px 30px;"
            >${handleTranslate("email.nl.fill", isEnglish)}</p>
            <div 
              style="margin:16px 0; padding: 0px 30px;"
            >
              <a 
                href="https://tally.so/r/mBBRNA" 
              >
                <button 
                  style="background-color: #22B8B5; color: white; border: none; padding: 10px 20px; font-size: 16px; border-radius: 5px; cursor: pointer;"
                >
                  ${handleTranslate("email.intake-form", isEnglish)}
                </button>
              </a>
            </div>
            <p 
              style="color:rgb(0,0,0);font-size:14px;line-height:17px;margin:16px 0; padding: 20px; text-align: center;">
              ${handleTranslate("email.nl.2", isEnglish)}
            </p>
          </td>
        </tr>
      </tbody>
    </table>
  `;
  return getEmailSkeleton({ body, name: "", isEnglish });
};
