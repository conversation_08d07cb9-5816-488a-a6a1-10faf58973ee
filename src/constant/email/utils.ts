import { handleTranslate } from "@/utils/common";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;
const MAIL_BASE_URL = `${BASE_URL}images/mail`;

export const COLOR = "#14A79C";

const REPORT_LINK = process.env.NEXT_PUBLIC_REPORT_LINK_URL;
const YOUTUBE_LINK = process.env.NEXT_PUBLIC_YOUTUBE_URL;
const INSTAGRAM_LINK = process.env.NEXT_PUBLIC_INSTA_URL;
const X_LINK = process.env.NEXT_PUBLIC_X_URL;
const FACEBOOK_LINK = process.env.NEXT_PUBLIC_FB_URL;
const WHATSAPP_LINK = `https://wa.me/${process.env.NEXT_PUBLIC_WHATSAPP_PHONE}`;

const EMAIL_SOCIALS = `
    <table 
        align="center" 
        width="100%" 
        border="0" 
        cellPadding="0" 
        cellSpacing="0" 
        role="presentation" 
        style="width:50%"
    >
        <tbody>
            <tr>
                <td>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation">
                        <tbody style="width:100%">
                            <tr style="width:100%">
                                <!-- Instagram Link -->
                                ${
                                  INSTAGRAM_LINK
                                    ? `<td align="center" data-id="__react-email-column">
                                          <a href="${INSTAGRAM_LINK}" style="color:${COLOR};text-decoration:none" target="_blank">
                                              <img src="${MAIL_BASE_URL}/insta.png" style="display:block;outline:none;border:none;text-decoration:none" />
                                          </a>
                                       </td>`
                                    : ""
                                }
                                
                                <!-- X (Twitter) Link -->
                                ${
                                  X_LINK
                                    ? `<td align="center" data-id="__react-email-column">
                                          <a href="${X_LINK}" style="color:${COLOR};text-decoration:none" target="_blank">
                                              <img src="${MAIL_BASE_URL}/X.png" style="display:block;outline:none;border:none;text-decoration:none" />
                                          </a>
                                       </td>`
                                    : ""
                                }
                                
                                <!-- WhatsApp Link -->
                                ${
                                  WHATSAPP_LINK
                                    ? `<td align="center" data-id="__react-email-column">
                                          <a href="${WHATSAPP_LINK}" style="color:${COLOR};text-decoration:none" target="_blank">
                                              <img src="${MAIL_BASE_URL}/WhatsApp.png" style="display:block;outline:none;border:none;text-decoration:none" />
                                          </a>
                                       </td>`
                                    : ""
                                }
                                
                                <!-- Facebook Link -->
                                ${
                                  FACEBOOK_LINK
                                    ? `<td align="center" data-id="__react-email-column">
                                          <a href="${FACEBOOK_LINK}" style="color:${COLOR};text-decoration:none" target="_blank">
                                              <img src="${MAIL_BASE_URL}/facebook.png" style="display:block;outline:none;border:none;text-decoration:none" />
                                          </a>
                                       </td>`
                                    : ""
                                }
                                
                                <!-- YouTube Link -->
                                ${
                                  YOUTUBE_LINK
                                    ? `<td align="center" data-id="__react-email-column">
                                          <a href="${YOUTUBE_LINK}" style="color:${COLOR};text-decoration:none" target="_blank">
                                              <img src="${MAIL_BASE_URL}/YouTube.png" style="display:block;outline:none;border:none;text-decoration:none" />
                                          </a>
                                       </td>`
                                    : ""
                                }
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
`;

const getEmailFooter = ({ footerMessage = "", isEnglish = false }) => {
  return `
    <p 
        style="color:rgb(0,0,0);text-align:center;font-size:14px;font-weight:500;line-height:18px;margin:16px 0"
    >
        ${handleTranslate("common.connect", isEnglish)}
    </p>
        ${EMAIL_SOCIALS}

        <table 
            align="center" 
            width="100%" 
            border="0" 
            cellPadding="0" 
            cellSpacing="0" 
            role="presentation" 
            style="width:95%"
        >
            <tbody>
                <tr>
                    <td>
                        <p 
                            style="color:rgb(102,102,102);font-size:12px;line-height:16px;cursor:pointer;padding:1rem;margin:16px 0"
                        >
${footerMessage ?? ""}.${
    REPORT_LINK
      ? ` ${handleTranslate(
          "email.ce.report-1",
          isEnglish
        )} <a href="${REPORT_LINK}" style="color:${COLOR};text-decoration-line:none;text-decoration:none" target="_blank">${handleTranslate(
          "email.ce.report-2",
          isEnglish
        )}.</a>`
      : ""
  }
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
    `;
};

export const EMAIL_HEADER = `
    <table 
        align="center" 
        width="100%" 
        border="0" 
        cellPadding="0" 
        cellSpacing="0" 
        role="presentation"
    >
        <tbody>
            <tr>
                <td>
                    <img alt="Vercel" width="100%" src="${MAIL_BASE_URL}/image.png" style="margin-top:0px;margin-bottom:0px;margin-left:auto;margin-right:auto;display:block;outline:none;border:none;text-decoration:none" width="100%" />
                </td>
            </tr>
        </tbody>
    </table>
    `;

type getEmailSkeletonProps = {
  body: string;
  name?: string;
  footerMessage?: string;
  isEnglish: boolean;
};
export const getEmailSkeleton = ({
  body,
  name = "",
  footerMessage = "",
  isEnglish = false,
}: getEmailSkeletonProps) => {
  return `
     <!DOCTYPE html>
<html dir="ltr" lang="en">
  <head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" />
  </head>
  <body style="background-color:rgb(255,255,255);margin-top:auto;margin-bottom:auto;margin-left:auto;margin-right:auto;font-family:ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;">
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="border-width:1px;border-style:solid;border-color:rgb(234,234,234);border-radius:0.25rem;margin-top:40px;margin-bottom:40px;margin-left:auto;margin-right:auto;max-width:465px">
      <tbody>
        <tr style="width:100%">
          <td>

            ${EMAIL_HEADER}

${
  name
    ? `
<h1 style="color:rgb(0,0,0);font-size:24px;font-weight:400;text-align:center;padding:0px;margin-top:30px;margin-bottom:30px;margin-left:0px;margin-right:0px"><strong>${handleTranslate(
        "email.ce.hi",
        isEnglish
      )} <!-- -->${name}<!-- --> ,</strong></h1>
`
    : ""
}

            ${body}

            ${getEmailFooter({ footerMessage, isEnglish })}
           </td>
        </tr>
      </tbody>
    </table>
  </body>
</html>
    `;
};
