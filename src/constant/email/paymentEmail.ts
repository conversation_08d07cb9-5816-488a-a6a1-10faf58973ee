import { getFinalAmount, getPriceSymbol } from "@/utils/classes";
import { COLOR, getEmailSkeleton } from "./utils";
import { CURRENCY_ENUM } from "../Enums";
import { handleTranslate } from "@/utils/common";

const contact = process.env.NEXT_PUBLIC_WHATSAPP_PHONE;
const emailId = process.env.SENDER_EMAIL;
const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
const signinUrl = `${baseUrl}/sign-in`;

const phoneWithPlus = `+${contact}`;

const getEmailSuccessFooter = ({ isEnglish = false }) => {
  const SUCCESS_FOOTER = `
    <p
                      style="font-size:0.79rem;line-height:24px;margin-bottom:10px;margin-top:16px;color:#000;text-align:left;font-weight:600">
                      ${handleTranslate("email.pe.next-step", isEnglish)}
                    </p>
                    <ol style="font-size:0.75rem">
                      <li>
                        <p
                          class="m-0 mt-0 mb-0 leading-[24px] text-gray-500"
                          style="font-size:0.75rem;line-height:24px;margin-bottom:16px;margin-top:16px;margin:0px">
                          ${handleTranslate("email.pe.access-start", isEnglish)}
                          <a
                            href="${signinUrl}"
                            style="color:${COLOR};text-decoration-line:none"
                            target="_blank"
                            >${handleTranslate(
                              "email.pe.download-2",
                              isEnglish
                            )}.</a
                          > ${handleTranslate("email.pe.access-end", isEnglish)}
                        </p>
                      </li>
                      <li>
                        <p
                          class="m-0 mt-0 mb-0  leading-[24px] text-gray-500"
                          style="font-size:0.75rem;line-height:24px;margin-bottom:16px;margin-top:16px;margin:0px">
                            ${handleTranslate(
                              "email.pe.assistance-1",
                              isEnglish
                            )} <a
                            href="mailto:${emailId}"
                            style="color:${COLOR};text-decoration-line:none"
                            target="_blank"
                            >${emailId}</a
                          > ${handleTranslate(
                            "email.pe.assistance-2",
                            isEnglish
                          )} <a
                            href="tel:${phoneWithPlus}"
                            style="color:${COLOR};text-decoration-line:none"
                            target="_blank"
                            > ${phoneWithPlus}</a
                          >
                        </p>
                      </li>
                    </ol>

`;

  return SUCCESS_FOOTER;
};

const getEmailErrorFooter = ({ isEnglish = false }) => {
  return `
    <p
              class="m-0 mt-0 mb-0 leading-[24px] text-gray-500"
              style="font-size:0.75rem;line-height:24px;margin-bottom:16px;margin-top:16px;margin:0px">
              ${handleTranslate("email.pe.assistance-1", isEnglish)}
             <a
                href="mailto:${emailId}"
                style="color:${COLOR};text-decoration-line:none"
                target="_blank">
                ${emailId}
                </a> ${handleTranslate("email.pe.assistance-2", isEnglish)} <a
                href="tel:${phoneWithPlus}"
                style="color:${COLOR};text-decoration-line:none"
                target="_blank"
              > ${phoneWithPlus}</a>
            </p>
`;
};

type PaymentDetailsType = {
  amount: number;
  title: string;
  type: string;
  currency: CURRENCY_ENUM;
};

type getPaymentEmailBodyProps = {
  isError: boolean;
  isSubscription: boolean;
  isEnglish: boolean;
  name: string;
  receiptUrl: string;
  transactionId: string;
  paymentDetails: PaymentDetailsType[];
  totalAmount: number;
};
const getPaymentEmailBody = ({
  isError,
  name,
  receiptUrl,
  transactionId,
  paymentDetails,
  totalAmount,
  isSubscription,
  isEnglish,
}: getPaymentEmailBodyProps) => {
  const info = (() => {
    const id = (() => {
      if (isSubscription) {
        return isError ? "email.pe.failed-info" : "email.pe.subscribing";
      } else {
        return isError ? "email.pe.regret" : "email.pe.choosing";
      }
    })();
    return handleTranslate(id, isEnglish);
  })();

  const body = `
   <table 
      align="center" 
      width="100%" 
      border="0" 
      cellPadding="0" 
      cellSpacing="0" 
      role="presentation" 
      style="padding:1rem"
    >
    <tbody>
        <tr style="width:100%">
          <td>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="padding:0 48px">
              <tbody>
                <tr>
                  <td>
                    <p
                      style="font-size:16px;line-height:24px;margin-bottom:16px;margin-top:16px;color:#000;text-align:center;font-weight:700">
                      ${handleTranslate("email.ce.hi", isEnglish)} ${name}
                    </p>
                    <p
                      style="font-size:16px;line-height:24px;margin-bottom:16px;margin-top:16px;color:#000;text-align:left">
                        ${info}
                    </p>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation"
                      style="border:1px solid rgba(238, 238, 238, 1);border-radius:10px;padding:10px">
                      <tbody>
                        <tr>
                          <td>
                            <p
                              style="font-size:0.9rem;line-height:24px;margin-bottom:16px;margin-top:0;color:#000;text-align:left;font-weight:600">
                              ${handleTranslate(
                                isSubscription
                                  ? "email.pe.sub-details"
                                  : "email.pe.services-details",
                                isEnglish
                              )}
                            </p>
   
                            ${paymentDetails
                              .map((m) =>
                                getPaymentRow({
                                  classType: m.type,
                                  classTitle: m.title,
                                  amount: m.amount,
                                  currency: m.currency,
                                })
                              )
                              .join("")}

                            <table
                              align="center"
                              width="100%"
                              border="0"
                              cellpadding="0"
                              cellspacing="0"
                              role="presentation"
                              style="border-top:1px solid rgba(238, 238, 238, 1);padding-top:10px">
                              <tbody style="width:100%">
                                <tr style="width:100%">
                                  <td data-id="__react-email-column">
                                    <p
                                      style="font-size:0.8rem;line-height:24px;margin-bottom:0;margin-top:0;color:#000;text-align:left;font-weight:600">
                                      ${handleTranslate(
                                        "cart.total-amt",
                                        isEnglish
                                      )}
                                    </p>
                                  </td>
                                  <td data-id="__react-email-column">
                                    <p
                                      style="font-size:0.8rem;line-height:24px;margin-bottom:0;margin-top:0;color:#000;text-align:end;font-weight:600">
                                      ${getPriceSymbol({
                                        currency: paymentDetails[0].currency,
                                      })}${getFinalAmount(totalAmount)}
                                    </p>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <p
                      style="font-size:0.7rem;line-height:24px;margin-bottom:16px;margin-top:16px;color:rgba(109, 109, 109, 1);text-align:left">
                      <b style="color:#000">${handleTranslate(
                        "email.pe.transaction-id",
                        isEnglish
                      )}</b>  ${transactionId}
                    </p>
${
  isError || !receiptUrl
    ? ""
    : `
<p
                      style="font-size:0.7rem;line-height:24px;margin-bottom:16px;margin-top:16px;color:#000;text-align:left">
                    ${handleTranslate("email.pe.download-1", isEnglish)}<!-- -->
                      <a
                        href="${receiptUrl}"
                        style="color:${COLOR};text-decoration-line:none"
                        target="_blank"
                    >${handleTranslate("email.pe.download-2", isEnglish)}.</a
                      >
                    </p>

`
}


                    ${
                      isError
                        ? getEmailErrorFooter({ isEnglish })
                        : getEmailSuccessFooter({ isEnglish })
                    }
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
  `;
  return getEmailSkeleton({ name: "", body, isEnglish });
};

const getPaymentRow = ({ classType, classTitle, amount, currency }) => {
  return `
     <table
        align="center"
        width="100%"
        border="0"
        cellpadding="0"
        cellspacing="0"
        role="presentation"
        style="margin-bottom:10px">
        <tbody>
          <tr>
            <td>
              <table
                align="center"
                width="100%"
                border="0"
                cellpadding="0"
                cellspacing="0"
                role="presentation">
                <tbody style="width:100%">
                  <tr style="width:100%">
                    <td data-id="__react-email-column">
                      <p
                        style="font-size:0.78rem;line-height:24px;margin-bottom:0;margin-top:0;color:#000;text-align:left;font-weight:500">
                        ${classType}
                      </p>
                    </td>
                    <td data-id="__react-email-column">
                      <p
                        style="font-size:0.78rem;line-height:24px;margin-bottom:0;margin-top:0;color:#000;text-align:end;font-weight:600">
                         ${getPriceSymbol({ currency })}${amount}
                      </p>
                    </td>
                  </tr>
                </tbody>
              </table>
              <table
                align="center"
                width="100%"
                border="0"
                cellpadding="0"
                cellspacing="0"
                role="presentation">
                <tbody style="width:100%">
                  <tr style="width:100%">
                    <p
                      style="font-size:0.72rem;line-height:24px;margin-bottom:0;margin-top:0;color:rgba(163, 163, 163, 1);text-align:left;font-weight:600">
                        ${classTitle}
                    </p>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
    `;
};

export { getPaymentEmailBody };
