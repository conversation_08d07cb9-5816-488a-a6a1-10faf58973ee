enum UpcomingFilterTypes {
  NEXT_7 = 1,
  NEXT_14 = 2,
  WHOLE_MONTH = 3,
  ALL = 4,
}

enum StudentFetchClassesType {
  UPCOMING = 1,
  PAST = 2,
}

const UpcomingFilter = [
  {
    id: UpcomingFilterTypes.NEXT_7,
    name: "Next 7 days",
  },
  {
    id: UpcomingFilterTypes.NEXT_14,
    name: "Next 14 days",
  },
  {
    id: UpcomingFilterTypes.WHOLE_MONTH,
    name: "Whole month",
  },
  {
    id: UpcomingFilterTypes.ALL,
    name: "All",
  },
];

enum PreviousFilterTypes {
  PREV_7 = 1,
  PREV_14 = 2,
  WHOLE_MONTH = 3,
  ALL = 4,
}

const PreviousFilter = [
  {
    id: PreviousFilterTypes.PREV_7,
    name: "Previous 7 days",
  },
  {
    id: PreviousFilterTypes.PREV_14,
    name: "Previous 14 days",
  },
  {
    id: PreviousFilterTypes.WHOLE_MONTH,
    name: "Whole month",
  },
  {
    id: PreviousFilterTypes.ALL,
    name: "All",
  },
];

export {
  StudentFetchClassesType,
  PreviousFilter,
  PreviousFilterTypes,
  UpcomingFilter,
  UpcomingFilterTypes,
};
