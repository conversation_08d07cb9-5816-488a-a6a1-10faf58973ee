enum DASHBOARD_SUBTITLES {
  ENGLISH = "en",
  SPANISH = "es",
}

enum DASHBOARD_CONTENT_TYPE {
  VIDEOS = "video",
  COLLECTION = "collection",
}

const dashboardSubtitlesOptions = [
  { label: "Spanish", value: DASHBOARD_SUBTITLES.SPANISH },
  { label: "English", value: DASHBOARD_SUBTITLES.ENGLISH },
];

const dashboardContentTypeOptions = [
  { label: "Videos", value: DASHBOARD_CONTENT_TYPE.VIDEOS },
  { label: "Collections", value: DASHBOARD_CONTENT_TYPE.COLLECTION },
];

const dashboardContentAllOptions = [
  { label: "All", value: "All" },
  ...dashboardContentTypeOptions,
];

enum DASHBOARD_SORT_BY {
  RELEVANT = "relevant",
  LIKED = "liked",
  DATE = "date",
}

const dashboardSortFilters = [
  { label: "Most Relevant", value: DASHBOARD_SORT_BY.RELEVANT },
  { label: "Most Liked", value: DASHBOARD_SORT_BY.LIKED },
  { label: "Newest", value: DASHBOARD_SORT_BY.DATE },
];

export {
  DASHBOARD_SUBTITLES,
  DASHBOARD_CONTENT_TYPE,
  DASHBOARD_SORT_BY,
  dashboardSubtitlesOptions,
  dashboardContentTypeOptions,
  dashboardSortFilters,
  dashboardContentAllOptions,
};
