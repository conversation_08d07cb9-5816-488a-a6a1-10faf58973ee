import img from "@/../../public/images/home/<USER>";
export const InPersonClassesPricing = {
  title: "Pricing For",
  typeOfClass: "In Person Classes",
  description: "Group classes run Monday to Thursday, 10:00 AM - 1:00 PM",
  type: [
    {
      name: "group",
      description: "Group classes run Monday to Thursday, 10:00 AM - 1:00 PM",
      plans: [
        {
          id: 1,
          planName: "1 Week Plan",
          price: 120,
          costPerPeriod: 120,
          costPeriod: "Week",
          descriptionTitle: "Customization Options",
          customizationOptions: [
            {
              id: 1,
              value: 1,
              timePeriod: "Week",
              cost: 120,
              savings: "",
            },
            { id: 2, value: 4, timePeriod: "Weeks", cost: 120, savings: "" },
          ],
          savings: "",
        },
        {
          id: 2,
          planName: "4 Week Plan",
          price: 410,
          costPerPeriod: 102,
          costPeriod: "Week",
          descriptionTitle: "Plan Details",
          customizationOptions: [
            {
              value: 4,
              timePeriod: "Weeks",
              cost: 410,
              savings: "Save 78.65% = $377.5 ",
            },
          ],
          savings: "Save 78.65 %",
        },
      ],
    },
    {
      name: "Private",
      description:
        "Customized one-on-one in-person sessions available on demand",
      plans: [
        {
          planName: "Single Hour Session",
          price: 16,
          costPerPeriod: 16,
          costPeriod: "Hour",
          descriptionTitle: "Customizations Available",
          customizationOptions: [
            {
              value: 1,
              timePeriod: "Session",
              cost: 16,
              savings: "",
            },
            {
              value: 10,
              timePeriod: "Sessions",
              cost: 160,
              savings: "",
            },
            {
              value: 20,
              timePeriod: "Sessions",
              cost: 220,
              savings: "",
            },
          ],
          savings: "",
        },
        {
          planName: "10 Hour Bundle",
          price: 135,
          costPerPeriod: 13.5,
          costPeriod: "Hour",
          descriptionTitle: "Plan Details",
          customizationOptions: [
            {
              value: 10,
              timePeriod: "Sessions",
              cost: 135,
              savings: "Save 15.63 % = $20",
            },
          ],
          savings: "Save 15.63 %",
        },
        {
          planName: "20 Hour Bundle",
          price: 240,
          costPerPeriod: 12,
          costPeriod: "Hour",
          descriptionTitle: "Plan Details",
          customizationOptions: [
            {
              value: 20,
              timePeriod: "Sessions",
              cost: 240,
              savings: "Save 20% = $60",
            },
          ],
          savings: "Save 20 %",
        },
      ],
    },
  ],
};

export const onlinePricing = {
  title: "Pricing For",
  typeOfClass: "online Classes",
  type: [
    {
      name: "Quick Sessions",
      description: "M",
      plans: [
        {
          id: 1,
          planName: "30 Mins Class",
          price: 7,
          costPerPeriod: 7,
          costPeriod: "Hour",
          descriptionTitle: "Customization Options",
          customizationOptions: [
            {
              id: 1,
              value: 1,
              timePeriod: "Session",
              cost: 7,
              savings: "",
            },
          ],
          savings: "",
        },
        {
          id: 2,
          planName: "10-Class Pack (30 Minutes Each)",
          price: 60,
          costPerPeriod: 6,
          costPeriod: "Hour",
          descriptionTitle: "Plan Details",
          customizationOptions: [
            {
              value: 10,
              timePeriod: "Sessions",
              cost: 60,
              savings: "Save 14.29% = $10",
            },
          ],
          savings: "Save 14.29%",
        },
      ],
    },
    {
      name: "Private",
      description:
        "Customized one-on-one in-person sessions available on demand",
      plans: [
        {
          planName: "1 Hour Pack",
          price: 15,
          costPerPeriod: 15,
          costPeriod: "Hour",
          descriptionTitle: "Customizations Options",
          customizationOptions: [
            {
              value: 1,
              timePeriod: "Session",
              cost: 15,
              savings: "",
            },
            {
              value: 10,
              timePeriod: "Sessions",
              cost: 150,
              savings: "",
            },
            {
              value: 20,
              timePeriod: "Sessions",
              cost: 300,
              savings: "",
            },
          ],
          savings: "",
        },
        {
          planName: "10 Hour Pack",
          price: 125,
          costPerPeriod: 12.5,
          costPeriod: "Hour",
          descriptionTitle: "Plan Details",
          customizationOptions: [
            {
              value: 10,
              timePeriod: "Session",
              cost: 125,
              savings: "Save 16.67% = $25.00",
            },
          ],
          savings: "Save 20 %",
        },
        {
          planName: "20 Hour Pack",
          price: 240,
          costPerPeriod: 12.5,
          costPeriod: "Hour",
          descriptionTitle: "Plan Details",
          customizationOptions: [
            {
              value: 20,
              timePeriod: "Session",
              cost: 240,
              savings: "Save 20% =  $60.00",
            },
          ],
          savings: "Save 20 %",
        },
        {
          planName: "90 Minute Sessions",
          price: 22,
          costPerPeriod: 22,
          costPeriod: "Hour",
          descriptionTitle: "Customization Options",
          customizationOptions: [
            {
              value: 1,
              timePeriod: "Session",
              cost: 22,
              savings: "",
            },
            {
              value: 10,
              timePeriod: "Session",
              cost: 220,
              savings: "",
            },
            {
              value: 20,
              timePeriod: "Session",
              cost: 440,
              savings: "",
            },
          ],
          savings: "",
        },
        {
          planName: "10 Hour Pack",
          price: 180,
          costPerPeriod: 18.5,
          costPeriod: "Hour",
          descriptionTitle: "Plan Details",
          customizationOptions: [
            {
              value: 10,
              timePeriod: "Session",
              cost: 185,
              savings: "Save 15.91% = $35.0",
            },
          ],
          savings: "Save 15.91 %",
        },
        {
          planName: "20 Hour Pack",
          price: 350,
          costPerPeriod: 17.5,
          costPeriod: "Hour",
          descriptionTitle: "Plan Details",
          customizationOptions: [
            {
              value: 20,
              timePeriod: "Session",
              cost: 350,
              savings: "Save 20.45% = $90",
            },
          ],
          savings: "Save 20.45 %",
        },
      ],
    },
  ],
};

export const CommunityExperiencesPlans = {
  title: "Community Experiences",
  events: [
    {
      id: 1,
      img: img.src,
      level: "INTERMEDIATE",
      mode: "online",
      location: "1923 West Ave.",
      title: "Mastering the Art of Spanish Communication",
      description:
        "Join us for an immersive workshop designed to enhance your Spanish communication skills. Whether youre a traveler navigating new destinations, a professional engaging with Spanish-speaking clients, or simply a language enthusiast, this event offers practical lessons, cultural insights, and real-world applications.",
      tags: ["TRAVELING", "ART & ARCHITECTURE", "MUSIC", "CULTURE"],
      start: "Dec 2, 9:00PM",
      end: "Dec 2, 9:00PM",
      url: "https://www.google.com",
    },
    {
      id: 2,
      img: img.src,
      level: "BEGINNER",
      mode: "offline",
      location: "1923 West Ave.",
      title: "Mastering the Art of Spanish Communication",
      description:
        "Join us for an immersive workshop designed to enhance your Spanish communication skills. Whether youre a traveler navigating new destinations, a professional engaging with Spanish-speaking clients, or simply a language enthusiast, this event offers practical lessons, cultural insights, and real-world applications.",
      tags: ["TRAVELING", "ART & ARCHITECTURE"],
      start: "Dec 2, 9:00PM",
      end: "Dec 2, 9:00PM",
      url: "https://www.google.com",
    },
    {
      id: 3,
      img: img.src,
      level: "INTERMEDIATE",
      mode: "online",
      location: "1923 West Ave.",
      title: "Mastering the Art of Spanish Communication",
      description:
        "Join us for an immersive workshop designed to enhance your Spanish communication skills. Whether youre a traveler navigating new destinations, a professional engaging with Spanish-speaking clients, or simply a language enthusiast, this event offers practical lessons, cultural insights, and real-world applications.",
      tags: ["TRAVELING", "ART & ARCHITECTURE"],
      start: "Dec 2, 9:00PM",
      end: "Dec 2, 9:00PM",
      url: "https://www.google.com",
    },
    {
      id: 4,
      img: img.src,
      level: "ADVANCED",
      mode: "offline",
      location: "1923 West Ave.",
      title: "Mastering the Art of Spanish Communication",
      description:
        "Join us for an immersive workshop designed to enhance your Spanish communication skills. Whether youre a traveler navigating new destinations, a professional engaging with Spanish-speaking clients, or simply a language enthusiast, this event offers practical lessons, cultural insights, and real-world applications.",
      tags: ["TRAVELING", "ART & ARCHITECTURE"],
      start: "Dec 2, 9:00PM",
      end: "Dec 2, 9:00PM",
      url: "https://www.google.com",
    },
  ],
};
