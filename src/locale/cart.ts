const cartLocaleEN = {
  "cart.shopping-cart": "Shopping Cart",
  "cart.plan-added": "Plan Added",
  "cart.offline-event": "Offline Event",
  "cart.amount": "Amount",
  "cart.discount": "Discount",
  "cart.total-amt": "Total Amount",
  "cart.checkout": "Checkout",
  "cart.browse-classes": "Browse Classes",
  "cart.mul-currencies":
    "Multiple currencies detected.Keep only one to continue",

  "cart.failed-fetch": "Failed to fetch cart data",

  "cart.add-success": "Added to cart successfully",
  "cart.update-success": "Updated cart successfully",
  "cart.failed-add": "Failed to add to the cart",
  "cart.failed-update": "Failed to update the cart",
  "cart.failed-remove": "Failed to remove from the cart",
  "cart.success-remove": "Removed from the cart successfully",
};

const cartLocaleES = {
  "cart.shopping-cart": "Carrito de Compras",
  "cart.plan-added": "plan agregado",
  "cart.offline-event": "Evento presencial",
  "cart.amount": "Importe",
  "cart.discount": "Descuento",
  "cart.total-amt": "Importe total",
  "cart.checkout": "Finalizar compra",
  "cart.browse-classes": "Explorar clases",
  "cart.mul-currencies":
    "Se detectaron varias monedas. Mantén solo una para continuar",
  "cart.failed-fetch": "Error al obtener los datos del carrito",

  "cart.add-success": "Agregado al carrito con éxito",
  "cart.update-success": "Carrito actualizado con éxito",
  "cart.failed-add": "No se pudo agregar al carrito",
  "cart.failed-update": "No se pudo actualizar el carrito",
  "cart.failed-remove": "No se pudo eliminar del carrito",
  "cart.success-remove": "Eliminado del carrito con éxito",
};

export { cartLocaleES, cartLocaleEN };
