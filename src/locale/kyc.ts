const nameSection = {
  en: {
    "kyc.add-name":
      "Add your first and last name to personalize your profile and make it yours.",
    "kyc.enter-name": "Enter First Name",
    "kyc.enter-last-name": "Enter Last Name",
    "kyc.fname-empty": "First name cannot be empty",
    "kyc.fname-small": "First name is too small",
    "kyc.lname-empty": "Last name cannot be empty",
    "kyc.lname-small": "last name is too small",
  },
  es: {
    "kyc.add-name":
      "Agrega tu nombre y apellido para personalizar tu perfil y hacerlo tuyo.",
    "kyc.enter-name": "Escribe tu nombre",
    "kyc.enter-last-name": "Escribe tu apellido",
    "kyc.fname-empty": "El nombre no puede estar vacío",
    "kyc.fname-small": "El nombre es demasiado corto",
    "kyc.lname-empty": "El apellido no puede estar vacío",
    "kyc.lname-small": "El apellido es demasiado corto",
  },
};

const locationSelection = {
  en: {
    "kyc.location": "What is your Location?",
    "kyc.country": "Country",
    "kyc.select-country": "Select Country",
    "kyc.select-country-please": "Please select location",
  },
  es: {
    "kyc.location": "¿Cuál es tu ubicación?",
    "kyc.country": "País",
    "kyc.select-country": "Selecciona un país",
    "kyc.select-country-please": "Por favor selecciona una ubicación",
  },
};

const languageSelection = {
  en: {
    "kyc.study-language": "What language do you want to study?",
    "kyc.select-language": "Select Language",
    "kyc.select-proficiency": "Select Proficiency",
    "kyc.additional-languages": "What additional languages do you know?",
    "kyc.language-journey":
      "Share your language journey with us! Let us know the languages you speak and your proficiency levels.",
    "kyc.select-study-lang": "Please select studying language",
    "kyc.select-study-lang-propf":
      "Please select studying language proficiency",
    "kyc.select-one-lang": "Please select atleast one language",
    "kyc.failed-fetch": "Failed to fetch languages. Please try again.",
    "kyc.add-more": "ADD MORE",
    "kyc.from-scratch": "From scratch/zero",
    "kyc.basic-knowledge": "Basic knowledge",
    "kyc.everyday-survival": "Everyday survival",
    "kyc.independent-speaker": "Independent speaker",
    "kyc.conversational": "Conversational",
    "kyc.fluent": "Fluent",
    "kyc.proficient": "Proficient",
    "kyc.nolangfound": "No languages found",
    "kyc.search-lang": "Search languages...",
    "kyc.Language": "Language",
    "kyc.Level": "Level",
  },
  es: {
    "kyc.study-language": "¿Qué idioma quieres estudiar?",
    "kyc.select-language": "Seleccionar idioma",
    "kyc.select-proficiency": "Competencias Lingüísticas",
    "kyc.additional-languages": "¿Qué otros idiomas conoces?",
    "kyc.language-journey":
      "¡Comparte tu recorrido lingüístico con nosotros! Cuéntanos qué idiomas hablas y cuál es tu nivel de dominio.",
    "kyc.select-study-lang": "Por favor selecciona el idioma de estudio",
    "kyc.select-study-lang-propf":
      "Por favor selecciona el nivel de dominio del idioma de estudio",
    "kyc.select-one-lang": "Por favor selecciona al menos un idioma",
    "kyc.failed-fetch":
      "No se pudieron obtener los idiomas. Por favor, inténtalo de nuevo.",
    "kyc.add-more": "Añadir más",
    "kyc.from-scratch": "Principiante absoluto",
    "kyc.basic-knowledge": "Conocimientos básicos",
    "kyc.everyday-survival": "Nivel de supervivencia",
    "kyc.independent-speaker": "Hablante independiente",
    "kyc.conversational": "Conversacional",
    "kyc.fluent": "Competente",
    "kyc.proficient": "Experto",
    "kyc.nolangfound": "No se encontraron idiomas",
    "kyc.search-lang": "Buscar idiomas...",
    "kyc.Language": "Idioma",
    "kyc.Level": "Nivel",
  },
};

const goalSelection = {
  en: {
    "kyc.goals": "Goals",
    "kyc.what-brings-you-here": "What brings you here?",
    "kyc.academic": "Academic",
    "kyc.professional": "Professional",
    "kyc.family": "Family",
    "kyc.travel": "Travel/Vacations",
    "kyc.personal-growth": "Personal Growth",
    "kyc.social-community": "Social/Community",
    "kyc.select-goal": "Please select goal",
  },
  es: {
    "kyc.goals": "Objetivos",
    "kyc.what-brings-you-here": "¿Qué te trae aquí?",
    "kyc.academic": "Académico",
    "kyc.professional": "Profesional",
    "kyc.family": "Familia",
    "kyc.travel": "Viajar / Vacaciones",
    "kyc.personal-growth": "Crecimiento Personal",
    "kyc.social-community": "Social / Comunidad",
    "kyc.select-goal": "Por favor selecciona un objetivo",
  },
};

const interestsSelection = {
  en: {
    "kyc.interests": "Interests",
    "kyc.experience-learning":
      "How would you like to experience your learning process?",
    "kyc.cooking": "Cooking and culinary experiences",
    "kyc.mexican-traditions": "Mexican festivals and traditions",
    "kyc.music-dance": "Music and dance",
    "kyc.travel-excursions": "Travel and excursions",
    "kyc.history-storytelling": "History and storytelling",
    "kyc.everyday-conversations": "Everyday conversations",
    "kyc.shopping-markets": "Shopping and markets",
    "kyc.sports-activities": "Sports and activities",
    "kyc.work-growth": "Work and professional growth",
    "kyc.friendship-networking": "Friendship and networking",
    "kyc.art-literature": "Art, literature, and architecture",
    "kyc.hate": "Hate it",
    "kyc.neutral": "Neutral",
    "kyc.love": "Love it",
    "kyc.fetchinterests-failed": "Failed to fetch interests.Please try again.",
    "kyc.interests-select": "Thank you for your selections!",
    "kyc.interests-select-please": "Please select your interest",
    "kyc.interests-select-remaining": "Please select remaining interests",
  },
  es: {
    "kyc.interests": "Intereses",
    "kyc.experience-learning":
      "¿Cómo te gustaría vivir tu proceso de aprendizaje?",
    "kyc.cooking": "Cocina y experiencias gastronómicas",
    "kyc.mexican-traditions": "Festivales y tradiciones mexicanas",
    "kyc.music-dance": "Música y danza",
    "kyc.travel-excursions": "Viajes y excursiones",
    "kyc.history-storytelling": "Historia y narración de historias",
    "kyc.everyday-conversations": "Conversaciones cotidianas",
    "kyc.shopping-markets": "Compras y mercados",
    "kyc.sports-activities": "Deportes y actividades",
    "kyc.work-growth": "Trabajo y crecimiento profesional",
    "kyc.friendship-networking": "Amistad y red de contactos",
    "kyc.art-literature": "Arte, literatura y Arquitectura",
    "kyc.hate": "Lo odio",
    "kyc.neutral": "Neutral",
    "kyc.love": "Lo amo",
    "kyc.fetchinterests-failed":
      "No se pudieron obtener los intereses. Por favor, inténtalo de nuevo.",
    "kyc.interests-select": "¡Gracias por tus selecciones!",
    "kyc.interests-select-please": "Por favor selecciona tu interés",
    "kyc.interests-select-remaining":
      "Por favor selecciona los intereses restantes",
  },
};

const congratulationsSelection = {
  en: {
    "kyc.congratulations": "🎉 Congratulations!",
    "kyc.welcome-community": "We're happy to have you as part of our community",
    "kyc.get-started": "Let’s get started!",
    "kyc.follow-us": "Please follow us",
  },
  es: {
    "kyc.congratulations": "¡Felicidades!",
    "kyc.welcome-community":
      "Nos alegra tenerte como parte de nuestra comunidad.",
    "kyc.get-started": "¡Comencemos!",
    "kyc.follow-us": "Por favor, síguenos",
  },
};

const manageProfileLocale = {
  en: {
    "mp.select-interests": "Please select the interests",
    "mp.complete-profile": "Please complete your profile",
    "mp.valid-whatsapp": "Please enter a valid WhatsApp phone number",
    "mp.profile-info-success": "Profile information updated successfully.",
    "mp.profile-info-updated": "Your profile information was updated.",
    "mp.unexpected": "Something unexpected happened. Please retry.",
    "mp.manage-profile": "Manage Profile",
    "mp.whatsapp-no": "WhatsApp No.",
    "mp.enter-whatsapp-no": "Enter your WhatsApp No.",
    "mp.save": "Save",
    "mp.cancel": "Cancel",
    "mp.location": "Location",
    "mp.first-name": "First Name",
    "mp.last-name": "Last Name",
    "mp.student-stated-level": "Select Student-stated level",
    "mp.student-stated-level-only": "Student-stated level",
    "mp.prof-only": "Proficiency",
    "mp.current-proficiency": "Current proficiency level",
    "mp.select-current-proficiency": "Select Current proficiency level",
    "mp.Beginner": "Beginner",
    "mp.Advanced": "Advanced",
    "mp.Intermediate": "Intermediate",
    "mp.proficiency-level": "Proficiency level",
    "mp.select-interests-love": "Select Interests you love",
    "mp.select-interests-hate": "Select Interests you hate",
    "mp.select-interests-ok": "Select Interests you're okay with",
    "mp.contact-info": "Contact Info",
    "mp.selection-pending": "Selection pending",
    "mp.select-level": "Select Level",
    "mp.no-available-langs": "No available languages",
    "mp.langs-load-failed": "Failed to load language data",
    "mp.name": "Name",
  },
  es: {
    "mp.select-interests": "Por favor selecciona los intereses",
    "mp.complete-profile": "Por favor completa tu perfil",
    "mp.valid-whatsapp": "Por favor ingresa un número de WhatsApp válido",
    "mp.profile-info-success":
      "La información del perfil se actualizó correctamente.",
    "mp.profile-info-updated": "Tu información de perfil fue actualizada.",
    "mp.unexpected": "Ocurrió algo inesperado. Por favor inténtalo de nuevo.",
    "mp.manage-profile": "Administrar perfil",
    "mp.whatsapp-no": "N.º de WhatsApp",
    "mp.enter-whatsapp-no": "Ingresa tu N.º de WhatsApp",
    "mp.save": "Guardar",
    "mp.cancel": "Cancelar",
    "mp.location": "Ubicación",
    "mp.first-name": "Nombre",
    "mp.last-name": "Apellido",
    "mp.student-stated-level": "Selecciona el nivel indicado por el estudiante",
    "mp.student-stated-level-only": "Nivel indicado por el estudiante",
    "mp.prof-only": "Competencia",
    "mp.current-proficiency": "Nivel de competencia actual",
    "mp.select-current-proficiency":
      "Selecciona el nivel de competencia actual",
    "mp.Beginner": "Principiante",
    "mp.Advanced": "Avanzado",
    "mp.Intermediate": "Intermedio",
    "mp.proficiency-level": "Nivel de competencia",
    "mp.select-interests-love": "Selecciona los intereses que te encantan",
    "mp.select-interests-hate": "Selecciona los intereses que no te gustan",
    "mp.select-interests-ok":
      "Selecciona los intereses con los que estás de acuerdo",
    "mp.contact-info": "Información de contacto",
    "mp.selection-pending": "Selección pendiente",
    "mp.select-level": "Selecciona el nivel",
    "mp.no-available-langs": "No hay idiomas disponibles",
    "mp.langs-load-failed": "Error al cargar los datos de idiomas",
    "mp.name": "Nombre",
  },
};

const kycLocaleEN = {
  ...nameSection.en,
  ...locationSelection.en,
  ...languageSelection.en,
  ...goalSelection.en,
  ...interestsSelection.en,
  ...manageProfileLocale.en,
  ...congratulationsSelection.en,

  "kyc.welcome": "Welcome",
  "kyc.create-account": "Create your account",
  "kyc.welcome-message": "Welcome! Please fill in the details to get started.",
  "kyc.user-updated-success": "User updated successfully",
  "kyc.user-updated-failure": "Failed to update user.Please try again.",
};

const kycLocaleES = {
  ...nameSection.es,
  ...locationSelection.es,
  ...languageSelection.es,
  ...goalSelection.es,
  ...interestsSelection.es,
  ...congratulationsSelection.es,
  ...manageProfileLocale.es,

  "kyc.welcome": "¡Bienvenid@!",
  "kyc.create-account": "Crea tu cuenta",
  "kyc.welcome-message":
    "¡Bienvenido(a)! Por favor completa tus datos para comenzar.",
  "kyc.user-updated-success": "Usuario actualizado correctamente",
  "kyc.user-updated-failure":
    "No se pudo actualizar el usuario. Por favor, inténtalo de nuevo.",
};

export { kycLocaleEN, kycLocaleES };
