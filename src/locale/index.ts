import { aboutLocale<PERSON>, aboutLocaleES } from "./about";
import {
  businessEnglishClassesLocaleEN,
  businessEnglishClassesLocaleES,
} from "./businessEnglishClasses";
import { cartLocaleEN, cartLocaleES } from "./cart";
import { classesLocaleEN, classesLocaleES } from "./classes";
import { clubsLocaleEN, clubsLocaleES } from "./clubs";
import { commonLocaleEN, commonLocaleES } from "./common";
import { dashboardLocaleEN, dashboardLocaleES } from "./dashboard";
import { emails } from "./email";
import {
  experiencesClassesLocaleEN,
  experiencesClassesLocaleES,
} from "./experiencesClasses";
import { homeLocaleEN, homeLocaleES } from "./home";
import {
  inpersonClassesLocaleEN,
  inpersonClassesLocaleES,
} from "./inPersonClasses";
import { kycLocaleEN, kycLocaleES } from "./kyc";
import { onlineClassesLocaleEN, onlineClassesLocaleES } from "./onlineClasses";

const locales = {
  en: {
    ...aboutLocaleEN,
    ...commonLocaleEN,
    ...classesLocaleEN,
    ...clubsLocaleEN,
    ...experiencesClassesLocaleEN,
    ...homeLocaleEN,
    ...inpersonClassesLocaleEN,
    ...onlineClassesLocaleEN,
    ...cartLocaleEN,
    ...dashboardLocaleEN,
    ...kycLocaleEN,
    ...businessEnglishClassesLocaleEN,
    ...emails.en,
  },
  es: {
    ...aboutLocaleES,
    ...commonLocaleES,
    ...classesLocaleES,
    ...clubsLocaleES,
    ...experiencesClassesLocaleES,
    ...homeLocaleES,
    ...inpersonClassesLocaleES,
    ...onlineClassesLocaleES,
    ...cartLocaleES,
    ...dashboardLocaleES,
    ...kycLocaleES,
    ...businessEnglishClassesLocaleES,
    ...emails.es,
  },
};

export default locales;
