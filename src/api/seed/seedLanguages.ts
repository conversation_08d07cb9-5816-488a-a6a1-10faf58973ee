import {
  LanguageProficiency,
  Language,
  User,
  UserLanguage,
  Video,
  Event,
  Club,
} from "@/api/mongo";
import { languages } from "../../../data/languages";
import { proficiencies } from "../../../data/proficiencies";

export const seedLanguages = async (isUpsert = true) => {
  handleLanguages(isUpsert);
  handleProficiencies(isUpsert);
};

const handleProficiencies = async (isUpsert = true) => {
  try {
    if (isUpsert) {
      for (let proficiency of proficiencies) {
        await LanguageProficiency.findOneAndUpdate(
          { cefr: proficiency.cefr },
          proficiency,
          { upsert: true }
        );
      }
    } else {
      await LanguageProficiency.insertMany(proficiencies);
    }
  } catch (error) {
    console.error(
      `Something went wrong while seeding language proficiencies due to`,
      error
    );
  }
};

const handleLanguages = async (isUpsert = true) => {
  try {
    if (isUpsert) {
      for (let lang of languages) {
        await Language.findOneAndUpdate({ name: lang.name }, lang, {
          upsert: true,
        });
      }
    } else {
      await Language.insertMany(languages);
    }
  } catch (error) {
    console.error(`Something went wrong while seeding languages due to`, error);
  }
};

export const handleReplaceAllProficiencies = async () => {
  try {
    // as we will replace the existing proficiencies with the new one
    // we will have to clear the earlier selected proficiencies
    //  from all the places
    await User.updateMany(
      {},
      {
        $set: {
          proficiencyOfLanguageOfInterest: null,
          completeLater: true,
          languages: [],
        },
      }
    );
    await UserLanguage.deleteMany();
    await Video.updateMany(
      { proficiencyLevel: { $exists: true } },
      {
        $set: { proficiencyLevel: null },
      }
    );
    await Event.updateMany(
      { proficiencyLevel: { $exists: true } },
      {
        $set: { proficiencyLevel: null },
      }
    );
    await Club.updateMany(
      { proficiencyLevel: { $exists: true } },
      {
        $set: { proficiencyLevel: null },
      }
    );
    await LanguageProficiency.deleteMany();
    await LanguageProficiency.insertMany(proficiencies);
  } catch (error) {
    console.error(
      "Something went wrong in handleReplaceAllProficiencies due to ",
      error
    );
    throw error;
  }
};
