import { ClassDetails } from "@/api/mongo";
import { ALL_CLASSES_PRICING } from "data/pricing";

export const seedClasses = async (isUpsert = true) => {
  try {
    for (let clas of ALL_CLASSES_PRICING) {
      if (isUpsert) {
        await ClassDetails.findOneAndUpdate({ uniqueId: clas.uniqueId }, clas, {
          upsert: true,
        });
      } else {
        await ClassDetails.create(clas);
      }
    }
  } catch (error) {
    console.error(`Something went wrong while seeding countries due to`, error);
  }
};
