import { Country } from "@/api/mongo";
import { countries } from "data/countries";

export const seedCountries = async (isUpsert = true) => {
  try {
    for (let ctry of countries) {
      if (isUpsert) {
        await Country.findOneAndUpdate(
          { name: ctry.Name },
          { name: ctry.Name, code: ctry.Code },
          { upsert: true }
        );
      } else {
        await Country.create({ name: ctry.Name, code: ctry.Code });
      }
    }
  } catch (error) {
    console.error(`Something went wrong while seeding countries due to`, error);
  }
};
