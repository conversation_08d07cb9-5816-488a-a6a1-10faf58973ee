import {
  ASSETS_STATUS,
  ASSETS_TYPES,
  CLASSES_TYPE,
  CURRENCY_ENUM,
  DURATION_TYPE,
  EVENT_MODE_TYPE,
  IN_PERSON_TYPE,
  INTEREST_TYPES,
  LEVEL_TYPES,
  ONLINE_CLASSES_TYPE,
  PAYMENT_MODE,
  PAYMENT_STATUS,
  PLAN_FOR,
  PROGRESS_STATUS,
  ROLE_TYPES,
} from "@/constant/Enums";
import { localPlanType, Maybe, MaybeObjectId, ValueOf } from "@/types";
import { Document, Types } from "mongoose";

type TimeStampsAndId = {
  _id: Types.ObjectId;
  createdOn: Date;
  updatedOn: Date;
  createdAt: Date;
  updatedAt: Date;
};

export type RoleType = Document & {
  description: string;
  type: ValueOf<typeof ROLE_TYPES>;
  user: MaybeObjectId<UserType>;
};

export type PasswordType = Document & {
  user: Types.ObjectId;
  passwordHash: string;
  lastThreeHashes: string[];
  requiresReset: boolean;
  createdOn: Date;
  updatedOn: Date;
};

export type BaseInterestType = {
  name: string;
  image: string;
  _id: string;
};

export type InterestType = Document & BaseInterestType;

export type GoalType = Document & {
  name: string;
  image: string;
  _id: Types.ObjectId;
};

export type UserSelectedInterestsType = {
  id: Types.ObjectId | InterestType;
  rating: (typeof INTEREST_TYPES)[keyof typeof INTEREST_TYPES];
}[];

export type UserType = Document &
  TimeStampsAndId & {
    clerkId: string;
    firstName: string;
    lastName: string;
    email: string;
    countryOfResidence: Types.ObjectId | CountryType;
    phone: string;
    languages: Types.ObjectId[] | UserLanguageType[]; // UserLanguage[]
    primaryLanguage: Types.ObjectId | LanguageType;
    languageOfInterest: Types.ObjectId | LanguageType;
    proficiencyOfLanguageOfInterest: MaybeObjectId<LanguageProficiencyType>;
    allowedRoles: Types.ObjectId[] | RoleType[];
    member: Types.ObjectId; // Member
    blocked: boolean;
    active: boolean;
    profileImageKey: string;
    profileImageId: string;
    interest: UserSelectedInterestsType;
    goals: Types.ObjectId[] | GoalType[];
    profileCreated: boolean;
    completeLater: boolean;
    stripeCustomerId: string;
    stripeCustomerIdMxn: string;
    notionUrl: string;
    whatsAppNo: string;
    level: ValueOf<typeof LEVEL_TYPES>;
    visitorId: MaybeObjectId<VisitorType>;
    profileImageUrl: string;
    role: ROLE_TYPES;
    password: Types.ObjectId;
  };

export type MemberType = Document & {
  level: string;
  type: string; //individual or business
  businessFavorites: Types.ObjectId[]; // Business[]
  videoFavorites: Types.ObjectId[]; // VideoFavorite[]
  classes: Types.ObjectId[]; // Class[]
  payments: Types.ObjectId[];
  user: Types.ObjectId;
  active: boolean;
  createdOn: Date;
  updatedOn: Date;
};

export type VideoType = Document & {
  id: string;
  title: string;
  duration: number;
  description: string;
  language: Types.ObjectId;
  proficiencyLevel: MaybeObjectId<LanguageProficiencyType>;
  categories: [string];
  creator: Types.ObjectId | UserType;
  status: string;
  environment: string;
  thumbnailKey: String;
  en_subtitleKey: String;
  es_subtitleKey: String;
  videoKey: String;
  level: ValueOf<typeof LEVEL_TYPES>;
  isFeatured: Boolean;
  likedCount: Number;
  progress: VideoProgressSchemaType;

  collectionIds: string[];
  categoryIds: string[];
  videoUrl: string;
  esSubtitleUrl: string;
  enSubtitleUrl: string;
  thumbnailUrl: string;
  isLiked: boolean;
};

export type MediaJobType = Document & {
  status: ValueOf<typeof ASSETS_STATUS>;
  errorMsg: String;
  videoId: String;
  assetType: ValueOf<typeof ASSETS_TYPES>;
};

export type PointType = Document & {
  type: string;
  coordinates: number[];
  createdOn: Date;
  updatedOn: Date;
};

export type CountryType = Document & {
  code: string;
  name: string;
  _id: Types.ObjectId;
};

export type AddressType = Document & {
  line1: string;
  line2: string;
  city: string;
  state: string;
  postalCode: string;
  country: Types.ObjectId;
  createdOn: Date;
  updatedOn: Date;
};

export type BusinessType = Document & {
  businessType: string;
  user: Types.ObjectId;
  numFavorites: number;
  location: PointType;
  address: AddressType;
  phone: string;
  shortDescription: string;
  longDescription: string;
  priceRange: number; //not sure how this will be captured but we want to use the standard $$$$ system
  active: boolean;
  createdOn: Date;
  updatedOn: Date;
};

export type ClassesType = Document & {
  teachers: Types.ObjectId[];
  title: string;
  city: string;
  address: {
    type: AddressType;
  };
  dates: Date[];
  hours: number;
  shortDescription: string;
  longDescription: string;
  cost: number;
  capacity: number;
  enrolled: Types.ObjectId[];
  active: boolean; // allows members to enroll
  createdOn: Date;
  updatedOn: Date;
};

export type TeacherType = Document & {
  user: Types.ObjectId;
  name: string;
  classes: Types.ObjectId;
  active: boolean;
  createdOn: Date;
  updatedOn: Date;
};

type enEsType = {
  en: string;
  es: string;
};

export type LanguageProficiencyType = Document & {
  _id: Types.ObjectId;
  cefr: string;
  pfLevel: enEsType;
  simpleLevel: enEsType;
  longDescription: enEsType;
  selfAssessment: enEsType;
  shortSelfAssessment: enEsType;
};
export type LanguageType = Document & {
  _id: Types.ObjectId;
  name: string;
  code: string;
  nameInEnglish: string;
};

export type WaitlistType = Document & {
  email: string;
  fname: string;
  lname: string;
  language: string;
  createdOn: Date;
  updatedOn: Date;
};

export type LaunchWaitlistType = Document & {
  email: string;
  createdOn: Date;
  updatedOn: Date;
};

export type UserLanguageType = Document & {
  user: Types.ObjectId;
  language: Types.ObjectId | LanguageType;
  proficiency: Types.ObjectId | LanguageProficiencyType;
};

export type SubscriptionType = Document & {
  name: string;
  type: string; // trial, paid, etc
  period: string; // monthly or yearly
  features: string[];
  description: string;
  activeFrom: Date;
  activeTo: Date;
  membersActive: Types.ObjectId[];
  membersInactive: Types.ObjectId[];
  subscriptionToTransferTo: Types.ObjectId;
  subscriptionToTransferToAfter: number; // number of days after which member/business transfers to new subscription
  price: number;
  country: Types.ObjectId;
  createdOn: Date;
  updatedOn: Date;
};

export type BillingInfoType = Document & {
  type: string; // stripe, paypal, etc
  active: boolean;
  customerId: string;
  subscriptionId: string;
  member: Types.ObjectId;
  createdOn: Date;
  updatedOn: Date;
};

export type PaymentType = Document & {
  chargeId: string;
  amount: number;
  status: string; // pending, paid, failed, refunded
  description: string;
  member: Types.ObjectId;
  billingInfo: Types.ObjectId;
  createdOn: Date;
  updatedOn: Date;
};

export type CategoryType = Document & {
  _id: Types.ObjectId;
  name: String;
  imgSrc: String;
};

export type VideoLikeType = Document & {
  _id: Types.ObjectId;
  likedBy: Types.ObjectId;
  videoId: Types.ObjectId;
};

export type VideoCategoryType = Document & {
  _id: Types.ObjectId;
  name: String;
  videoId: Types.ObjectId;
  categoryId: Types.ObjectId;
};

export type VideosCollectionType = Document &
  TimeStampsAndId & {
    name: String;
    desc: String;
    imageId: String;
    imageKey: String;
    categories: String[];
    level: ValueOf<typeof LEVEL_TYPES>;
    likedCount: Number;
    videoCount: Number;
    videoDuration: Number;
    videoCollectionProgress: VideoCollectionProgressSchemaType;
    coverImageUrl: string;
    videos?: VideoType[];
    isLiked: boolean;
    proficiencyLevel: MaybeObjectId<LanguageProficiencyType>;
  };

export type VideosCollectionMapType = Document &
  TimeStampsAndId & {
    collectionId: Types.ObjectId;
    videoId: Types.ObjectId;
  };

export type VideoCollectionLikesMapType = Document &
  TimeStampsAndId & {
    likedBy: Types.ObjectId;
    collectionId: Types.ObjectId;
  };

export type VideoProgressSchemaType = Document &
  TimeStampsAndId & {
    videoId: MaybeObjectId<VideoType>;
    userId: Types.ObjectId;
    progress: Number;
    state: ValueOf<typeof PROGRESS_STATUS>;
  };

export type VideoCollectionProgressSchemaType = Document &
  TimeStampsAndId & {
    collectionId: MaybeObjectId<VideosCollectionType>;
    userId: Types.ObjectId;
    progress: Number;
    state: ValueOf<typeof PROGRESS_STATUS>;
  };

export type EventSchemaType = Document &
  TimeStampsAndId & {
    title: string;
    description: string;
    startDate: string;
    startTime: string;
    endDate: string;
    endTime: string;
    timezone: string;
    startDateTime: Date;
    endDateTime: Date;
    mode: ValueOf<typeof EVENT_MODE_TYPE>;
    proficiencyLevel: Types.ObjectId | LanguageProficiencyType;
    categories: [string];
    location: string;
    url: string;
    price: number;
    currency: CURRENCY_ENUM;
    maxNumberOfRegistrations: number;
    targetLanguage: MaybeObjectId<LanguageType>;
    eventOn: MaybeObjectId<EventOnType>[];
    imagesKeysAndIds: {
      key: string;
      id: string;
    }[];

    // this is not for the schema but we will be using this
    // when we return the images url
    images: [
      {
        key: string;
        id: string;
        url: string;
      }
    ];
    // eventImageId: string;
    // eventImageKey: string;
    // eventImageUrl: string;
  };

export type ClassesPricingPlan = {
  price: number;
  singlePrice: number;
  discount: number;
  duration: number;
  planId: string;
};

export type ClassesPricingType = Document &
  TimeStampsAndId & {
    uniqueId: string;
    benefits: string;
    title: string;
    subtitle: string;
    description: string;
    benefitsEs: string;
    descriptionEs: string;
    isLimitedDeal: boolean;
    isPopular: boolean;
    type: ValueOf<typeof CLASSES_TYPE>;
    durationType: ValueOf<typeof DURATION_TYPE>;
    subType:
      | ValueOf<typeof IN_PERSON_TYPE>
      | ValueOf<typeof ONLINE_CLASSES_TYPE>;
    plans: [ClassesPricingPlan];
  };

export type SingleMembershipType = {
  planFor: ValueOf<typeof PLAN_FOR>;
  emailId: string;
};

export type MembershipType = SingleMembershipType[];

export type PlanType = localPlanType;

export type CartType = Document &
  TimeStampsAndId & {
    classesId: ClassesPricingType;
    userId: MaybeObjectId<UserType>;
    eventId: MaybeObjectId<EventSchemaType>;
    clubId: MaybeObjectId<ClubType>;
    plans: PlanType[];
    isCheckedOut: boolean;
    isBuying: boolean;
    memberships: MembershipType;
    transactionId: MaybeObjectId<TransactionType>;
    eventOn: MaybeObjectId<EventOnType>;

    // we are doing this for my-purchases
    currentPlan: PlanType;
    currentMembership: SingleMembershipType;
    isSuggested: boolean;
  };

export type EventsPriceDetailsType = {
  eventInfo: MaybeObjectId<EventSchemaType>;
  price: Number;
  currency: string;
  eventOn: MaybeObjectId<EventOnType>;
};

export type TransactionType = Document &
  TimeStampsAndId & {
    userId: MaybeObjectId<UserType>;
    // plans: PlanType[];
    // memberships: MembershipType;
    // classesId: [ClassesPricingType];
    // clubsId: [MaybeObjectId<ClubType>];

    classesDetails: [
      {
        classInfo: ClassesPricingType;
        plans: PlanType[];
      }
    ];
    clubsDetails: [
      {
        clubInfo: MaybeObjectId<ClubType>;
        memberships: MembershipType;
        price: Number;
        currency: CURRENCY_ENUM;
      }
    ];
    eventIds: [MaybeObjectId<EventSchemaType>];
    transactionId: [MaybeObjectId<TransactionType>];
    cartId: [MaybeObjectId<CartType>];
    status: ValueOf<typeof PAYMENT_STATUS>;
    email: string;
    stripeCheckoutId: string;
    paymentIntentId: string;
    price: Number;
    discount: Number;
    finalAmount: Number;
    transactionDate: Date;
    mode: ValueOf<typeof PAYMENT_MODE>;
    stripeSubscriptionId: string;
    subscriptionEndDate: Date;
    unsubscribe: {
      value: Boolean;
      date: Date;
    };
    invoices: [string];
    eventsPriceDetails: [EventsPriceDetailsType];
    customProductsDetails: {
      price: Number;
      currency: CURRENCY_ENUM;
      customProductsInfo: MaybeObjectId<CustomProductType>;
    };
    isGift: boolean;
    giftedBy: MaybeObjectId<UserType>;
    giftTransactionId: MaybeObjectId<TransactionType>;
  };

export type ClubType = Document &
  TimeStampsAndId & {
    title: string;
    about: string;
    price: Number;
    theme: string;
    // level: ValueOf<typeof LEVEL_TYPES>;
    proficiencyLevel: Types.ObjectId | LanguageProficiencyType;
    categories: [string];
    teachers: [MaybeObjectId<UserType>];
    highlights: [string];
    imagesKeysAndIds: [
      {
        key: string;
        id: string;
      }
    ];
    images: [
      {
        key: string;
        id: string;
        url: string;
      }
    ];
    stripePriceId: string;
    historicalPrices: [MaybeObjectId<ClubPriceDetailsType>];
    stripeProductId: string;
    noOfStudentsEnrolled: number;
    ratings: {
      value: number;
      totalRatings: number;
    };
    currency: CURRENCY_ENUM;
    targetLanguage: Types.ObjectId | LanguageType;
  };

export type SingleScheduledStudentType = {
  transaction: Types.ObjectId | TransactionType;
  userId: Types.ObjectId | UserType;
  level: ValueOf<typeof LEVEL_TYPES>;
  selectedPlan: PlanType;
  scheduledAt: Date;
};

export type ScheduledStudentType = SingleScheduledStudentType[];

export type ScheduledTeacherType = {
  location: string;
  level: ValueOf<typeof LEVEL_TYPES>;
  teacherId: Types.ObjectId | UserType;
}[];

export type ScheduleType = Document &
  TimeStampsAndId & {
    type: ValueOf<typeof CLASSES_TYPE>;
    subType:
      | ValueOf<typeof IN_PERSON_TYPE>
      | ValueOf<typeof ONLINE_CLASSES_TYPE>;
    startDate: Date;
    endDate: Date;
    language: Types.ObjectId | LanguageType;
    students: ScheduledStudentType;
    teachers: ScheduledTeacherType;
  };

export type VisitorType = Document &
  TimeStampsAndId & {
    userId: MaybeObjectId<UserType>;
    latitude: Number;
    longitude: Number;
  };

export type ClubPriceDetailsType = Document &
  TimeStampsAndId & {
    priceId: string;
    amount: number;
    currency: CURRENCY_ENUM;
    clubId: MaybeObjectId<ClubType>;
  };

export type EventOnType = Document &
  TimeStampsAndId & {
    startDate: Date;
    startTime: String;
    timezone: String;
    endDate: String;
    endTime: String;
    startDateTime: Date;
    endDateTime: Date;
    eventId: MaybeObjectId<EventSchemaType>;
  };

export type CustomProductType = Document &
  TimeStampsAndId & {
    title: string;
    description: string;
    internalNotes: string;
    currency: CURRENCY_ENUM;
    targetLanguage: MaybeObjectId<LanguageType>;
    price: number;
    startDate: Date;
    expirationDate: Date;
    instructor: MaybeObjectId<UserType>[];
    code: string;
    timezone: string;
    transactionId: MaybeObjectId<TransactionType>;
    productFor: MaybeObjectId<UserType>;
    isExpired: boolean;
  };
