import mongoose, { Model, Schema } from "mongoose";
import {
  BillingInfoType,
  BusinessType,
  ClassesType,
  CountryType,
  LanguageType,
  LanguageProficiencyType,
  MemberType,
  PasswordType,
  PaymentType,
  RoleType,
  SubscriptionType,
  TeacherType,
  UserType,
  VideoType,
  UserLanguageType,
  WaitlistType,
  LaunchWaitlistType,
  GoalType,
  InterestType,
  VideoCategoryType,
  VideoLikeType,
  CategoryType,
  VideosCollectionMapType,
  VideoCollectionLikesMapType,
  VideosCollectionType,
  MediaJobType,
  VideoProgressSchemaType,
  VideoCollectionProgressSchemaType,
  EventSchemaType,
  ClassesPricingType,
  CartType,
  TransactionType,
  ClubType,
  ScheduleType,
  VisitorType,
  EventOnType,
  CustomProductType,
} from "./mongoTypes";
import {
  ASSETS_STATUS,
  ASSETS_TYPES,
  CLASSES_TYPE,
  DURATION_TYPE,
  ENVIRONMENT,
  EVENT_MODE_TYPE,
  IN_PERSON_TYPE,
  INTEREST_TYPES,
  LEVEL_TYPES,
  ONLINE_CLASSES_TYPE,
  PAYMENT_STATUS,
  PLAN_FOR,
  PROGRESS_STATUS,
  VIDEO_STATUS,
  ROLE_TYPES,
  PAYMENT_MODE,
  CURRENCY_ENUM,
} from "@/constant/Enums";

const {
  MONGO_URI: uri,
  MONGO_INITDB_ROOT_USERNAME: user,
  MONGO_INITDB_ROOT_PASSWORD: pass,
  MONGO_INITDB_DATABASE: dbName,
} = process.env;

/*
  Logging messages for mongo connection
*/
mongoose.connection.on("disconnected", () => {
  console.error("mongo -\\- Connection disconnected");
});
mongoose.connection.on("error", () => {
  console.error("mongo -\\- Connection error");
});
mongoose.connection.on("reconnect", () => {
  console.error("mongo -> reconnected");
});
mongoose.connection.on("reconnectFailed", () => {
  console.error("mongo -\\- could not reconnect");
});

/*
  Connect to mongo -- forcing it to use admin
*/
mongoose.connect(
  uri.replace("USER", user).replace("PASS", encodeURIComponent(pass)),
  {
    dbName,
  }
);

const RoleSchema = new Schema({
  type: {
    type: String,
    enum: Object.values(ROLE_TYPES),
    required: true,
  },
  description: String,
  user: { type: Schema.Types.ObjectId, ref: "User" },
});

const PasswordSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: "User" },
    passwordHash: { type: String, required: true },
    lastThreeHashes: [String],
    requiresReset: Boolean,
  },
  { timestamps: true }
);

const LanguageSchema = new Schema({
  code: String,
  name: String, // native string English, Espanol, etc (n-yah not in text)
  nameInEnglish: String,
});

const InterestSchema = new Schema(
  {
    name: { type: String, required: true },
    image: { type: String, required: true },
  },
  { timestamps: true }
);

const GoalSchema = new Schema(
  {
    name: { type: String, required: true },
    image: { type: String, required: true },
  },
  { timestamps: true }
);

const UserSchema = new Schema(
  {
    clerkId: { type: String, required: false },
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    email: { type: String, required: false },
    countryOfResidence: { type: Schema.Types.ObjectId, ref: "Country" },
    phone: { type: String },
    languages: [{ type: Schema.Types.ObjectId, ref: "UserLanguage" }],
    primaryLanguage: { type: Schema.Types.ObjectId, ref: "Language" },
    languageOfInterest: { type: Schema.Types.ObjectId, ref: "Language" },
    proficiencyOfLanguageOfInterest: {
      type: Schema.Types.ObjectId,
      ref: "LanguageProficiency",
    },
    allowedRoles: [{ type: Schema.Types.ObjectId, ref: "Role" }],
    member: { type: Schema.Types.ObjectId, ref: "Member" },
    blocked: { type: Boolean, default: false },
    active: { type: Boolean, default: true },
    profileImageKey: String,
    profileImageId: String,
    interest: [
      {
        id: {
          type: Schema.Types.ObjectId,
          ref: "Interest",
        },
        rating: {
          type: String,
          enum: [
            INTEREST_TYPES.HATE,
            INTEREST_TYPES.NEUTRAL,
            INTEREST_TYPES.LOVE,
          ],
          required: true,
        },
      },
    ],
    goals: [{ type: Schema.Types.ObjectId, ref: "Goal" }],
    profileCreated: {
      type: Boolean,
      default: false,
    },
    completeLater: {
      type: Boolean,
      default: false,
    },
    stripeCustomerId: {
      type: String,
    },
    stripeCustomerIdMxn: {
      type: String,
    },
    notionUrl: {
      type: String,
    },
    whatsAppNo: {
      type: String,
    },
    level: {
      type: String,
      enum: [
        LEVEL_TYPES.ADVANCED,
        LEVEL_TYPES.BEGINNER,
        LEVEL_TYPES.INTERMEDIATE,
      ],
      default: LEVEL_TYPES.BEGINNER,
    },
    visitorId: {
      type: Schema.Types.ObjectId,
      ref: "Visitor",
    },
  },
  { timestamps: true }
);

const UserLanguageSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: "User" },
    language: { type: Schema.Types.ObjectId, ref: "Language" },
    proficiency: { type: Schema.Types.ObjectId, ref: "LanguageProficiency" },
  },
  { timestamps: true }
);

const MemberSchema = new Schema(
  {
    level: String,
    type: String, //individual or business
    businessFavorites: [{ type: Schema.Types.ObjectId, ref: "Business" }],
    videoFavorites: [{ type: Schema.Types.ObjectId, ref: "VideoFavorite" }],
    classes: [{ type: Schema.Types.ObjectId, ref: "Classes" }],
    payments: [{ type: Schema.Types.ObjectId, ref: "Payment" }],
    user: { type: Schema.Types.ObjectId, ref: "User" },
    active: Boolean,
  },
  { timestamps: true }
);

const VideoLikeSchema = new Schema(
  {
    likedBy: { type: Schema.Types.ObjectId, ref: "User" },
    videoId: { type: Schema.Types.ObjectId, ref: "Video" },
  },
  { timestamps: true }
);

const VideoCategorySchema = new Schema(
  {
    name: String,
    videoId: { type: Schema.Types.ObjectId, ref: "Video" },
    categoryId: { type: Schema.Types.ObjectId, ref: "Category" },
  },
  { timestamps: true }
);

const VideoSchema = new Schema(
  {
    id: String,
    title: String,
    duration: Number,
    description: String,
    language: { type: Schema.Types.ObjectId, ref: "Language" },
    proficiencyLevel: {
      type: Schema.Types.ObjectId,
      ref: "LanguageProficiency",
    },
    categories: [String],
    creator: { type: Schema.Types.ObjectId, ref: "User" },
    status: {
      type: String,
      enum: [VIDEO_STATUS.NONE, VIDEO_STATUS.READY],
      default: VIDEO_STATUS.NONE,
    },
    environment: {
      type: String,
      enum: [ENVIRONMENT.PUBLISHED, ENVIRONMENT.SANDBOX],
      required: true,
    },
    thumbnailKey: String,
    en_subtitleKey: String,
    es_subtitleKey: String,
    videoKey: String,
    isFeatured: Boolean,
    likedCount: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

const CategorySchema = new Schema(
  {
    name: String,
    imgSrc: String,
  },
  { timestamps: true }
);

const PointSchema = new Schema(
  {
    type: { type: String, enum: ["Point"], default: "Point" },
    coordinates: { type: [Number], required: true },
  },
  { timestamps: true }
);

const CountrySchema = new Schema({
  code: String,
  name: String,
});

const AddressSchema = new Schema(
  {
    line1: String,
    line2: String,
    city: String,
    state: String,
    postalCode: String,
    country: { type: Schema.Types.ObjectId, ref: "Country" },
  },
  { timestamps: true }
);

const BusinessSchema = new Schema(
  {
    businessType: String,
    user: { type: Schema.Types.ObjectId, ref: "User" },
    numFavorites: { type: Number, default: 0 },
    location: { type: PointSchema },
    address: { type: AddressSchema },
    phone: String,
    shortDescription: String,
    longDescription: String,
    priceRange: Number, //not sure how this will be captured but we want to use the standard $$$$ system
    active: Boolean,
  },
  { timestamps: true }
);

const ClassSchema = new Schema(
  {
    teachers: [{ type: Schema.Types.ObjectId, ref: "Teacher" }],
    title: String,
    city: String,
    address: {
      type: AddressSchema,
    },
    dates: [Date],
    hours: Number,
    shortDescription: String,
    longDescription: String,
    cost: Number,
    capacity: Number,
    enrolled: [{ type: Schema.Types.ObjectId, ref: "Member" }],
    active: Boolean, // allows members to enroll
  },
  { timestamps: true }
);

const TeacherSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: "User" },
    name: String,
    classes: [{ type: Schema.Types.ObjectId, ref: "Class" }],
    active: Boolean,
  },
  { timestamps: true }
);

const LanguageProficiencySchema = new Schema(
  {
    cefr: { type: String, required: true },
    pfLevel: {
      en: { type: String, required: true },
      es: { type: String, required: true },
    },
    simpleLevel: {
      en: { type: String, required: true },
      es: { type: String, required: true },
    },
    longDescription: {
      en: { type: String, required: true },
      es: { type: String, required: true },
    },
    selfAssessment: {
      en: { type: String, required: true },
      es: { type: String, required: true },
    },
    shortSelfAssessment: {
      en: { type: String, required: true },
      es: { type: String, required: true },
    },
  },
  { timestamps: true }
);

const SubscriptionSchema = new Schema(
  {
    name: String,
    type: String, // trial, paid, etc
    period: String, // monthly or yearly
    features: [String],
    description: String,
    activeFrom: Date,
    activeTo: Date,
    membersActive: [{ type: Schema.Types.ObjectId, ref: "Member" }],
    membersInactive: [{ type: Schema.Types.ObjectId, ref: "Member" }],
    subscriptionToTransferTo: {
      type: Schema.Types.ObjectId,
      ref: "Subscription",
    },
    subscriptionToTransferToAfter: { type: Number }, // number of days after which member/business transfers to new subscription
    price: Number,
    country: { type: Schema.Types.ObjectId, ref: "Country" },
  },
  { timestamps: true }
);

const BillingInfoSchema = new Schema(
  {
    type: String, // stripe, paypal, etc
    active: Boolean,
    customerId: String,
    subscriptionId: String,
    member: { type: Schema.Types.ObjectId, ref: "Member" },
  },
  { timestamps: true }
);

const PaymentSchema = new Schema(
  {
    chargeId: String,
    amount: Number,
    status: String, // pending, paid, failed, refunded
    description: String,
    member: {
      type: Schema.Types.ObjectId,
      ref: "Member",
    },
    billingInfo: { type: Schema.Types.ObjectId, ref: "BillingInfo" },
  },
  { timestamps: true }
);

const WaitlistSchema = new Schema({
  fname: { type: String, required: true },
  lname: { type: String, required: true },
  email: { type: String, required: true },
  language: { type: String, required: true },
});

const LaunchWaitlistSchema = new Schema(
  {
    email: { type: String, required: true },
  },
  { timestamps: true }
);

const VideosCollection = new Schema(
  {
    name: { type: String, required: true },
    desc: { type: String, required: true },
    imageId: String,
    imageKey: String,
    categories: [String],
    likedCount: {
      type: Number,
      default: 0,
    },
    videoCount: {
      type: Number,
      default: 0,
    },
    videoDuration: {
      type: Number,
      default: 0,
    },
    proficiencyLevel: {
      type: Schema.Types.ObjectId,
      ref: "LanguageProficiency",
    },
  },
  { timestamps: true }
);

const VideosCollectionMap = new Schema(
  {
    collectionId: { type: Schema.Types.ObjectId, ref: "VideoCollection" },
    videoId: { type: Schema.Types.ObjectId, ref: "Video" },
  },
  { timestamps: true }
);

const VideosCollectionLikesMap = new Schema(
  {
    likedBy: { type: Schema.Types.ObjectId, ref: "User" },
    collectionId: { type: Schema.Types.ObjectId, ref: "VideoCollection" },
  },
  { timestamps: true }
);

const MediaJobSchema = new Schema(
  {
    videoId: String,
    assetType: {
      type: String,
      enum: [ASSETS_TYPES.CC, ASSETS_TYPES.THUMBNAIL, ASSETS_TYPES.VIDEO],
    },
    status: {
      type: String,
      enum: [ASSETS_STATUS.FAILED, ASSETS_STATUS.READY, ASSETS_STATUS.STARTED],
    },
    errorMsg: String,
  },
  { timestamps: true }
);

const videoProgressSchema = new Schema(
  {
    videoId: { type: Schema.Types.ObjectId, ref: "Video", required: true },
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    state: {
      type: Number,
      enum: [
        PROGRESS_STATUS.COMPLETE,
        PROGRESS_STATUS.IN_PROGRESS,
        PROGRESS_STATUS.NOT_STARTED,
      ],
      required: true,
    },
    progress: Number,
  },
  { timestamps: true }
);

const videoCollectionProgressSchema = new Schema(
  {
    collectionId: {
      type: Schema.Types.ObjectId,
      ref: "VideoCollection",
      required: true,
    },
    userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
    state: {
      type: Number,
      enum: [
        PROGRESS_STATUS.COMPLETE,
        PROGRESS_STATUS.IN_PROGRESS,
        PROGRESS_STATUS.NOT_STARTED,
      ],
      required: true,
    },
    progress: Number,
  },
  { timestamps: true }
);

const EventOnSchema = new Schema(
  {
    startDate: Date,
    startTime: String,
    endDate: String,
    endTime: String,
    timezone: String,
    startDateTime: Date,
    endDateTime: Date,
    eventId: { type: Schema.Types.ObjectId, ref: "Event" },
  },
  { timestamps: true }
);

const EventSchema = new Schema(
  {
    title: String,
    description: String,
    // startDate: String,
    // startTime: String,
    // endDate: String,
    // endTime: String,
    // timezone: String,
    // startDateTime: Date,
    // endDateTime: Date,
    location: String,
    url: String,
    mode: {
      type: String,
      enum: [EVENT_MODE_TYPE.OFFLINE, EVENT_MODE_TYPE.ONLINE],
    },
    proficiencyLevel: {
      type: Schema.Types.ObjectId,
      ref: "LanguageProficiency",
    },
    imagesKeysAndIds: [
      {
        key: String,
        id: String,
      },
    ],
    eventImageId: String,
    eventImageKey: String,
    categories: [String],
    price: Number,
    currency: {
      type: String,
      enum: [CURRENCY_ENUM.MXN, CURRENCY_ENUM.USD],
    },
    maxNumberOfRegistrations: Number,
    targetLanguage: { type: Schema.Types.ObjectId, ref: "Language" },
    eventOn: [
      {
        type: Schema.Types.ObjectId,
        ref: "EventOn",
      },
    ],
  },
  { timestamps: true }
);

const PlanDetails = {
  price: Number,
  singlePrice: Number,
  discount: Number,
  duration: Number,
  planId: String,
};

const ClassesPricingSchema = {
  uniqueId: String,
  benefits: String,
  benefitsEs: String,
  descriptionEs: String,
  title: String,
  subtitle: String,
  description: String,
  isLimitedDeal: Boolean,
  isPopular: Boolean,
  type: {
    type: String,
    enum: [CLASSES_TYPE.IN_PERSON, CLASSES_TYPE.ONLINE],
  },
  durationType: {
    type: String,
    enum: [
      DURATION_TYPE.WEEK,
      DURATION_TYPE.HOUR,
      DURATION_TYPE.MINUTES,
      DURATION_TYPE.THIRTY_MINUTES,
      DURATION_TYPE.NINETY_MINUTES,
    ],
  },
  subType: {
    type: String,
    enum: [
      IN_PERSON_TYPE.GROUP,
      IN_PERSON_TYPE.PRIVATE,

      ONLINE_CLASSES_TYPE.QUICK_SESSIONS,
      ONLINE_CLASSES_TYPE.REGULAR_SESSIONS,
      ONLINE_CLASSES_TYPE.LONG_SESSIONS,
    ],
  },
  plans: [PlanDetails],
};

const UserPlan = {
  // price: Number,
  // singlePrice: Number,
  // discount: Number,
  // duration: Number,

  planId: String,
  startDate: Date,
  endDate: Date,
  planFor: {
    type: String,
    enum: [PLAN_FOR.MYSELF, PLAN_FOR.SOMEONE],
  },
  emailId: String,
  isDateEnabled: Boolean,
};

const UserMemberships = {
  planFor: {
    type: String,
    enum: [PLAN_FOR.MYSELF, PLAN_FOR.SOMEONE],
  },
  emailId: String,
};

const CartSchema = new Schema(
  {
    classesId: ClassesPricingSchema,
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    eventId: {
      type: Schema.Types.ObjectId,
      ref: "Event",
    },
    clubId: {
      type: Schema.Types.ObjectId,
      ref: "Club",
    },
    eventOn: {
      type: Schema.Types.ObjectId,
      ref: "EventOn",
    },
    plans: [UserPlan],
    memberships: [UserMemberships],
    // we make isCheckOut true in stripe webhook
    // only after payment is successful
    isCheckedOut: {
      default: false,
      type: Boolean,
    },
    // lets say suppose i havent added it into cart
    // but i am directly buying it , it will be true if i am directly buying it
    // in get/all carts we need to pass this in find({isBuying:false})
    isBuying: {
      default: false,
      type: Boolean,
    },
    transactionId: {
      type: Schema.Types.ObjectId,
      ref: "Transaction",
    },
  },
  { timestamps: true }
);

const TransactionSchema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: "User",
    },
    classesDetails: [
      {
        classInfo: ClassesPricingSchema,
        plans: [UserPlan],
      },
    ],
    clubsDetails: [
      {
        clubInfo: {
          type: Schema.Types.ObjectId,
          ref: "Club",
        },
        memberships: [UserMemberships],
        // as we will be allowing user to update club
        // lets say user has bought the club for 10$ and later we change it
        // to 20$ for user for our calculation we need to use below price and
        // currency we will later use this price and currency
        price: Number,
        currency: String,
      },
    ],
    eventIds: [
      {
        type: Schema.Types.ObjectId,
        ref: "Event",
      },
    ],
    // NOTE i am adding this additional field so that it wont break existing logic of fetching
    // in this we will be saving the price and currency by which we bought the event
    // as above we are just storing its id and later poipulating it which may show newer price and currency
    eventsPriceDetails: [
      {
        eventInfo: {
          type: Schema.Types.ObjectId,
          ref: "Event",
        },
        price: Number,
        currency: String,
        eventOn: {
          type: Schema.Types.ObjectId,
          ref: "EventOn",
        },
      },
    ],

    customProductsDetails: {
      price: Number,
      currency: String,
      customProductsInfo: {
        type: Schema.Types.ObjectId,
        ref: "CustomProduct",
      },
    },

    cartId: [
      {
        type: Schema.Types.ObjectId,
        ref: "Cart",
      },
    ],
    status: {
      type: String,
      enum: [
        PAYMENT_STATUS.FAILED,
        PAYMENT_STATUS.SUCCESS,
        PAYMENT_STATUS.INITIATED,
        PAYMENT_STATUS.EXPIRED,
        PAYMENT_STATUS.CANCELLED,
      ],
    },
    email: String,
    stripeCheckoutId: String,
    paymentIntentId: String,
    price: Number,
    discount: Number,
    finalAmount: Number,
    transactionDate: Date,
    mode: {
      type: String,
      enum: [
        PAYMENT_MODE.PAYMENT,
        PAYMENT_MODE.SUBSCRIPTION,
        PAYMENT_MODE.BANK_TRANSFER,
        PAYMENT_MODE.CASH,
      ],
    },
    stripeSubscriptionId: String,
    subscriptionEndDate: Date,
    unsubscribe: {
      value: {
        type: Boolean,
        default: false,
      },
      date: Date,
    },
    invoices: [String],
    isGift: { type: Boolean, default: false },
    giftedBy: { type: Schema.Types.ObjectId, ref: "User" },
    giftTransactionId: { type: Schema.Types.ObjectId, ref: "Transaction" },
  },
  { timestamps: true }
);

const ClassDetailsSchema = new Schema(ClassesPricingSchema, {
  timestamps: true,
});

const ClubPriceDetailsSchema = new Schema(
  {
    priceId: { type: String, required: true },
    amount: { type: Number, required: true },
    currency: {
      type: String,
      enum: [CURRENCY_ENUM.MXN, CURRENCY_ENUM.USD],
      required: true,
    },
    clubId: {
      type: Schema.Types.ObjectId,
      ref: "Club",
    },
  },
  {
    timestamps: true,
  }
);

const ClubSchema = new Schema(
  {
    title: String,
    about: String,
    price: Number,
    theme: String,
    currency: {
      type: String,
      enum: [CURRENCY_ENUM.MXN, CURRENCY_ENUM.USD],
    },
    proficiencyLevel: {
      type: Schema.Types.ObjectId,
      ref: "LanguageProficiency",
    },
    teachers: [
      {
        type: Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    categories: [String],
    highlights: [String],
    imagesKeysAndIds: [
      {
        key: String,
        id: String,
      },
    ],
    stripePriceId: String,
    historicalPrices: [
      {
        type: Schema.Types.ObjectId,
        ref: "ClubPriceDetails",
      },
    ],
    stripeProductId: String,
    noOfStudentsEnrolled: {
      type: Number,
      default: 0,
    },
    // we will store the number of students rated it
    // NOTE we will only increment the count....we will should
    // maintain separate schema for rating details where we will store
    // userId and clubId
    ratings: {
      totalRatings: {
        type: Number,
        default: 0,
      },
      value: {
        type: Number,
        default: 0,
      },
    },
    targetLanguage: {
      type: Schema.Types.ObjectId,
      ref: "Language",
    },
  },
  { timestamps: true }
);

const ScheduleSchema = new Schema(
  {
    type: {
      type: String,
      enum: [CLASSES_TYPE.IN_PERSON, CLASSES_TYPE.ONLINE],
    },
    subType: {
      type: String,
      enum: [
        IN_PERSON_TYPE.GROUP,
        IN_PERSON_TYPE.PRIVATE,

        ONLINE_CLASSES_TYPE.QUICK_SESSIONS,
        ONLINE_CLASSES_TYPE.REGULAR_SESSIONS,
        ONLINE_CLASSES_TYPE.LONG_SESSIONS,
      ],
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    language: {
      type: Schema.Types.ObjectId,
      ref: "Language",
    },
    teachers: [
      {
        location: String,
        level: {
          type: String,
          enum: [
            LEVEL_TYPES.ADVANCED,
            LEVEL_TYPES.BEGINNER,
            LEVEL_TYPES.INTERMEDIATE,
          ],
        },
        teacherId: {
          type: Schema.Types.ObjectId,
          ref: "User",
        },
      },
    ],
    students: [
      {
        transaction: {
          type: Schema.Types.ObjectId,
          ref: "Transaction",
        },
        userId: {
          type: Schema.Types.ObjectId,
          ref: "User",
        },
        level: {
          type: String,
          enum: [
            LEVEL_TYPES.ADVANCED,
            LEVEL_TYPES.BEGINNER,
            LEVEL_TYPES.INTERMEDIATE,
          ],
        },
        selectedPlan: UserPlan,
        scheduledAt: Date,
      },
    ],
  },
  {
    timestamps: true,
  }
);

const VisitorSchema = new Schema(
  {
    userId: { type: Schema.Types.ObjectId, ref: "User" },
    latitude: Number,
    longitude: Number,
  },
  { timestamps: true }
);

const CustomProductSchema = new Schema(
  {
    title: {
      type: String,
    },
    description: {
      type: String,
    },
    internalNotes: {
      type: String,
    },
    currency: {
      type: String,
      enum: [CURRENCY_ENUM.MXN, CURRENCY_ENUM.USD],
    },
    targetLanguage: {
      type: Schema.Types.ObjectId,
      ref: "Language",
    },
    price: {
      type: Number,
    },
    startDate: {
      type: Date,
    },
    expirationDate: { type: Date },
    timezone: {
      type: String,
    },
    instructor: [{ type: Schema.Types.ObjectId, ref: "User" }],
    code: { type: String, required: true },
    transactionId: {
      type: Schema.Types.ObjectId,
      ref: "Transaction",
    },
    productFor: { type: Schema.Types.ObjectId, ref: "User" },
    isExpired: { type: Boolean, default: false },
  },
  {
    timestamps: true,
  }
);
/**
 * Normally this would just be
 * export const Role = mongoose.model<RoleType>("Role", RoleSchema);
 * but because of the hot reloading of Next.js, we need to check if the model
 * already exists, otherwise we get an error.
 */
export const CustomProduct =
  (mongoose.models.CustomProduct as Model<CustomProductType>) ||
  mongoose.model<CustomProductType>("CustomProduct", CustomProductSchema);
export const Visitor =
  (mongoose.models.Visitor as Model<VisitorType>) ||
  mongoose.model<VisitorType>("Visitor", VisitorSchema);
export const ClubPriceDetails =
  (mongoose.models.ClubPriceDetails as Model<VisitorType>) ||
  mongoose.model<VisitorType>("ClubPriceDetails", ClubPriceDetailsSchema);
export const EventOn =
  (mongoose.models.EventOn as Model<EventOnType>) ||
  mongoose.model<EventOnType>("EventOn", EventOnSchema);
export const ClassDetails =
  (mongoose.models.ClassDetails as Model<ClassesPricingType>) ||
  mongoose.model<ClassesPricingType>("ClassDetails", ClassDetailsSchema);
export const Schedule =
  (mongoose.models.Schedule as Model<ScheduleType>) ||
  mongoose.model<ScheduleType>("Schedule", ScheduleSchema);
export const Transaction =
  (mongoose.models.Transaction as Model<TransactionType>) ||
  mongoose.model<TransactionType>("Transaction", TransactionSchema);
export const Club =
  (mongoose.models.Club as Model<ClubType>) ||
  mongoose.model<ClubType>("Club", ClubSchema);
export const Cart =
  (mongoose.models.Cart as Model<CartType>) ||
  mongoose.model<CartType>("Cart", CartSchema);
export const VideoProgress =
  (mongoose.models.VideoProgress as Model<VideoProgressSchemaType>) ||
  mongoose.model<VideoProgressSchemaType>("VideoProgress", videoProgressSchema);
export const Event =
  (mongoose.models.Event as Model<EventSchemaType>) ||
  mongoose.model<EventSchemaType>("Event", EventSchema);
export const VideoCollectionProgress =
  (mongoose.models
    .VideoCollectionProgress as Model<VideoCollectionProgressSchemaType>) ||
  mongoose.model<VideoCollectionProgressSchemaType>(
    "VideoCollectionProgress",
    videoCollectionProgressSchema
  );
export const Role =
  (mongoose.models.Role as Model<RoleType>) ||
  mongoose.model<RoleType>("Role", RoleSchema);
export const User =
  (mongoose.models.User as Model<UserType>) ||
  mongoose.model<UserType>("User", UserSchema);
export const Goal =
  (mongoose.models.Goal as Model<GoalType>) ||
  mongoose.model<GoalType>("Goal", GoalSchema);
export const Interest =
  (mongoose.models.Interest as Model<InterestType>) ||
  mongoose.model<InterestType>("Interest", InterestSchema);
export const Member =
  (mongoose.models.Member as Model<MemberType>) ||
  mongoose.model<MemberType>("Member", MemberSchema);
export const Video =
  (mongoose.models.Video as Model<VideoType>) ||
  mongoose.model<VideoType>("Video", VideoSchema);
export const MediaJob =
  (mongoose.models.MediaJob as Model<MediaJobType>) ||
  mongoose.model<MediaJobType>("MediaJob", MediaJobSchema);
export const Business =
  (mongoose.models.Business as Model<BusinessType>) ||
  mongoose.model<BusinessType>("Business", BusinessSchema);
export const Classes =
  (mongoose.models.Classes as Model<ClassesType>) ||
  mongoose.model<ClassesType>("Classes", ClassSchema); // Class is protected sometimes, just to differentiate, plural
export const Teacher =
  (mongoose.models.Teacher as Model<TeacherType>) ||
  mongoose.model<TeacherType>("Teacher", TeacherSchema);
export const LanguageProficiency =
  (mongoose.models.LanguageProficiency as Model<LanguageProficiencyType>) ||
  mongoose.model<LanguageProficiencyType>(
    "LanguageProficiency",
    LanguageProficiencySchema
  );
export const Payment =
  (mongoose.models.Payment as Model<PaymentType>) ||
  mongoose.model<PaymentType>("Payment", PaymentSchema);
export const Country =
  (mongoose.models.Country as Model<CountryType>) ||
  mongoose.model<CountryType>("Country", CountrySchema);
export const Subscription =
  (mongoose.models.Subscription as Model<SubscriptionType>) ||
  mongoose.model<SubscriptionType>("Subscription", SubscriptionSchema);
export const BillingInfo =
  (mongoose.models.BillingInfo as Model<BillingInfoType>) ||
  mongoose.model<BillingInfoType>("BillingInfo", BillingInfoSchema);
export const VideoCategory =
  (mongoose.models.VideoCategory as Model<VideoCategoryType>) ||
  mongoose.model<VideoCategoryType>("VideoCategory", VideoCategorySchema);
export const Password =
  (mongoose.models.Password as Model<PasswordType>) ||
  mongoose.model<PasswordType>("Password", PasswordSchema);
export const Language =
  (mongoose.models.Language as Model<LanguageType>) ||
  mongoose.model<LanguageType>("Language", LanguageSchema);
export const UserLanguage =
  (mongoose.models.UserLanguage as Model<UserLanguageType>) ||
  mongoose.model<UserLanguageType>("UserLanguage", UserLanguageSchema);
export const Waitlist =
  (mongoose.models.Waitlist as Model<WaitlistType>) ||
  mongoose.model<WaitlistType>("Waitlist", WaitlistSchema);
export const LaunchWaitlist =
  (mongoose.models.LaunchWaitlist as Model<LaunchWaitlistType>) ||
  mongoose.model<LaunchWaitlistType>("LaunchWaitlist", LaunchWaitlistSchema);
export const Category =
  (mongoose.models.Category as Model<CategoryType>) ||
  mongoose.model<CategoryType>("Category", CategorySchema);
export const VideoLike =
  (mongoose.models.VideoLike as Model<VideoLikeType>) ||
  mongoose.model<VideoLikeType>("VideoLike", VideoLikeSchema);
export const VideoCollection =
  (mongoose.models.VideoCollection as Model<VideosCollectionType>) ||
  mongoose.model<VideosCollectionType>("VideoCollection", VideosCollection);
export const VideoCollectionMap =
  (mongoose.models.VideoCollectionMap as Model<VideosCollectionMapType>) ||
  mongoose.model<VideosCollectionMapType>(
    "VideoCollectionMap",
    VideosCollectionMap
  );
export const VideoCollectionLikesMap =
  (mongoose.models
    .VideoCollectionLikesMap as Model<VideoCollectionLikesMapType>) ||
  mongoose.model<VideoCollectionLikesMapType>(
    "VideoCollectionLikesMap",
    VideosCollectionLikesMap
  );
