{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "src/pages/sign-up/index.tsz", "src/components/Profile/ProfileComponent.js", "src/components/Profile/GoalsSection.tsx", "src/pages/create/event/create/index.js", "src/components/CheckoutModal/CheckoutClass/PlanSelector.tsx", "src/components/header/Header.js"], "exclude": ["node_modules"]}